<template>
  <view class="area-approve">
    <view class="top-filter">
      <view class="filter-item" @click="approveType">
        <text class="title" v-if="!state.params.applyType">申请类型</text>
        <text class="title" v-if="state.params.applyType" style="color: #2551f2">{{ getApproveType() }}</text>
        <IconFont name="triangle-down" color="#2551F2" size="8px" v-if="state.params.applyType"></IconFont>
        <IconFont name="triangle-down" color="#4e595e" size="8px" v-if="!state.params.applyType"></IconFont>
      </view>
      <view class="filter-item" @click="approveStatus">
        <text class="title" v-if="!state.params.status">审批状态</text>

        <text class="title" v-if="state.params.status" style="color: #2551f2">{{
            getApproveStatus()
          }}</text>
        <IconFont name="triangle-down" color="#2551F2" size="8px" v-if="state.params.status"></IconFont>
        <IconFont name="triangle-down" color="#4e595e" size="8px" v-if="!state.params.status"></IconFont>
      </view>
    </view>
    <scroll-view style="height: 528px" :scroll-y="true" :scroll-top="state.scrollTop" @scrolltolower="onScrollToLower">
      <view style="
          position: absolute;
          top: 28%;
          left: 50%;
          transform: translate(-50%);
        " v-if="!state.insList.length && !scrollMyFlag">
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px">暂无数据</view>
      </view>
      <div class="px-[10px]">
        <view v-for="item in state.insList" :key="item.processId" class="relative rounded-[8px] bg-white p-[12px] mb-[8px]" @tap="handleToApproveDetail(item)">
          <div class="absolute top-0 right-0">
            <ProcessStatusTag :instanceState="item.instanceState" />
          </div>
          <div class="header flex">
            <div class="name text-[18px] text-[#1D212B]">{{ item.processName }}</div>
          </div>
          <div class="flex justify-between items-center mt-[10px]">
            <div class="flex items-center">
              <div class="w-[25px] h-[25px] rounded-[50%] bg-[#92A8F8] text-center leading-[25px] text-[12px] text-white">{{ item?.startUser?.name?.slice(-1) }}</div>
              <div class="text-[14px] text-[#1D212B] pl-[8px]">{{ item?.startUser?.name }}</div>
            </div>
            <div class="flex">
              <div class="text-[#4E595E] text-[14px]">{{ item.taskStartTime }}</div>
            </div>
          </div>
        </view>
      </div>
      <view v-if="state.insList.length > 0 && state.insList.length == state.total"
            style="color: #869199; font-size: 16px; text-align: center">已全部加载完毕</view>
      <PopupRadio ref="popupRadioRef" @radio-confirm="radioConfirm" />
    </scroll-view>
  </view>
</template>
<script setup>
import Taro, {useDidShow} from "@tarojs/taro";
import {
  defineEmits,
  defineProps,
  ref,
  reactive,
  defineExpose,
  computed,
} from "vue";

import { todoTaskApi, doneListApi, ccTaskListApi, taskApplyListApi } from './../../api/bpm.js';
import ProcessStatusTag from './processStatusTag.vue';

import arrowRight from "./../../assets/images/arrow-right.png";
// import { approveList } from "../../../api/area.js";
import { IconFont } from "@nutui/icons-vue-taro";
import PopupRadio from "./../../components/popupRadio/popupRadio.vue";
import noData from "./../../assets/images/no-data.png";
const emit = defineEmits(["change-filter"]);
const scrollMyFlag = ref(false);
const initHrInfo = Taro.getStorageSync("initHrInfo") || {};
const applyFormTypeList = []
const popupRadioRef = ref(null);

const state = reactive({
  scrollTop: 0,
  search: [],
  params: {
    pageNum: 1,
    pageSize: 99,
    applyType: "讲者新增审批",
    status: "1",
    insName: "",
  },
  total: 0,
  pcdvalue: "",
  insList: [],
  list: [
    { text: "待审批", key: "1", active: false },
    { text: "已审批", key: "2", active: false },
    { text: "我发起", key: "3", active: false },
  ],
  show1: false,
  statusList: [
    {
      dictLabel: '待办',
      dictValue: '1',
    },
    {
      dictLabel: '已办',
      dictValue: '2',
    },
    {
      dictLabel: '抄送我',
      dictValue: '3',
    },
    {
      dictLabel: '已发送',
      dictValue: '4',
    }
  ],
  statusType: [
    {
      dictLabel: '讲者新增审批',
      // dictValue: 'gs_speaker_mdm_apply',
      dictValue: '讲者新增审批',
    },

    {
      dictLabel: '讲者更新审批',
      // dictValue: 'gs_speaker_update_mdm_apply',
      dictValue: '讲者更新审批',
    },

  ],
  topType: "",
});


const getList = () => {
  scrollMyFlag.value = true;
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  let api = () => {};
  if(state.params.status === '1') {
    api = todoTaskApi
  }else if(state.params.status === '2') {
    api = doneListApi
  }else if(state.params.status === '3') {
    api = ccTaskListApi
  }else if(state.params.status === '4') {
    api = taskApplyListApi
  }

  state.params.processName = state.params.applyType;
  api(state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.total;
        if (!state.insList.length) {
          console.log("res.data.rows", res.rows);
          state.insList = res.rows || [];
        } else {
          state.insList = [...state.insList, ...res.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = false;
      } else {
        Taro.hideLoading();
      }
    })
    .catch((e) => {
      scrollMyFlag.value = false;
      Taro.hideLoading();
      console.log(e, 'eee')
    });
};


const topSearch = (search) => {
  state.params.processName = search;
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getList();
};

const approveFilters = () => {
  const info = {
    startDate: state.params.startDate,
    endDate: state.params.endDate,
  };
  Taro.navigateTo({
    url: `/pages/pharmacy/approve/approveFiltes?info=${JSON.stringify(info)}`,
  });
};
const onScrollToLower = () => {
  if (!scrollMyFlag.value && state.insList.length < state.total) {
    state.params.pageNum++;
    getList();
  }
};

const statusText = (i) => {
  if(!i) return
  const list= state.statusList.filter(item => item.dictValue === i.status) || [];
  console
  return list.length ? list[0].dictLabel : ''
};
const gotoInst = (item) => {
  if (item.status === '-1') {
    if( item.rejectType === '1') {
      window.location.href = `${process.env.TARO_APP_APPROVE_URL}/ae/automation/inbox?usingInstanceId=${item.aeFlowId}&hideAction=false&hideForm=false&hideProcess=false&hideTitle=false`;
    } else {
      return
    }
  } else {
    const list = state.statusList.filter(v => v.dictValue ===item.status) || []
    if(list[0].jump === 'true') {
      return window.location.href = `${process.env.TARO_APP_APPROVE_URL}/ae/automation/inbox?usingInstanceId=${item.aeFlowId}&hideAction=false&hideForm=false&hideProcess=false&hideTitle=false`;
    } else {
      return false
    }
  }

};

const approveStatus = () => {
  state.topType = "status";
  popupRadioRef.value.open(state.statusList, state.params.status);
};
const approveType = () => {
  state.topType = "type";
  popupRadioRef.value.open(state.statusType, state.params.applyType);
};
const radioConfirm = (val) => {

  if (state.topType === "type") {
    state.params.applyType = val;
    // state.params.status = "";
    // const list = state.statusType.filter(
    //   (item) => item.dictValue === state.params.applyType
    // )[0].remark;
    // state.statusList = JSON.parse(list).data;

  } else {
    state.params.status = val;
  }
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getList();
};

const getApproveType = () => {
  const list = state.statusType.filter(
    (item) => item.dictValue === state.params.applyType
  );
  return (list[0] && list[0].dictLabel) || "";
};

const getApproveTypeItem = (type) => {
  const list = state.statusType.filter((item) => item.dictValue === type);
  return (list[0] && list[0].dictLabel) || "";
};
const getApproveStatus = () => {
  if (!state.params.status && state.statusList.length) return;
  const list = state.statusList.filter(
    (item) => item.dictValue === state.params.status
  );
  return (list[0] && list[0].dictLabel) || "";
};

const approveDetail = (item) => {

  if (item.status === '-1') {
    if( item.rejectType === '1') {
      return true
    } else {
      return false
    }
  } else {
    const list = state.statusList.filter(v => v.dictValue ===item.status) || []
    if(list[0].jump === 'true') {
      return true
    } else {
      return false
    }
  }
}

const initList = () => {
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getList();
};

const statusColor = (i) => {
  if(!i) return
  const list= state.statusList.filter(item => item.dictValue === i.status) || [];
  return list.length ? list[0].color : ''
}

const statusBgcolor = (i) => {
  if(!i) return
  const list= state.statusList.filter(item => item.dictValue === i.status) || [];
  return list.length ? list[0].bgcColor : ''
}

const handleToApproveDetail = (item) => {
  Taro.navigateTo({
    url: `/pages/speaker/approve_detail/index?processId=${item.processId}&instanceId=${item.instanceId}`
  })
}
defineExpose({
  topSearch,
  approveFilters,
  initList,
});

useDidShow(() => {
  setTimeout(() => {
    initList()
  }, 1000)
})
</script>
<style lang="scss">
.area-approve {
  padding: 0px 0px;
  width: 100vw;
  position: relative;
  // height: 630px;
  font-family: PingFang SC, PingFang SC;

  .top-filter {
    padding: 8px 16px;
    display: flex;
    align-items: center;

    .filter-item {
      height: 27px;
      padding: 0px 9px;
      border-radius: 16px;
      background: #fff;
      font-size: 14px;
      line-height: 27px;
      margin-right: 8px;
      display: inline-block;

      color: #4e595e;
      border: 1px solid #fff;

      .title {
        margin-right: 4px;
      }
    }
  }

  .content {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    margin-left: 12px;
    margin-right: 12px;
    position: relative;

    .content-item {
      padding: 16px 16px 12px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item-top {
        font-size: 18px;
        margin-bottom: -5px;
        font-weight: 500;
      }
    }

    .item-time {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #869199;
    }

    .item-type {
      font-size: 14px;
      color: #869199;
    }
  }

  .approve {
    color: #ff7d00;
    background: #fff3e8;
  }

  .agree {
    color: #00b578;
    background: #e6f8f2;
  }

  .reject {
    color: #f32f29;
    background: #feeaea;
  }

  .back {
    color: #4e595e;
    background: #f3f4f5;
  }

  .approvalStatus {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 12px;
    text-align: center;
    height: 20px;
    padding: 0 7px;
    line-height: 20px;
    border-radius: 2px 8px 0 10px;
  }

  .nut-divider.nut-divider-vertical {
    top: 0;
  }

  .add-pharmacy {
    position: absolute;
    right: 16px;
    top: 53%;
  }

  .nut-overlay {
    position: absolute !important;
  }

  .nut-popup {
    position: absolute !important;
  }
}
</style>
