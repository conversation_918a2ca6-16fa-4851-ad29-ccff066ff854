<template>
  <view class="pharmacyTabDetail">
    <view class="bcfff">
      <view style="font-size: 16px; color: #1d212b">基本信息</view>

      <DetailItem label="机构类型" :value="state.info.dsType" />
      <DetailItem label="区域类型" :value="state.info.dsAreaType" />
      <DetailItem label="是否医保定点" :value="state.info.fixedPoint" />
      <DetailItem
        label="省市区"
        :value="`${state.info.province || ''} ${state.info.city || ''} ${
          state.info.district || ''
        }`"
      />
      <DetailItem label="地址" :value="state.info.address" />
      <DetailItem label="法人" :value="state.info.legalPerson" />
      <DetailItem
        label="统一社会信用代码"
        :value="state.info.socialCreditCode"
      />
      <DetailItem label="经营期限" :value="state.info.businessTerm" />
      <DetailItem label="上级药店编码" :value="state.info.superiorDsMdmCode" />
      <DetailItem label="上级药店" :value="state.info.superiorDsName" />
    </view>
    <view class="bcfff" style="margin-top: 8px">
      <view style="font-size: 16px; color: #1d212b">经营信息</view>

      <DetailItem label="药店性质" :value="state.info.dsNature" />
      <DetailItem label="支付类型" :value="state.info.economicType" />
      <DetailItem label="经营范围" :value="state.info.businessScope" />
      <DetailItem label="执业药师数量" :value="state.info.docNum" />
      <DetailItem label="营业时间" :value="state.info.openingTime" />
      <DetailItem
        label="周六日(含节假日)营业时间"
        :value="state.info.weekOpeningTime"
      />

      <DetailItem label="药店电话" :value="state.info.dsContactTel" />
      <DetailItem label="专用药师手机号" :value="state.info.docContactTel" />
      <DetailItem
        :isImageUrl="true"
        label="照片(营业执照，经营许可证，门头照)"
        :value="state.info.ossUrlList"
      />
    </view>
  </view>
</template>
<script setup>
import { View } from "@tarojs/components";
import { reactive } from "vue";
import DetailItem from "../../../pages/components/detailItem/index.vue";
const state = reactive({
  info: {
    fileList: [],
  },
});

const setInfo = (info) => {
  state.info = info;
};

defineExpose({
  setInfo,
});
</script>
<style lang="scss">
.pharmacyTabDetail::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}
.pharmacyTabDetail {
  font-size: 14px;
  color: #869199;
  // background: #fff;
  overflow: hidden;
  overflow-y: scroll;
  .bcfff {
    background: #fff;
    padding: 12px 16px;
    border-radius: 8px;
    margin: 12px;
  }
  .title {
    font-size: 20px;
    color: #1d212b;
  }
  .cusDetail-item {
    margin-top: 10px;
    .cusDetail-item-title {
      display: flex;
      align-items: center;
      padding: 6px 0;
      .label {
        width: 30%;
      }
      .value {
        color: #1d212b;
        width: 70%;
        display: block;
      }
    }
  }
}
</style>
