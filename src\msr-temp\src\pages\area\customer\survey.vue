<template>
  <view class="survey-container">
    <nut-cell-group :title="survey.name">
      <view
        class="question-item"
        v-for="(question, index) in questions"
        :key="index"
      >
        <view class="question-title">{{ question.title }}</view>
        <template v-if="question.type === 'single'">
          <nut-radio-group v-model="question.answer" :disabled="hideSubmit">
            <nut-radio
              v-for="(option, optIndex) in question.options"
              :key="optIndex"
              :label="optIndex"
              :disabled="hideSubmit"
            >
              {{ option.content }}
            </nut-radio>
          </nut-radio-group>
        </template>
        <template v-else-if="question.type === 'numeric'">
          <nut-input
            v-model="question.answer"
            type="number"
            :disabled="hideSubmit"
            placeholder="请输入数值"
            clearable
          />
        </template>
        <template v-else-if="question.type === 'text'">
          <nut-textarea
            v-model="question.answer"
            :disabled="hideSubmit"
            placeholder="请输入文本"
            autosize
            clearable
            :rows="5"
          />
        </template>
      </view>
    </nut-cell-group>
    <view v-if="hideSubmit" class="text-red-500 text-[12px] pt-[4px] pl-[12px]">问卷为一次性提交，提交后仅可查看</view>
    <view class="submit-section" v-if="!hideSubmit">
      <nut-button
        type="primary"
        block
        @click="handleSubmit"
        :disabled="!isFormValid"
        :loading="loading"
      >
        提交问卷
      </nut-button>
    </view>

  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import Taro, { useRouter } from "@tarojs/taro";
import {
  addSurveyApi,
  surveyDetailApi,
  addSurveyApi048
} from "@/msr-temp/src/api/customerAPI.js";
const detailInfo = Taro.getStorageSync("detailInfo");
const initUserInfo = Taro.getStorageSync("initUserInfo");
const customerDetail = Taro.getStorageSync("customerDetail");
const router = useRouter();

const survey = ref({});
const hideSubmit = ref(false);
const loading = ref(false);
const questions = ref([]);

const isFormValid = computed(() => {
  return questions.value?.every((q) => {
    if (q.type === "single") return q.answer !== undefined;
    if (q.type === "numeric") return q.answer !== undefined && q.answer !== "";
    if (q.type === "text") return q.answer !== undefined && q.answer !== "";
    return false;
  });
});

const handleSubmit = async () => {
  if (!isFormValid.value) {
    Taro.showToast({
      title: "请完成所有题目",
      icon: "none",
    });
    return;
  }

  loading.value = true;
  try {
    const submitData = {
      questions: JSON.stringify(questions.value),
      surveyId: survey.value.id,
      productCode: router.params.productCode,
      productName: router.params.productName,
      scoreLevel: survey.value.scoreLevel,
      deptName: customerDetail.deptName || decodeURIComponent(router.params.deptName),
      customerCode: customerDetail.customerCode || router.params.customerCode,
      customerName: customerDetail.customerName || decodeURIComponent(router.params.customerName),
      insCode: customerDetail.insCode || router.params.insCode,
      insName: customerDetail.insName || decodeURIComponent(router.params.insName),
    };

    if(router.params.type === '048') {
      await addSurveyApi048(submitData);
    }else {
      await addSurveyApi(submitData);
    }
    Taro.showToast({
      title: "提交成功",
      icon: "success",
    });
    Taro.navigateBack({
      delta: 1,
    });
  } catch (error) {
    Taro.showToast({
      title: "提交失败",
      icon: "error",
    });
  } finally {
    loading.value = false;
  }
};
const initHrInfo = Taro.getStorageSync("initHrInfo");

const getDetail = async () => {
  let res = null;
  if(router.params.type === '048') {
    console.log(router.params.customerCode, 'router.params.productCode');
    res = await surveyDetailApi({
      surveyId: router.params.surveyId,
      customerCode: Array.isArray(router.params.customerCode) ? router.params.customerCode[0] : router.params.customerCode,
      productCode: router.params.productCode,
      pageNum: 1,
      pageSize: 10,
    });
  }else {
    res = await surveyDetailApi({
      surveyId: router.params.surveyId,
      createBy: initUserInfo.user?.userId,
      pageNum: 1,
      pageSize: 10,
    });

  }
  const _detail = res?.rows?.[0] || {};
  if (_detail.questions) {
    questions.value = JSON.parse(_detail.questions);
    hideSubmit.value = true;
  }
};

onMounted(() => {
  survey.value = Taro.getStorageSync("_survey");
  questions.value =
    Taro.getStorageSync("_survey")?.questions &&
    JSON.parse(Taro.getStorageSync("_survey")?.questions);
  getDetail();
});
</script>

<style lang="scss">
.survey-container {
  padding-top: 5px;
  background: #f7f8fa;
  min-height: 100vh;

  .question-item {
    margin-bottom: 8px;
    background: #fff;
    padding: 12px 16px 0 16px;
    border-radius: 6px;
  }

  .question-title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    line-height: 1.4;
  }

  :deep(.nut-radio) {
    margin-bottom: 8px;
    display: block;

    .nut-radio__label {
      font-size: 14px;
      line-height: 1.4;
    }
  }

  .submit-section {
    margin-top: 24px;
    padding: 0 16px;
    margin-bottom: 24px;
  }
}
.nut-cell-group__title {
  margin-top: 0px !important;
  font-size: 16px !important;
  padding: 0 16px !important;
}
</style>
