<template>
  <nut-cascader
    v-model:visible="visible"
    v-model="state.cityValue"
    title="选择地址"
    text-key="value"
    value-key="code"
    children-key="children"
    :options="state.type ? district : districtList"
    @change="confirmCity"
  ></nut-cascader>
</template>
<script setup>
import { reactive, ref, defineEmits } from "vue";
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
const visible = ref(false);
const popupRef = ref(null);
const districtList = Taro.getStorageSync("districtList");
const district = Taro.getStorageSync("district");
const emit = defineEmits(["district-confirm"]);
const state = reactive({
  cityValue: [],
  type: "",
});
const confirmCity = (selectedValue, selectedOptions) => {
  console.log("selectedValue, selectedOptions", selectedValue, selectedOptions);
  const valueList = selectedOptions
    .map((val) => val.text)
    .filter((item) => item !== "全部")
    .join("/");

  const codeList = selectedValue;
  console.log("valueList", valueList, codeList);
  emit("district-confirm", valueList, codeList);
};

const open = (list, type) => {
  state.type = type;
  if (list.length) {
    state.cityValue = list;
  }
  visible.value = true;
};

defineExpose({
  open,
});
</script>
<style lang="less" scoped></style>
