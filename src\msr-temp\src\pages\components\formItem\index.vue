<template>
  <nut-form-item
    :class="{ formItem: true, 'non-border': props.type == 'textarea' }"
    :label="props.label"
    :prop="props.propValue"
    :label-width="props.labelWidth"
    :required="props.required"
    :rules="props.rules"
  >
    <!--  uploader上传-->
    <template v-if="props.type == 'uploader'">
      <nut-uploader
        url=""
        :before-upload="beforeXhrUpload"
        accept="image/*"
        v-model:file-list="uploaderModelComputer"
        :maximum="props.uploaderOption.maximum"
        :multiple="props.uploaderOption.multiple"
      >
      </nut-uploader>
    </template>
    <!--  高德地图-->
    <!--  switch开关-->
    <template v-if="props.type == 'switch'">
      <nut-switch v-model="model"></nut-switch>
    </template>
    <!--  ellipsis多行显示-->
    <template v-if="props.type == 'ellipsis'">
      <div class="ellipsisBox">
        <nut-ellipsis
          style="flex: 1"
          :content="model"
          direction="end"
          rows="5"
        ></nut-ellipsis>
        <!-- <img :src="arrow" class="arrowIcon" /> -->
        <div class="arrowIcon"></div>
      </div>
    </template>
    <!--  input or city-->
    <template
      v-if="
        props.type == 'input' ||
        props.type == 'city' ||
        props.type == 'gdmap' ||
        props.type == 'postCode'
      "
    >
      <div @click="selectClick" class="ellipsisBox" v-if="needP && model">
        <nut-ellipsis
          style="flex: 1"
          :content="model"
          direction="end"
          rows="5"
        ></nut-ellipsis>
        <img :src="arrow" class="arrowIcon" />
      </div>
      <nut-input
        v-else
        v-model="model"
        :readonly="props.readonly || needP"
        input-align="right"
        :placeholder="props.placeholder"
        :border="false"
        clearable
        @click="selectClick"
        :max-length="100"
      >
        <template #right>
          <template v-if="!needP">
            <img
              v-if="props.type == 'gdmap'"
              :src="location_ins"
              class="location_ins"
            />
            <div v-else class="arrowIcon"></div>
          </template>
          <template v-else>
            <img :src="arrow" class="arrowIcon" />
          </template>
        </template>
      </nut-input>
    </template>
  </nut-form-item>
  <!-- textarea -->
  <template v-if="props.type == 'textarea'">
    <nut-textarea
      class="form-textare"
      v-model="model"
      limit-show
      max-length="100"
    />
  </template>
  <!-- 单选，可搜索 -->
  <PopupRadio
    v-if="needP && props.type != 'city'"
    ref="popupRadioRef"
    @radio-confirm="radioConfirm"
    @search="search"
    :needSearch="props.needSearch"
  />
  <!-- 地址 -->
  <DistrictPopup
    v-if="needP && props.type == 'city'"
    ref="districtPopupRef"
    @district-confirm="districtConfirm"
  />
  <UserPopup ref="userPopupRef" @select-user="selectUser" />
</template>
<script setup>
import { computed, defineProps, ref } from "vue";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";

import DistrictPopup from "../../../pages/components/districtPopup/index";
import PopupRadio from "../../../pages/components/popupRadio/popupRadio";
import arrow from "../../../images/arrow.png";
import location_ins from "../../../images/location_ins.png";
import UserPopup from "../../../pages/area/components/userPopup.vue";

const emit = defineEmits([
  "search",
  "current:radio",
  "current:city",
  "before:Upload",
]);

const props = defineProps({
  popupRadioDisabledCallBack: {
    type: Function,
    default: () => {},
  },
  popupRadioDisabled: {
    type: Boolean,
    default: true,
  },

  uploaderOption: {
    type: Object,
    default: false,
    default: {},
  },
  radionOption: {
    type: Array,
    default: false,
    default: [],
  },
  type: {
    type: String,
    default: "input",
  },
  needSearch: {
    type: Boolean,
    default: false,
  },
  popupOption: {
    type: Array,
    required: false,
  },
  placeholder: {
    type: String,
    default: "请输入",
  },
  needPopup: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
  propValue: {
    type: String,
    default: "",
  },
  label: {
    type: String,
    default: "",
  },
  labelWidth: {
    type: String,
    default: "100",
  },
  rules: {
    type: Array,
    required: false,
  },
  loadingBrfore: {
    type: Boolean,
    default: false,
  },
});
const optionCurrent = ref([]);
const model = defineModel();
model.value = "";
const popupRadioRef = ref(null);
const districtPopupRef = ref(null);
const userPopupRef = ref(null);
const needP = computed(() => props.needPopup || props.needSearch);
const districtConfirm = (value, codeList) => {
  model.value = value;
  emit("current:city", value, codeList);
};
const selectUser = (item) => {
  emit("current:deptName", item);
};
const uploaderModelComputer = computed({
  get() {
    if (
      Object.prototype.toString.call(model.value) != "[object Array]" &&
      model.value
    )
      return model.value.split(",");
    else if (
      Object.prototype.toString.call(model.value) != "[object Array]" &&
      !model.value
    )
      return [];
    return model.value;
  },
  set(v) {
    model.value = v;
  },
});
const beforeXhrUpload = (xhr, options) => {
  emit("before:Upload", xhr, options);
  return false;
};
const addressName = () => {
  Taro.navigateTo({
    url: `/pages/ins/components/gdmap`,
  });
};
const selectClick = (callBack) => {
  console.log(props.popupRadioDisabled, "--popupRadioDisabled");
  if (props.type == "gdmap") return addressName();
  if (props.type == "postCode") {
    console.log(model.value);
    userPopupRef.value.open(model.value);
  } else if (props.type == "city") {
    districtPopupRef.value.open([], "ins");
  } else {
    optionCurrent.value = props.popupOption;
    if (needP.value) {
      if (props.popupRadioDisabled) {
        if (props.loadingBrfore) {
          popupRadioRef.value.open([], model.value, () => {});
          search();
        } else
          popupRadioRef.value.open(props.popupOption, model.value, () => {});
      } else {
        props.popupRadioDisabledCallBack();
      }
    }
  }
};
const selectSearch = (data = null) => {
  optionCurrent.value = data;
  if (needP.value) popupRadioRef.value.open(data, model.value);
};
const search = (name) => {
  emit("search", name);
};
const radioConfirm = (val) => {
  model.value = val;
  let arr = optionCurrent.value?.filter((el) => el.dictValue == val)[0] || [];
  emit("current:radio", arr);
};
const setAdress = () => {
  if (props.type == "gdmap") {
    console.log(111);
    const location = Taro.getStorageSync("location");
    if (location) {
      // state.params.latitude = location.location.lat;
      // state.params.longitude = location.location.lng;
      model.value = location.name;
      Taro.removeStorageSync("location");
    }
  }
};

useDidShow(() => {
  setAdress();
});
defineExpose({
  selectSearch,
});
</script>
<style lang="scss">
.location_ins {
  width: 24px;
  height: 24px;
}
.form-textare {
  padding-left: 0 !important;
  padding-top: 0 !important;
  border-bottom: 1px solid #f4f5f7 !important;
}
.non-border {
  border: none !important;
  // padding-bottom: 0 !important;
}
.nut-cell-group .nut-cell::after {
  border: none;
}

.nut-textarea__textarea {
  // text-align: right;
  // padding-bottom: 16px !important;
}
.nut-textarea {
  background: rgba(0, 0, 0, 0) !important;
}
.nut-ellipsis__wordbreak {
  width: 100%;
  text-align: right;
  margin-right: 10px;
  font-size: 16px;
}
.ellipsisBox {
  display: flex;
  // align-items: center;
}
// .nut-cell__title{
// 	line-height: ;
// }
.formItem {
}
.arrowIcon {
  width: 16px;
  height: 16px;
  margin-top: 2px;
}
.input-text::placeholder {
  text-align: right;
  padding-right: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #c9cdd0;
  line-height: 22px;
}
.nut-cell__title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #1d212b;
  line-height: 22px;
}
.nut-cell-group__wrap {
  margin-top: 0;
  padding: 0 16px;
}
.addIns {
  padding-bottom: 90px;
  background: #fff;
  .section {
    font-size: 14px;
    .label {
      color: #1d212b;
      &::before {
        content: "*";
        color: red;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }
  .nut-cell__title {
    color: #1d212b;
  }
  .nut-input-left-box {
    margin-right: 10px;
  }
}
</style>
