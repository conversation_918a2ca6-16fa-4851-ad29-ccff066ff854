<template></template>
<script setup>
import Taro, { useRouter } from "@tarojs/taro";
import { reactive, onMounted, ref } from "vue";
import { login, getUserInfo, hrDetail } from "@/api/login.js";

const login = async () => {
  const userData = await getUserInfo();
  Taro.setStorage({ key: "initUserInfo", data: userData.data });
  const hrData = await hrDetail({
    appCode: "athena_hr",
    tenantId: '000010',
  });
  Taro.setStorage({ key: "initHrInfo", data: hrData.data });
};

</script>