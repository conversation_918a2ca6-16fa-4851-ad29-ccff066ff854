<template>
  <!-- 页面头部 -->

  <view class="navBar" :style="props.navBarStyle">
    <nut-navbar :title="props.leftText">
      <template #left>
        <slot name="left">
          <img :src="backIcon" class="backIcon" @click="back" />
        </slot>
      </template>
      <template #content>
        <slot name="titleContent" />
      </template>
    </nut-navbar>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import backIcon from "@/images/back.png";
import { defineEmits, defineProps, reactive } from "vue";
const emit = defineEmits(["click-back"]);
const props = defineProps({
  navBarStyle: {
    type: String,
    default: "",
  },
  leftText: {
    type: String,
    default: "",
  },
  flag: {
    type: Boolean,
    default: false,
  },
  flag: {
    type: Boolean,
    default: false,
  },
});

const back = () => {
  emit("click-back");
};
</script>
<style lang="scss">
.navBar {
  width: 100%;
  background-color: #fff;
  padding-top: 44px;
  position: fixed;
  top: 0;
  z-index: 3;

  .backIcon {
    width: 32px;
    height: 32px;
    vertical-align: middle;
  }

  .leftText {
    color: black;
    margin-top: 10px;
    font-size: 17px;
  }

  .nut-navbar {
    height: 50px;
    margin-bottom: 0px;
    padding: 0 5px 0 12px;
    -webkit-box-shadow: none;
    margin-left: 16px;
  }

  .nut-navbar__left {
    padding: 0;
  }
  .nut-navbar__title .title {
    color: #2b2f36;
    font-size: 16px;
    font-weight: 700;
  }
}
</style>
