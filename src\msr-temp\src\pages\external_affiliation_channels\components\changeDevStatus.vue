<template>
  <nut-popup v-model:visible="show" position="bottom" round>
    <view class="text-[#1D212B] text-[18px] font-[600] text-center py-[16px]">变更开发状态</view>
    <view class="px-[16px]">
      <view v-for="item in list" class="flex justify-between items-center" @tap="handleCheck(item)">
        <view :class="{
          'h-[54px]': true,
          'leading-[54px]': true,
          'text-[#1D212B]': currentItem.value !== item.value,
          'text-[#2551F2]': currentItem.value === item.value,
          'text-[16px]': true
        }">{{ item.label }}</view>
        <view>
          <img :src="checked" class="w-[16px] h-[12px]" v-show="currentItem.value === item.value" />
        </view>
      </view>
    </view>
    <view class="text-center h-[54px] leading-[54px] text-[#2551F2] text-[16px] pb-[16px]" style="border-top: 8px solid #f4f5f7">
      确认
    </view>
  </nut-popup>
</template>
<script setup>
import { ref } from 'vue';
import checked from './../../../images/checked2.png';

const emits = defineEmits(['confirm'])
const currentItem = ref({});
const show = ref(false);

const list = [
  {
    label: '开发中-完成商业政策谈判',
    value: '1'
  },
  {
    label: '开发中-完成商业政策谈判',
    value: '2'
  },
  {
    label: '开发中-完成商业政策谈判',
    value: '3'
  },
  {
    label: '开发中-完成商业政策谈判',
    value: '4'
  },
  {
    label: '开发中-完成商业政策谈判',
    value: '5'
  },
  {
    label: '开发中-完成商业政策谈判',
    value: '6'
  }
]

const handleCheck = item => {
  currentItem.value = item;
}

const submit = () => {
  emits('confirm', currentItem.value)
}

const open = () => {
  show.value = true
}

defineExpose({
  open
})
</script>
