<template>
  <view>
    <view class="contentTitle">
      <view
        :class="{ active: props.activeIndex === 0 }"
        @tap="setActiveIndex(0)"
      >
        {{ `我的${props.leftText}` }}
      </view>
      <view
        :class="{ active: props.activeIndex === 1 }"
        @tap="setActiveIndex(1)"
      >
        {{ `下属${props.rightText}` }}
      </view>
    </view>
  </view>
</template>
<script setup>
import { reactive, defineEmits, defineProps } from "vue";
const emit = defineEmits(["setActiveIndex"]);
const props = defineProps({
  activeIndex: {
    type: Number,
    default: 0,
  },
  leftText: {
    type: String,
    default: "",
  },
  rightText: {
    type: String,
    default: "",
  },
});
const state = reactive({});
const setActiveIndex = (v) => {
  emit("setActiveIndex", v);
};
</script>
<style lang="scss">
.contentTitle {
  display: flex;
  justify-content: space-between;
  color: #2551f2;
  view {
    width: 88px;
    height: 30px;
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 600;
    text-align: center;
    line-height: 30px;
    border: 1px solid var(--brand-color, #2551f2);
  }
}
.contentTitle :first-child {
  border-radius: 4px 0 0 4px;
}
.contentTitle :last-child {
  border-radius: 0 4px 4px 0;
}
.contentTitle view.active {
  color: #fff;
  background: #2551f2;
}
</style>
