<template>
  <view
    class="top-search"
    :style="{ paddingRight: props.selectFlag ? '40px' : '0' }"
  >
    <view
      class="top-search-item"
      :class="item.active ? 'bgc' : ''"
      v-for="item in state.list"
      :key="item.key"
      @click="activeItem(item.key)"
    >
      <view style="white-space: nowrap">
        {{ item.text }}
      </view>
    </view>
    <view class="top-search-right" @click="selecSubPre" v-if="props.selectFlag">
      <img :src="screen" alt="" />
    </view>
    <RepresentPopup ref="representPopupRef" @select-pre="selectPre" />
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import screen from "../../../images/screen.png";
import RepresentPopup from "./representPopup";
const representPopupRef = ref(null);

const props = defineProps({
  selectFlag: {
    type: Boolean,
    default: false,
  },
  subEmpCode: {
    type: Array,
    default: [],
  },
  scrollFlag: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["activeItem", "sub-user-select"]);
const state = reactive({
  list: [
    { text: "待拜访", key: "0", active: false },
    { text: "拜访中", key: "2", active: false },
    { text: "已拜访", key: "1", active: false },
    { text: "已取消", key: "3", active: false },
    { text: "已过期", key: "4", active: false },
  ],
});
const debounce = (fn, wait) => {
  if (state.timer !== null) {
    clearTimeout(state.timer);
  }
  state.timer = setTimeout(fn, wait);
};

const clickName = (key) => {
  debounce(activeItem(key), 1000);
};
const activeItem = (key) => {
  if (props.scrollFlag) return;
  state.list.forEach((item) => {
    if (item.key == key) {
      item.active = !item.active;
    }
  });
  const arr = state.list.filter((item) => item.active);
  const keys = arr.map((item) => item.key);
  emit("activeItem", keys);
};

const selecSubPre = () => {
  representPopupRef.value.open(props.subEmpCode);
};

const selectPre = (list) => {
  emit("sub-user-select", list);
};
</script>
<style lang="scss">
.top-search::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}
.top-search {
  display: flex;
  padding: 0 16px;
  overflow: hidden;
  overflow-x: scroll;
  align-items: center;
  background: #edeff5;
  height: 60px;

  .top-search-item {
    height: 27px;
    padding: 0px 9px;
    border-radius: 16px;
    background: #e3e4e9;
    font-size: 14px;
    line-height: 27px;
    margin-right: 8px;
    display: inline-block;
    white-space: nowrap;
    color: #4e595e;
    border: 1px solid #e3e4e9;
  }

  .line {
    margin: 0 4px;
    color: #eeee;
  }

  .top-search-right {
    position: absolute;
    right: 0px;
    font-size: 14px;
    line-height: 27px;
    text-align: center;
    width: 40px;
    background: linear-gradient(
      90deg,
      rgba(237, 239, 245, 0) 0%,
      #edeff5 17%,
      #edeff5 100%
    );
    height: 30px;

    img {
      width: 20px;
      height: 20px;
      vertical-align: middle;
    }
  }
  .bgc {
    color: #2551f2;
    background: var(--brand-color-1, #e8f0ff);
    border: 1px solid #92a8f8;
  }

  .top-search-value {
    max-width: 148px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .rotate {
    color: #2551f2;
    transform: rotate(180deg);
  }
}
</style>
