<template>
  <view class="cellClick">
    <nut-cell is-link @click="selectClick">
      <template #title>
        <view
          style="display: flex"
          :style="{ justifyContent: props.phRight ? 'space-between' : '' }"
        >
          <text :class="props.required ? 'title-required' : ''" class="title">{{
            props.label
          }}</text>
          <text class="placeholder" v-if="!props.value">{{
            props.placeholder
          }}</text>
          <text
            :style="`text-align: right; margin-left: 10px;${props.valueStyle}`"
            v-else
            >{{ props.value }}</text
          >
        </view>
      </template>
      <template #link>
        <img :src="arrow" class="arrowIcon" />
      </template>
    </nut-cell>
  </view>
</template>
<script setup>
import { reactive } from "vue";
import arrow from "@/images/arrow.png";
const props = defineProps({
  valueStyle: {
    type: String,
    default: "",
  },

  value: {
    type: String,
    default: "",
  },
  placeholder: {
    type: String,
    default: "",
  },
  label: {
    type: String,
    default: "",
  },
  required: {
    type: Boolean,
    default: false,
  },
  phRight: {
    type: Boolean,
    default: false,
  },
});
const state = reactive({});
const emit = defineEmits(["selectClick"]);
const selectClick = () => {
  emit("selectClick");
};
</script>
<style lang="scss">
.cellClick {
  .placeholder {
    margin-left: 10px;
    color: #c9cdd0;
  }
  .arrowIcon {
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }
  .title {
    color: #1d212b;
    white-space: pre;
    margin-right: 10px;
  }
  .title-required {
    &::before {
      content: "*";
      color: red;
      margin-right: 5px;
      vertical-align: middle;
    }
  }
}
</style>
