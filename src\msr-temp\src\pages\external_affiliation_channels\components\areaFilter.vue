<template>
  <GlobalPopup ref="popupRef" position="right" :round="false">
    <view class="w-[80vw] h-screen">
      <view>省市区县筛选</view>
    </view>
  </GlobalPopup>
</template>
<script setup>
import { ref } from 'vue';
import GlobalPopup from "../../../pages/components/globalPopup/globalPopup.vue";

const popupRef = ref();

const open = () => {
  popupRef.value?.open();
}

defineExpose({
  open,
});

</script>
