.nut-tabs .nut-tabs__titles {
  background: #fff;
}
.nut-tabs
  .nut-tabs__titles
  .nut-tabs__titles-item.active
  .nut-tabs__titles-item__line {
  background: #2551f2;
  border-radius: 3px 3px 0 0;
}
.nut-tabs .nut-tabs__titles .nut-tabs__titles-item__smile,
.nut-tabs .nut-tabs__titles .nut-tabs__titles-item__line {
  bottom: 0;
}
.nut-cell {
  -webkit-box-shadow: none;
  margin: 0;
  font-size: 16px;
  padding: 17px 0;
  border-bottom: 1px solid #f4f5f7;
}
.nut-cell_title {
  align-items: center;
}
.nut-radio {
  padding: 17px 0;
  border-bottom: 1px solid #f4f5f7;
  color: #1d212b;
  background: #fff;
}
.nut-radio__label {
  font-size: 16px;
  color: #1d212b;
}
.nut-radio-group {
  display: block;
}

.nut-input {
  font-size: 16px;
  padding: 17px 0;
  border-bottom: 1px solid #f4f5f7;
}
.nut-textarea {
  height: 65px;
  padding: 0;
  height: 88px;
  background: #f4f5f7;
}

textarea {
  left: -5px;
  z-index: 0;
  height: 88px;
}
.nut-textarea__textarea {
  padding: 16px;
}
.nut-textarea__limit {
  bottom: 2px;
}
.nut-cell__value {
  color: #1d212b;
}
.nut-cell__title {
  color: #1d212b;
}
.nut-input .input-text {
  font-size: 16px;
}
.nut-checkbox-group {
  display: block;
}

.nut-checkbox {
  display: flex;
  padding: 17px 0;
  margin-right: 0;
}
.nut-picker__right {
  color: #2551f2;
}
.nut-tag {
  height: 18px;
  border-radius: 2px;
  background: rgb(255, 255, 255, 0) !important;
}
.nut-checkbox__label {
  font-size: 16px;
}
.nut-input-left-box {
  margin-right: 0;
}
.nut-input-box {
  margin-left: 10px;
}
.nut-calendar
  .nut-calendar__header
  .nut-calendar__weekdays
  .nut-calendar__weekday.weekend {
  color: #869199;
}
.nut-calendar
  .nut-calendar__content
  .nut-calendar__panel
  .nut-calendar__days
  .nut-calendar__day.weekend {
  color: #869199;
}
.nut-calendar
  .nut-calendar__content
  .nut-calendar__panel
  .nut-calendar__days
  .nut-calendar__day--active {
  background: #2551f2;
}

.nut-searchbar__search-input {
  box-shadow: none;
}
.nut-switch {
  background-color: #2551f2;
}
.nut-cascader-item.active:not(.disabled) {
  color: #2551f2;
}
.nut-cascader-item.active .nut-cascader-item__icon-check {
  color: #2551f2;
}

.nut-tabs .nut-tabs__titles .nut-tabs__titles-item {
  color: #86909c;
}
