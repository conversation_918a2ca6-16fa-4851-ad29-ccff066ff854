import {
  institutionLevel,
  district,
  deptNameList,
  applyFormType,
} from "../api/area.js";
import Taro from "@tarojs/taro";

export async function getDistrict() {
  const { data } = await district();
  Taro.setStorage({
    key: "district",
    data: data,
  });
  data.forEach((item) => {
    item.children.unshift({ code: "", value: "全部" });
    if (item.children && item.children.length) {
      item.children.forEach((i) => {
        if (i.children && item.children.length) {
          i.children.unshift({ code: "", value: "全部" });
        }
      });
    }
  });

  Taro.setStorage({
    key: "districtList",
    data: data,
  });
}
export async function getInstitutionLevel() {
  const { rows } = await institutionLevel();
  Taro.setStorage({
    key: "institutionLevel",
    data: rows,
  });
}

export async function getDeptNameList() {
  const { rows } = await deptNameList();
  Taro.setStorage({
    key: "deptNameList",
    data: rows,
  });
}

export async function getApplyFormType() {
  const { rows } = await applyFormType();
  Taro.setStorage({
    key: "applyFormTypeList",
    data: rows,
  });
}

export function getInsLevelColor(code) {
  if (code === "一级") {
    return "#FCC59F";
  } else if (code === "二级") {
    return "#A6E5D0";
  } else if (code === "三级") {
    return "#92A8F8";
  } else {
    return "#92A8F8";
  }
}

export function getInsLevelTextColor(code) {
  if (code === "一级") {
    return "#F77234";
  } else if (code === "二级") {
    return "#00B578";
  } else if (code === "三级") {
    return "#2551F2";
  } else {
    return "#2551F2";
  }
}

export function getInsDetailLevelColor(code) {
  if (code === "一级") {
    return "#E8F0FF";
  } else if (code === "二级") {
    return "#E6F8F2";
  } else if (code === "三级") {
    return "#FFF3E8";
  } else {
    return "#F3F4F5";
  }
}

export function getInsDetailLevelTextColor(code) {
  if (code === "一级") {
    return "#2551F2";
  } else if (code === "二级") {
    return "#00B578";
  } else if (code === "三级") {
    return "#F77234";
  } else {
    return "#4E595E";
  }
}

export function getPostCode() {
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  if (initHrInfo.jurCodeList) {
    return initHrInfo.jurCodeList.map((item) => item).join(",");
  } else {
    return "";
  }
}

export function getProduct(list) {
  if (list && list.length) {
    return list.map((item) => item.name).join(" | ");
  }
}
export function getDetailProduct(list) {
  if (list && list.length) {
    return list.map((item) => item.name).join("、");
  }
}

export function getcolorProfession(code) {
  if (code === "主任医师") {
    return "#FCC59F";
  } else if (code === "住院医师") {
    return "#A6E5D0";
  } else if (code === "主治医师") {
    return "#FCC59F";
  } else if (code === "副主任医师") {
    return "#94BFFF";
  } else {
    return "#C6CAD1";
  }
}

export function getTextProfession(code) {
  if (code === "主任医师") {
    return "#F77234";
  } else if (code === "住院医师") {
    return "#00B578";
  } else if (code === "主治医师") {
    return "#F77234";
  } else if (code === "副主任医师") {
    return "#2551F2";
  } else {
    return "#4E595E";
  }
}
