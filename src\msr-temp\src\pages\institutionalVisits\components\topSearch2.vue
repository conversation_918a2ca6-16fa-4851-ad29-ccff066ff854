<template>
  <view class="topSearch">
    <nut-searchbar
      :style="{
        paddingBottom: 0
      }"
      :placeholder="props.placeholder"
      v-model="state.searchValue"
      clearable
      @change="onChange"
    >
      <template #leftin>
        <Search2 />
      </template>
      <template #rightin>
        <view style="display: flex; flex-direction: row; align-items: center">
          <view class="split"></view>
          <view class="searchFont" @click="search">搜索</view>
        </view>
      </template>
    </nut-searchbar>
  </view>
</template>
<script setup>
import { reactive } from "vue";
import { Search2 } from "@nutui/icons-vue-taro";
const props = defineProps({
  fixed: {
    type: String,
    default: "static",
  },
  placeholder: {
    type: String,
    default: "",
  },
  searchFlag: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["top-search"]);
const state = reactive({
  searchValue: "",
});
const search = () => {
  emit("top-search", state.searchValue);
};

const onChange = (value) => {
  if (!value) emit("top-search", "");
  // if (props.searchFlag) return;
  // search();
};
const setSearch = () => {
  state.searchValue = "";
};
defineExpose({
  setSearch,
});
</script>
<style lang="scss" scoped>
:deep(.nut-icon-search2) {
  font-size: 13px;
  color: #869199;
}
.topSearch {
  height: 36px;
  box-sizing: border-box;
  margin-bottom: 8px;

  .split {
    width: 0px;
    height: 19px;
    border: 0.5px solid #e5e6eb;
  }

  .searchFont {
    height: 21px;
    font-weight: 400;
    font-size: 15px;
    color: #2551f2;
    line-height: 21px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-left: 8px;
  }

  .nut-searchbar__search-input {
    height: 36px;
    border-radius: 20px 20px 20px 20px;
    border: 1px solid #1d212b;
    color: #869199;
  }
}
</style>
