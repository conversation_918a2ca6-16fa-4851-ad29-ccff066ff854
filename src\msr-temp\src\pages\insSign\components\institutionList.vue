<template>
  <view class="ins-sign-list">
    <scroll-view
      :scroll-y="true"
      style="height: 600px"
      @scrolltolower="scrolltolower"
      :scroll-top="state.scrollTop"
    >
      <view
        style="
          position: absolute;
          top: 40%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!props.institutionList.length"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>
      <view
        v-for="item in props.institutionList"
        :key="item.id"
        class="visitsList"
        @click="visitsList(item)"
      >
        <view>
          <view class="visitsList_left">
            <view style="display: flex; align-items: center">
              <view
                style="
                  margin-right: 4px;
                  display: flex;
                  align-items: center;
                  font-weight: 500;
                "
                >{{ item.institutionName }}
              </view>
            </view>
            <view
              :class="getColor(item.status)"
              style="font-size: 14px; min-width: 60px; text-align: right"
            >
              {{ getVisitStatusName(item.status) }}
            </view>
            <view> </view>
          </view>
          <view>
            <nut-tag
              plain
              color="#C6CAD1"
              text-color="#4E595E"
              style="vertical-align: text-top"
              v-if="item.insLabel"
            >
              {{ item.insLabel }}
            </nut-tag>
          </view>
          <view
            class="visitsList_bottom"
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <view>签到时间：{{ visitTime(item) }}</view>
            <view v-if="props.activeIndex == 1"
              >代表：{{ item.visitUserName }}
            </view>
          </view>
        </view>
        <nut-divider dashed :style="{ color: '#E5E6EB', margin: '8px 0' }" />
        <view class="visitsList_bottom">
          <view>机构地址：{{ item.insAddress }}</view>
          <view>签到地址：{{ item.signInAddress }}</view>
        </view>
      </view>
      <view
        v-if="
          props.institutionList.length > 0 &&
          props.institutionList.length == props.total
        "
        style="color: #869199; text-align: center"
        >已全部加载完毕</view
      >
    </scroll-view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import noData from "../../../images/no-data.png";
import { defineProps, onMounted, reactive } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

import {
  getVisitStatusName,
  getColor,
  currentTime,
} from "../../../utils/content.js";
import time from "../../../images/schedule.png";
import cus_ins from "../../../images/hospital_v3.png";
import adress from "../../../images/location.png";
import user from "../../../images/user.png";
const emit = defineEmits(["scrolltolower"]);
const props = defineProps({
  institutionList: {
    type: Array,
    default: [],
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
  total: {
    type: Number,
    default: 0,
  },
});
const state = reactive({
  scrollTop: 0,
});
const scrolltolower = () => {
  emit("scrolltolower");
};

const visitsList = (item) => {
  if (item.status === "0" || item.status === "3" || item.status === "4") {
    Taro.navigateTo({
      url: `/pages/insSign/planDetail?id=${item.id}&activeIndex=${props.activeIndex}`,
    });
  } else {
    Taro.navigateTo({
      url: `/pages/insSign/visiting?id=${item.id}&activeIndex=${props.activeIndex}`,
    });
  }
};

const visitTime = (item) => {
  if (item.status === "0" || item.status === "3" || item.status === "4") {
    const start = dayjs(item.visitTime).format("MM-DD HH:mm");
    const end = dayjs(item.completeTime).format("HH:mm");
    return `${start} - ${end}`;
  } else if (item.status === "1") {
    const signIn = dayjs(item.signInTime).format("MM-DD HH:mm");
    const signOut = dayjs(item.signOutTime).format("HH:mm");
    return `${signIn} - ${signOut}`;
  } else if (item.status === "2") {
    return dayjs(item.signInTime).format("MM-DD HH:mm");
  } else {
  }
};
</script>

<style lang="scss" scoped>
.ins-sign-list {
  color: #1d212b;
  padding: 0 16px;
  padding-bottom: 25px;
  margin-top: 16px;
  .visitsList {
    padding: 12px 16px 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .visitsList_left {
      font-size: 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .visitsList_bottom {
      font-size: 13px;
      color: #869199;
    }
  }
  .nut-divider {
    margin: 16px 0;
  }
  .color-0 {
    color: rgb(55, 118, 246);
  }

  .color-1 {
    color: rgba(0, 181, 120);
  }

  .color-2 {
    color: rgba(255, 180, 47);
  }

  .color-4 {
    color: rgba(243, 47, 41);
  }
  .tag-f {
    margin-left: 4px;
    color: #f77234;
    background: #fff3e8;
    min-width: 48px;

    font-size: 12px;

    font-family: PingFang SC;
    font-weight: 600;
    border-radius: 4px;
    padding: 2px 8px;
    height: 20px;
    line-height: 20px;
  }
  .tag-s {
    margin-left: 4px;
    color: #2551f2;
    background: #e8f0ff;
    min-width: 48px;
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: 600;
    border-radius: 4px;
    padding: 2px 8px;
    height: 20px;
    line-height: 20px;
  }
}
</style>
