import httpRequest from "@/servers/http";
export const pharmacyType = (params) => {
  return httpRequest.get("/plt/dict/data/list?dictType=pharmacy_type", params);
};

export const areaType = (params) => {
  return httpRequest.get("/plt/dict/data/list?dictType=area_type", params);
};

export const pharmacyNature = (params) => {
  return httpRequest.get(
    "/plt/dict/data/list?dictType=pharmacy_nature",
    params
  );
};

export const paymentType = (params) => {
  return httpRequest.get("/plt/dict/data/list?dictType=payment_type", params);
};

export const businessScope = (params) => {
  return httpRequest.get("/plt/dict/data/list?dictType=business_scope", params);
};

export const approveType = (params) => {
  return httpRequest.get("/plt/dict/data/list?dictType=approve_type", params);
};
export const developType = (params) => {
  return httpRequest.get("/plt/dict/data/list?dictType=develop_type", params);
};

export const addDrugstore = (params) => {
  return httpRequest.post("/msr/drugstore/add", params);
};

export const SuperiorDrugstoreList = (params) => {
  return httpRequest.get("/msr/drugstore/lov", params);
};

export const drugstoreList = (pageNum, pageSize, data) => {
  return httpRequest.post(
    `/msr/rel-drugstore/lov?pageNum=${pageNum}&pageSize=${pageSize}`,
    data
  );
};

export const associationList = (params) => {
  return httpRequest.get("/msr/rel-drugstore/drugstores", params);
};
export const drugstoreInsList = (pageNum, pageSize, data) => {
  return httpRequest.post(
    `/msr/rel-drugstore/institution?pageNum=${pageNum}&pageSize=${pageSize}`,
    data
  );
};
export const updateSalesStatus = (id, params) => {
  return httpRequest.put(`/msr/rel-drugstore/status/${id}?status=${params}`);
};
export const relationDrugstoreDetail = (id) => {
  return httpRequest.get(`/msr/drugstore/${id}`);
};

export const selectNoBindingList = (params) => {
  return httpRequest.get(`/msr/drugstore/add-search`, params);
};
export const applyList = (params) => {
  return httpRequest.get(`/msr/bpm/page`, params);
};
export const cancel = (params) => {
  return httpRequest.put(`/msr/bpm/cancel`, params);
};

export const sysAddPharmacy = () => {
  return httpRequest.get(`/plt/config/configKey/sys.add.pharmacy`, );
};
