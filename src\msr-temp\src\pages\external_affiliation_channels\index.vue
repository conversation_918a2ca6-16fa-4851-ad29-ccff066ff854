<!-- 机构列表页 -->
<template>
  <view class="pharmacy">
    <view class="flex justify-between gap-[10px] items-center bg-white">
      <SearchBar @seacrh-top="topSpeakerSearch" placeholder="请输入医院机构名称或编码" v-show="state.currentTab === '0'"
                 :active="state.activeFlag" :filters="false" />
      <SearchBar @seacrh-top="topApproveSearch" placeholder="请输入审批流程名称进行搜索" v-show="state.currentTab === '1'"
                 :active="state.approveActiveFlag" :filters="false" />
      <view @tap="openAreaFilter()">筛选</view>
    </view>
    <view>
      <nut-tabs v-model="state.currentTab" backgroud="#FFFFFF" @change="tabChange">
        <nut-tab-pane title="院外关联渠道" pane-key="0">
          <List ref="listRef" :tab="state.currentTab" @change-pcdvalue="changePcdvalue" />
        </nut-tab-pane>
        <nut-tab-pane title="我的审批" pane-key="1">
          <ApproveList ref="approveListRef" :tab="state.currentTab" @change-filter="changeApprove" />
        </nut-tab-pane>
      </nut-tabs>
    </view>
    <view style="
        position: fixed;
        height: 56px;
        text-align: right;
        bottom: 80px;
        right: 0;
      " @click="handleAdd()">
      <view class="oval">
        <img :src="plus" style="width: 24px; height: 24px; margin-top: 21px" />
      </view>
    </view>
  </view>
  <AreaFilterPop ref="areaFilterPopRef" />
</template>
<script setup>
import Taro, { useRouter, useDidShow, useDidHide } from "@tarojs/taro";
import { ref, onMounted, reactive, onUnmounted, onBeforeUnmount } from "vue";
import List from "./list/index.vue";
import ApproveList from "@/bpm-temp/src/pages/approve_msr/index.vue";
import plus from "../../images/inst_plus.png";
import SearchBar from "../../pages/components/searchBar";
import { getAcademyLevelApi, getAccessLevelApi, getAdministrationLvlApi, getBrandLevelApi, getRetireStatusApi, getSpeakerTypeApi, getAntiAgingRatingApi, getTerapyAreaApi, getIdCardTypeaApi } from "../../utils/content";;
import { getUserInfo, hrDetail } from "@/api/login.js";
import AreaFilterPop from './components/areaFilter';


const router = useRouter();
const query = router.params;
const initHrInfo = Taro.getStorageSync("initHrInfo");
const listRef = ref(null);
const approveListRef = ref(null);
const areaFilterPopRef = ref(null);
const isProd =  process.env.NODE_ENV === "production";

const state = reactive({
  currentTab: "0",
  activeFlag: false,
  approveActiveFlag: false,
  previousUrl: "",
  addPharmacyFlag: ''
});

const openAreaFilter = () => {
  areaFilterPopRef.value?.open()
}

const changePcdvalue = (value) => {
  if (value) {
    state.activeFlag = true;
  } else {
    state.activeFlag = false;
  }
};
const changeApprove = (value) => {
  console.log("2222", value);
  if (value) {
    state.approveActiveFlag = true;
  } else {
    state.approveActiveFlag = false;
  }
};

const topSpeakerSearch = (search) => {
  listRef.value.topSearch(search);
};

const topApproveSearch = (search) => {
  approveListRef.value.topSearch(search);
};


const tabChange = () => {
  if (state.currentTab === "0") {
    listRef.value.initInsList();
  } else {
    approveListRef.value.initInsList();
  }
};

const handleAdd = () => {
  Taro.navigateTo({
    url: `/pages/external_affiliation_channels/components/add?type=add`,
  });
};

// 将当前token转换成000010的token
const authToken = async () => {
  try {
    const __res = await Taro.request({
      method: 'post',
      url: isProd ? 'https://athena-api.dgtmeta.com/auth/social/sysToken' : 'https://athena-api-dev.dgtmeta.com/auth/social/sysToken',
      data: {
        appCode: "athena_hr",
        tenantId: '000010',
        userToken: Taro.getStorageSync("access_token")
      }
    });
    access_token.value = __res?.data?.access_token;
    Taro.setStorage({ key: "000010-" + "access_token", data: __res?.data?.access_token });

    const userData = await getUserInfo();
    Taro.setStorage({ key: "000010-" + "initUserInfo", data: userData.data });
    const hrData = await hrDetail({
      appCode: 'athena_hr',
      tenantId: '000010',
    });
    Taro.setStorage({ key: "000010-" + "initHrInfo", data: hrData.data });
  } catch (e) {

  }
}

useDidShow(() => {
  authToken()
  if (query.index === "1") {
    state.currentTab = "1";
  }
  Taro.setNavigationBarTitle({ title: "院外关联渠道" });
  tabChange();
});
onMounted(() => {
  getAcademyLevelApi()
  getAccessLevelApi()
  getAdministrationLvlApi()
  getBrandLevelApi()
  getRetireStatusApi()
  getSpeakerTypeApi()
  getAntiAgingRatingApi()
  getTerapyAreaApi()
  getIdCardTypeaApi()
})

</script>

<style lang="scss">
input::placeholder {
  color: yellow;
}

.pharmacy {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100vw;
  overflow: hidden;

  .searchBar {
    background: #fff;
  }

  .oval {
    background-image: url("../../images/inst_oval.png");
    background-size: cover;
    background-repeat: no-repeat;

    width: 75px;
    height: 75px;
    margin-right: 24px;
    display: inline-block;
    text-align: center;
  }

  .nut-tabs__titles-item__line {
    background: blue;
  }

  .nut-tab-pane {
    background-color: transparent;
    padding: 18px 0px;
  }

  .nut-tab-pane {
    padding: 0 0;
  }
}
</style>
