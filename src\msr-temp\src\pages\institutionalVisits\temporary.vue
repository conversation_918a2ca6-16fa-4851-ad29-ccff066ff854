<template>
  <view :class="{ temporary: true, overflowStyle: overflowStyle }">
    <view class="section">
      <CellClick
        @selectClick="selectPopup('objective')"
        :value="state.visitPurposeName"
        :required="true"
        label="拜访目的"
        placeholder="请选择"
        :phRight="true"
      />
      <view class="subtitle">
        <nut-cell
          title="上次沟通内容"
          :sub-title="state.lastCommunicateContent"
        ></nut-cell
      ></view>
      <view>
        <view style="font-size: 16px; margin: 10px 0">本次沟通内容</view>
        <nut-textarea
          v-model="state.params.communicateContent"
          limit-show
          max-length="200"
          placeholder="请输入"
          @blur="blurTextarea"
        />
      </view>
      <nut-divider style="color: #f4f5f7" :style="{ marginBottom: 0 }" />
      <view class="sculpture">
        <view class="selectUser">
          <view class="selectUser-title">协访人员</view>

          <view class="deteleText">点击头像可删除</view>
        </view>
        <view class="headSculpture">
          <view
            v-for="(item, index) in state.params.assistForm"
            :key="item.empCode"
            style="margin-right: 5px; display: flex; flex-wrap: wrap"
          >
            <view style="text-align: center; margin-right: 5px">
              <view class="picture" @click="deleteAssist(item)">
                <img
                  :src="item.assistAvatar ? item.assistAvatar : avatar"
                  alt=""
                />
              </view>
              <view style="color: #4e595e; font-size: 12px">{{
                item.empName
              }}</view>
            </view>
          </view>
          <view @click="openAssist">
            <img :src="Group5" alt="" />
          </view>
        </view>
      </view>
      <CellClick
        @selectClick="selectPopup('assistObjective')"
        :value="state.assistRequireName"
        label="协访需求"
        placeholder="请选择"
        :phRight="true"
      />

      <view class="customerstext">
        <view class="title cellClick">
          <view style="font-size: 16px" class="title-pro title-required"
            >沟通产品（{{ state.params.productForms.length }}）</view
          >
          <view
            ><nut-button
              color="#E8F0FF"
              @click="selectProducts"
              style="
                color: #2551f2;
                height: 32px;
                padding: 12px;
                font-size: 16px;
              "
              >新增
              <template #icon>
                <img
                  :src="addicon"
                  style="
                    width: 16px;
                    width: 16px;
                    margin-top: -2.5px;
                    margin-right: 4px;
                  "
                  alt=""
                />
              </template>
            </nut-button>
          </view>
        </view>
        <view
          v-for="item in state.params.productForms"
          :key="item.productCode"
          class="product"
        >
          <view class="productNames">
            <view>{{ item.productName }}</view>
          </view>

          <view class="productContent">
            <view
              v-for="j in item.productInfoList"
              :key="j.infoId"
              class="info"
            >
              <view
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  padding: 12px;
                "
              >
                <view> {{ j.info }}</view>
                <view style="min-width: 20px; text-align: right">
                  <img
                    :src="deleteThemes"
                    alt=""
                    class="deleteIcon"
                    @click="deleteProduct(item.productCode, j.infoId)"
                /></view>
              </view>
              <view class="feedback">
                <view class="approve">认可度</view>
                <nut-radio-group
                  v-model="j.cusProdRespCode"
                  direction="horizontal"
                >
                  <nut-radio
                    :label="item.label"
                    v-for="item in state.optionsRadio"
                    :key="item.label"
                  >
                    {{ item.text }}
                    <template #checkedIcon>
                      <img :src="radioIcon3" alt="" class="radioIcon" />
                    </template>
                  </nut-radio>
                </nut-radio-group>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <FooterButton
      :isLoading="state.isLoading"
      text="保存"
      @click-button="clickFooterButtonDeb"
    />
    <PopupChexked ref="temporaryPopupRef" @check-confirm="checkConfirm" />
    <SelectAssistPopup ref="assistPopupRef" @select-assist="selectAssist" />
  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
import CellClick from "../../pages/components/cellClick/index.vue";
import PopupChexked from "../../pages/components/popupChexked/popupChexked";

import {
  addVisiInfo,
  assistMessage,
  institutionalVisitsDetail,
  filtering,
} from "../../api/institutionalVisitsApi.js";
import FooterButton from "../../pages/components/footerButton/index.vue";
import deleteThemes from "../../images/Delete-themes.png";
import addicon from "../../images/addicon.png";

import radioIcon3 from "../../images/radio-icon3.png";
import avatar from "../../images/avatar.png";
import Group5 from "../../images/Group5.png";
import SelectAssistPopup from "./components/selectAssistPopup.vue";
import {
  visitPurposeName,
  assistPurposeName,
  appCode,
  assistUrl,
} from "../../utils/content.js";
const feishuNotice = ref({
  appLink: `${assistUrl}`,
  pcLink: `${assistUrl}`,
});
const router = useRouter();
const query = router.params;
const temporaryPopupRef = ref(null);
const selectObjectiveList = Taro.getStorageSync("selectObjective");
const assistPurpose = Taro.getStorageSync("assistPurpose");
const overflowStyle = ref(true);
const assistPopupRef = ref(null);
const state = reactive({
  isLoading: false,
  type: "",
  lastCommunicateContent: "",
  visitPurposeName: "",
  assistRequireName: "",
  info: {},
  params: {
    visitPurposeCode: "one",
    assistRequirement: "",
    assistForm: [],
    communicateContent: "",
    productForms: [],
  },
  optionsRadio: [
    {
      text: "认可",
      label: "1",
    },
    {
      text: "不认可",
      label: "0",
    },

    {
      text: "中立",
      label: "2",
    },
  ],
});

const selectPopup = (type) => {
  state.type = type;
  if (type === "objective") {
    temporaryPopupRef.value.open(
      selectObjectiveList,
      state.params.visitPurposeCode
    );
  }
  if (type === "assistObjective") {
    temporaryPopupRef.value.open(assistPurpose, state.params.assistRequirement);
  }
};

const checkConfirm = (list) => {
  console.log("多选的code", list);
  if (state.type === "objective") {
    const arr = selectObjectiveList.filter((item) =>
      list.includes(item.dictValue)
    );
    state.visitPurposeName = arr.map((item) => item.dictLabel).join(",");
    state.params.visitPurposeCode = list.join(",");
  }
  if (state.type === "assistObjective") {
    const arr = assistPurpose.filter((item) => list.includes(item.dictValue));
    state.assistRequireName = arr.map((item) => item.dictLabel).join(",");
    state.params.assistRequirement = list.join(",");
  }
};

const selectAssist = (list) => {
  if (!state.params.assistForm.length) {
    state.params.assistForm = list;
  } else {
    const newList = list.filter(
      (item) =>
        !state.params.assistForm.some(
          (existingItem) => existingItem.id === item.id
        )
    );
    state.params.assistForm = [...state.params.assistForm, ...newList];
  }

  state.params.assistForm = state.params.assistForm.slice(0, 10);
};

const deleteAssist = (v) => {
  Taro.showModal({
    content: `是否删除协访人${v.empName}`,
    success: async function (res) {
      if (res.confirm) {
        state.params.assistForm = state.params.assistForm.filter(
          (item) => item.id !== v.id
        );
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};
let timerFooterId = null;
const blurTextarea = async () => {
  const res = await filtering({ txt: state.params.communicateContent });
  if (res.data.length) {
    const content = `本次沟通内容包含如下敏感词：${res.data.join(
      "、"
    )}，请重新输入`;
    Taro.showModal({
      content: content,
      confirmText: "确定",
      duration: 2000,
      showCancel: false,
    });
    return false;
  } else {
    return true;
  }
};
const clickFooterButtonDeb = async () => {
  if (timerFooterId) {
    clearTimeout(timerFooterId);
    timerFooterId = null;
  }

  if(!state.params.productForms.length) {
    Taro.showToast({
      title: "请选择至少一个沟通产品",
      icon: "none",
    });
    return;
  }
  timerFooterId = setTimeout(() => {
    clickFooterButton();
  }, 300);
};

const clickFooterButton = async () => {
  if (!state.params.visitPurposeCode) {
    Taro.showToast({
      title: "请选择拜访目的",
      icon: "none",
    });
    return;
  }

  const res = await blurTextarea();
  if (!res) return;

  // if (!state.params.productForms.length) {
  //   Taro.showToast({
  //     title: "请选择沟通产品",
  //     icon: "none",
  //   });
  //   return;
  // }

  let shouldBreak = false;
  state.params.productForms.forEach((item) => {
    if (!shouldBreak) {
      const flag = item.productInfoList.some(
        (product) => !product.cusProdRespCode
      );
      if (flag) {
        shouldBreak = true;
      }
    }
  });

  if (shouldBreak) {
    Taro.showToast({
      title: "请选择认可度",
      icon: "none",
    });
    return;
  }
  const params = {
    ...state.params,
    visitInstitutionId: state.info.visitInstitutionId,
  };
  state.isLoading = true;
  try {
    const res = await addVisiInfo(params);

    const params1 = {
      visitId: state.info.visitInstitutionId,
      empCode: state.info.visitUserHrCode,
      empName: state.info.visitUserName,
      customerName: state.info.customerName,
      insName: state.info.institutionName,
      appCode: appCode(),
      ...feishuNotice.value,
    };
    await assistMessage(params1);
    Taro.navigateBack({
      delta: 1,
    });
  } catch (err) {
    state.isLoading = false;
  }
};
const selectProducts = () => {
  if (state.params.productForms.length) {
    Taro.setStorage({ key: "setPro", data: state.params.productForms });
  }
  console.log(state.info, "--state.info");
  Taro.navigateTo({
    url: `/pages/institutionalVisits/components/selectProducts?insCode=${state.info.insCode}`,
  });
};
const deleteProduct = (code, id) => {
  state.params.productForms = state.params.productForms.map((productForm) => {
    if (productForm.productCode === code) {
      productForm.productInfoList = productForm.productInfoList.filter(
        (info) => info.infoId !== id
      );
    }
    return productForm;
  });

  state.params.productForms = state.params.productForms.filter(
    (productForm) => {
      if (productForm.productCode === code) {
        return productForm.productInfoList.length > 0;
      }
      return true;
    }
  );
  console.log("state.params.productForms", state.params.productForms);
};
const openAssist = () => {
  if (state.params.assistForm.length === 10) {
    Taro.showToast({
      title: "协访人最多只能添加10个",
      icon: "none",
    });
    return;
  }
  assistPopupRef.value.open();
};
const getDetail = async () => {
  const params = {
    visitInstitutionId: query.visitInstitutionId,
  };
  const res = await institutionalVisitsDetail(params);
  state.info = {
    ...res?.data,
    ...state.info,
  };
  // if (query.type === "edit") {
  state.info.visitInstitutionId = state.info.id;
  state.lastCommunicateContent = state.info.lastCommunicateContent;
  state.info.insCode = state.info.institutionCode;
  state.params.visitPurposeCode = state.info.visitPurposeCode;
  state.params.assistRequirement = state.info.assistRequirement;
  state.params.assistForm = state.info.assistUserVOs;
  state.params.communicateContent = state.info.communicateContent;
  state.params.productForms = state.info.productForms;
  state.visitPurposeName = visitPurposeName(state.info.visitPurposeCode);
  state.assistRequireName = assistPurposeName(state.info.assistRequirement);
  // }
};

onMounted(() => {
  Taro.setNavigationBarTitle({
    title: "添加拜访信息",
  });
  // if (query.type === "add") {

  //   state.info = JSON.(query.info);
  //   state.lastCommunicateContent = state.info.lastCommunicateContent;
  //   state.params.communicateContent = state.info.communicateContent;
  //   // getDetail();
  // } else {
  // }
  getDetail();
});
useDidShow(() => {
  Taro.setNavigationBarTitle({ title: "拜访签退" });

  const selectePro = Taro.getStorageSync("selectePro");
  if (selectePro.length) {
    state.params.productForms = selectePro;

    Taro.removeStorageSync("selectePro");
  }
});
</script>

<style lang="scss">
.overflowStyle {
  overflow: hidden;
}
.temporary {
  color: #1d212b;
  .section {
    background: #fff;
    padding: 16px;
    .subtitle {
      .nut-cell__title-desc {
        padding: 8px 0;
      }
    }
    .sculpture {
      margin-bottom: 10px;
      .selectUser {
        display: flex;
        font-size: 12px;
        height: 48px;
        align-items: center;
        .selectUser-title {
          font-size: 16px;
          color: #333;
        }
        .deteleText {
          color: #999;
          font-size: 10px;
          margin-left: 5px;
        }
      }

      .headSculpture {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        font-size: 16px;

        img {
          display: inline-block;
          width: 48px;
          height: 48px;
          margin-top: 5px;
        }

        // .picture {}

        .text {
          width: 46px;
          text-align: center;
        }
      }
    }

    .customerstext {
      background-color: #ffffff;
      margin-top: 8px;
      font-family: PingFang SC;

      .title {
        display: flex;
        justify-content: space-between;
        height: 50px;
        align-items: center;
      }
      .title-pro {
        // &::before {
        //   content: "*";
        //   color: red;
        //   margin-right: 5px;
        //   vertical-align: middle;
        // }
      }

      .productText {
        color: #999;
      }

      .product {
        background-color: #f8fafe;
        margin-top: 8px;

        padding-bottom: 0;

        .productNames {
          display: flex;
          justify-content: space-between;
          border-bottom: 0.96px solid #edf0f3;
          color: #2551f2;
          font-size: 16px;
          font-weight: 500;
          padding: 12px;
        }

        .deleteIcon {
          display: inline-block;
          width: 18px;
          height: 18px;
        }

        .productContent {
          color: #75798d;

          .info {
            font-size: 14px;
          }
        }

        .feedback {
          display: flex;
          align-items: center;
          padding-left: 12px;
          border-bottom: 1px dashed #edf0f3;
          .radioIcon {
            width: 20px;
            height: 20px;
            display: inline-block;
            // margin-top: 2px;
          }
          .approve {
            min-width: 55px;
            &::before {
              content: "*";
              color: red;
              margin-right: 5px;
              vertical-align: middle;
            }
          }
        }
      }
      .nut-radio-group {
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
      }
      .nut-radio {
        background: none;
        border: none;
        padding: 12px 0;
      }
    }
    .cellClick .arrowIcon {
      margin-right: 0;
      margin-top: 2px;
    }
  }
  .nut-button .nut-button__text {
    margin: 0;
  }
  .nut-radio-group--horizontal .nut-radio {
    margin-bottom: 0;
  }
}
</style>
