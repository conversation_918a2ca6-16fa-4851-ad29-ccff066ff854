<template>
  <div>
    <div class="text-[#1D212B] text-[14px] py-[10px]">基本信息</div>
    <div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          变更类型
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.type }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          辖区负责人
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.jurMgrName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">部门</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.ancestors }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">岗位</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.postName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          机构名称
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.insName }}
        </div>
      </div>

      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          客户名称
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.customerName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          认领产品
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.productName }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["formData"]);
</script>
<style lang="scss"></style>
