<template>
  <view class="selectPharmacy">
    <GlobalPopup ref="popupRef">
      <TopSearch @top-search="search" placeholder="搜索" ref="topSearchRef" />
      <scroll-view
        style="height: 400px"
        :scroll-y="true"
        :scroll-top="0"
        @scrolltolower="scrollToLower"
      >
        <view v-if="!state.flag && !state.list.length" class="result-loading"
          >搜索暂无数据</view
        >
        <nut-radio-group v-model="state.radioChecked" text-position="left">
          <GlobalNutRadio
            v-for="item in state.list"
            :value="item.userId"
            :key="item.userId"
          >
            <template #name>
              <view>
                {{ item.nickName }}
                <template v-if="item.userName">({{ item.userName }})</template>
              </view>
            </template>
          </GlobalNutRadio>
        </nut-radio-group>
      </scroll-view>

      <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { reactive, ref } from "vue";
import { listUserApi } from "./../../../api/bpm.js";
import TopSearch from "@/pages/components/topSearch/topSearch.vue";
import GlobalPopup from "@/pages/components/globalPopup/globalPopup.vue";
import GlobalNutRadio from "@/pages/components/globalNutRadio/globalNutRadio.vue";
const emit = defineEmits(["confirm"]);
const popupRef = ref(null);
const state = reactive({
  list: [],
  radioChecked: "",
  params: {
    userName: "",
    pageSize: 10,
    pageNum: 1,
  },
  flag: false,
  total: 0,
});

const search = (name) => {
  state.params.userName = name;
  state.params.pageNum = 1;
  state.list = [];
  getInstitutionList();
};

const getInstitutionList = async () => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  state.flag = true;
  const res = await listUserApi(state.params);
  if (!state.list) {
    state.list = res.rows;
  } else {
    state.list = [...state.list, ...res.rows];
  }
  state.total = res.total;
  state.flag = false;
  Taro.hideLoading();
};

const scrollToLower = () => {
  if (!state.flag && state.list.length < state.total) {
    state.params.pageNum++;
    getInstitutionList();
  }
};
const confirm = () => {
  const user = state.list.filter(
    (item) => item.userId === state.radioChecked
  )[0];
  emit("confirm", user);
  popupRef.value.close();
};

const open = () => {
  state.list = [];
  state.params.userName = "";
  popupRef.value.open();

  getInstitutionList();
};
defineExpose({
  open,
});
</script>
<style lang="scss">
.selectPharmacy {
  // background: #f4f5f7;
  .result-loading {
    color: #869199;
    font-size: 12px;
    text-align: center;
    margin-top: 16px;
  }
  .searchBar .nut-searchbar {
    width: 100%;
  }
  .nut-radio-group {
    padding: 0 16px;
  }
  .nut-radio {
    background: #fff;
  }

  .footer-comfirm {
    height: 40px;

    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    padding-right: 0;
  }
}
</style>
