<template>
  <view class="globa-dialog">
    <nut-popup
    v-model:visible="state.numShow"
    round
    :style="{ width: '311px',}"
  >
    <view style="padding: 16px 16px 0 16px">
      <view class="num-title-popup"
        >{{ props.title }}</view
      >
     <slot />
    </view>
   <view class="popup-footer-button">
    <view class="footer-button" @click="state.numShow = false">
      {{ props.cancelText }}
    </view>
    <nut-divider direction="vertical" :style="{ height:'48px', margin: 0, top:0, borderLeft:'1px solid #E5E6EB' }"/>
    <view class="footer-button" style="color: #2551f2;" @click="confirm">
      {{ props.subTetx}}
    </view>
   </view>
  </nut-popup>
  </view>
</template>
<script setup>
import { reactive,defineEmits } from "vue";
const emit = defineEmits(['confirm'])
const props = defineProps({
  title:{
    type: String,
    default:''
  },
 cancelText:{
  type: String,
    default:'取消'
},
  subTetx:{
    type: String,
    default:'确定'
  },
})
const state = reactive({
  numShow: false,
});

const open = () => {
  state.numShow= true
}
const cancel = () => {
  state.numShow= false
}

const confirm = () => {
  emit("confirm")
}
defineExpose({
  open,
  cancel,
})
</script>
<style lang="scss">
.globa-dialog {
  .num-title-popup {
  font-size: 16px;
  color: #121d29;
  text-align: center;
}

.popup-footer-button {
  height: 48px;
  line-height: 48px;
  display: flex;
  align-items: center;
  border-top: 1px solid #E5E6EB;
  .footer-button {
    width: 50%;
    text-align: center;
    font-size: 16px;
  }
  
}

}

</style>
