<template>
  <view class="speakerList">
    <view class="title"
      >共{{ state.total }}个讲者
    </view>
    <scroll-view
      style="height: 500px"
      :scroll-y="true"
      :scroll-top="state.scrollTop"
      @scrolltolower="onScrollToLower"
    >
      <view
        style="
          position: absolute;
          top: 28%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!state.speakerList.length && scrollMyFlag"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>

      <view v-for="item in state.speakerList" :key="item.id" class="instItem">
        <view
          @click="gotoDetail(item)"
          style="
            display: flex;
            flex-direction: column;
            padding: 16px 16px 12px 16px;
          "
        >
          <view
            style="display: flex; flex-direction: row; align-items: flex-start"
          >
            <view
              style="
                font-weight: 500;
                font-size: 18px;
                color: #1d212b;
                margin-left: 4px;
                max-width: 260px;
                -webkit-line-clamp: 2; /* 显示的行数 */
                -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
                overflow: hidden; /* 隐藏超出的内容 */
                text-overflow: ellipsis;
              "
            >
              {{
                item.name.length > 21
                  ? item.name.substring(0, 21) + "..."
                  : item.name
              }}
              <nut-tag
                plain
                color="#94BFFF"
                text-color="#2551F2"
                style="margin-top: -4px; vertical-align: middle"
              >
                {{ item.type }}
              </nut-tag></view
            >

            <view style="margin-left: auto">
              <img :src="arrowRight" style="width: 15px; height: 17px"
            /></view>
          </view>

          <view
              style="color: #869199; font-size: 14px; margin-top: 3px"

              >医疗机构:{{ item.insName }}

            </view>

            <view
              style="color: #869199; font-size: 14px; margin-top: 3px"

              >行政级别:{{ item.job }}

            </view>
        </view>
      </view>
      <view
        v-if="state.speakerList.length > 0 && state.speakerList.length == state.total"
        style="color: #869199; font-size: 16px; text-align: center"
        >已全部加载完毕</view
      >
    </scroll-view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { defineEmits, defineProps, ref, reactive, defineExpose } from "vue";
import arrowRight from "../../../images/arrow-right.png";
import { speakerListApi } from "../../../api/speaker.js";
import { getPostIdList } from "../../../utils/pharmacy";
import noData from "../../../images/no-data.png";
import {
  getPostCode,
} from "../../../utils/area.js";

const scrollMyFlag = ref(false);
const initHrInfo = Taro.getStorageSync("initHrInfo");

const state = reactive({
  scrollTop: 0,
  pageNum: 1,
  pageSize: 10,
  params: {
    search:'',
    jurCodeList: getPostCode().split(","),

  },
  total: 0,
  pcdvalue: "",
  speakerList: [],
});

const getSpeakerList = () => {
  Taro.showToast({
    title: "加载中",
    icon: "loading",
  });

  speakerListApi(state.pageNum, state.pageSize, state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.total;

        if (!state.speakerList.length) {
          state.speakerList = res.rows || [];
        } else {
          state.speakerList = [...state.speakerList, ...res.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = true;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      Taro.hideLoading();
    });
};

const topSearch = (search) => {
  state.params.search = search;
  state.total = 0;
  state.pageNum = 1;
  state.speakerList = [];
  getSpeakerList();
};


const onScrollToLower = () => {
  if (scrollMyFlag.value && state.speakerList.length < state.total) {
    state.pageNum++;
    scrollMyFlag.value = false;
    getSpeakerList();
  }
};

const initInsList = () => {
  state.total = 0;
  state.pageNum = 1;
  state.speakerList = [];
  getSpeakerList();
};

const gotoDetail = (item) => {
  Taro.navigateTo({
    url: `/pages/external_affiliation_channels/components/detail?code=${item.code}`,
  });
};
defineExpose({
  topSearch,
  initInsList,
});
</script>
<style lang="scss">
.speakerList {
  padding: 0px 0px;
  width: 100vw;
  // height: 630px;
  font-family: PingFang SC, PingFang SC;
  .title {
    color: #869199;
    font-size: 14px;
    padding: 18px 12px 8px 12px;
  }
  .icons {
    width: 14px;
    height: 14px;
    display: block;
    margin-right: 4px;
    margin-top: 4px;
  }
  .instItem {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    margin-left: 14px;
    margin-right: 14px;
  }
  .address {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    font-size: 14px;
    color: #869199;
  }
}
</style>
