<template>
  <view class="signIn">
    <SignTime
      signType="signIn"
      :visitUserName="initUserInfo.nickName"
      status="0"
    />
    <view class="sign-select">
      <CellClick
        @selectClick="selectCusClick"
        :value="state.params.customerName"
        :required="true"
        label="客户"
        placeholder="请选择"
        :phRight="true"
      />
      <nut-tag
        plain
        color="#2551f2"
        text-color="#2551f2"
        style="
          vertical-align: text-top;
          position: relative;
          left: calc(100% - 50px);
        "
        v-if="state.params.insLabel"
      >
        {{ state.params.insLabel }}
      </nut-tag>
      <nut-cell
        title="医疗机构"
        class="sign"
        :desc="state.params.institutionName"
      ></nut-cell>
      <nut-cell
        title="机构地址"
        class="sign"
        :desc="state.params.address"
      ></nut-cell>
      <nut-cell
        title="科室"
        class="sign"
        :desc="state.params.insDeptName"
      ></nut-cell>
      <CellClick
        @selectClick="selectTypeClick"
        :value="state.params.visitTypeName"
        label="拜访类型"
        placeholder="请选择"
        :phRight="true"
      />
      <view class="photograph">
        <view style="padding: 16px 0">拍照</view>
        <view class="photo-list">
          <view
            class="photo"
            v-for="(photo, index) in photoList"
            :key="photo.ossId"
          >
            <img
              :src="photo.url"
              alt=""
              style="width: 80px; height: 80px"
              @click="lookImage(index)"
            />
            <view class="del" @tap="delPhoto(index)">X</view>
          </view>
          <view @tap="takePhoto()" class="photo">
            <img :src="uploaderImg" alt="" style="width: 80px; height: 80px" />
          </view>
        </view>
      </view>
    </view>
    <SignInOut
      type="signIn"
      :signStatus="state.status"
      @refresh-location="refreshLocation"
      @sign="signIn"
      :signAddress="state.params.signInAddress"
      :distance="state.params.signInDistance"
      :institutionName="state.params.institutionName"
      :insLonNoFlag="state.insLonNoFlag"
      @calibration="calibration"
    />
  </view>
  <CusPopup ref="cusPopupRef" @select-cus="selectCus" />
  <VisitTypePopup
    @visit-type="visitTypeSelect"
    ref="visitTypePopupRef"
  ></VisitTypePopup>
  <nut-image-preview
    :show="state.showPreview"
    :images="state.imgData"
    :init-no="initNo"
    @close="state.showPreview = false"
    style="width: 100%; height: 100%"
  />
  <CalibrationDialog
    ref="calibrationDialogRef"
    @selectLocation="selectLocation"
  />
</template>

<script setup>
import { onMounted, reactive, ref, onUnmounted, onDeactivated } from "vue";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import SignTime from "./components/signTime";
import CellClick from "../../../pages/components/cellClick/index.vue";
import SignInOut from "./components/signInOut";
import CusPopup from "../components/cusPopup";
import VisitTypePopup from "../components/visitTypePopup";
import {
  validIsNotSignOut,
  visitDistance,
  sign,
  crmSinInM,
} from "../../../api/institutionalVisitsApi";

import { currentTime } from "../../../utils/content.js";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { apiAuth } from "../../../utils/feishuAuth";

import { Photograph } from "@nutui/icons-vue-taro";
import uploaderImg from "../../../images/uploader.png";
import topology from "../../../images/<EMAIL>";
import { baseApi } from "../../../utils/content";
import CalibrationDialog from "../../components/calibrationDialog.vue";
const initNo = ref(0);
const TokenKey = Taro.getStorageSync("access_token");
const router = useRouter();
const query = router.params;
const cusPopupRef = ref(null);
const visitTypePopupRef = ref(null);
const initUserInfo = Taro.getStorageSync("initUserInfo").user;
const photoList = ref([]);
const calibrationDialogRef = ref(null);
const state = reactive({
  // 拜访类型字段
  status: "", // 0 定位失败 1定位成功
  params: {
    isPlan: "0", //根据拜访状态判断,
    signType: "0",
    visitTypeCode: "0",
    visitTypeName: "院内拜访",
    signInLat: "",
    signInLon: "",
    institutionCode: "",
    institutionName: "",
    customerName: "",
    customerCode: "",
    insDeptName: "",
    signInDistance: 0,
    signInAddress: "",
    insLat: null,
    insLon: null,
  },
  insLonNoFlag: false,
  showPreview: false,
  imgData: [],
  calibrationData: {},
});
const selectCusClick = () => {
  if (state.id) return;
  cusPopupRef.value.open();
};

const selectCus = (obj) => {
  state.params.institutionCode = obj.insCode;
  state.params.institutionName = obj.insName;
  state.params.customerName = obj.customerName;
  state.params.customerCode = obj.customerCode;
  state.params.insDeptName = obj.insDeptName;
  state.params.insLat = obj.latitude;
  state.params.insLon = obj.longitude;
  state.params.jurCode = obj.jurCode;
  state.params.address = obj.address;
  state.params.insLabel = obj.insLabel;
  state.calibrationData = obj;
  getLocation();
};
const chooseImage = () =>
  new Promise((res, rej) => {
    try {
      tt.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (data) => {
          console.log(data, "--data");
          const fileSystemManager = tt.getFileSystemManager();
          fileSystemManager.readFile({
            filePath: data.tempFilePaths?.[0],
            encoding: "base64",
            success(res_v1) {
              const imageType = "image/png";
              const base64ImageURL = `data:${imageType};base64,${res_v1?.data}`;
              res({ url: base64ImageURL, type: "FS" });
            },
            fail(res) {
              console.log(`readFile fail: ${JSON.stringify(res)}`);
            },
          });
        },
        fail: (err) => rej(err),
      });
    } catch (err) {
      console.log("!ttt");
      Taro.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (data) => {
          console.log(data, "---dddd");
          res({ url: data?.tempFilePaths?.[0], type: "H5" });
        },
        fail: (err) => rej(err),
      });
    }
  });
const takePhoto = async () => {
  try {
    const { url, type } = await chooseImage();
    Taro.showLoading({
      mask: true,
      title: "上传中",
    });
    addWatermark(url, type).then((files) => {
      Taro.uploadFile({
        url: process.env.TARO_APP_API + `${baseApi}oss-file/upload`,
        header: {
          clientid: process.env.TARO_APP_CLIENT_ID,
          Authorization: `Bearer ${TokenKey}`,
        },
        filePath: files,
        name: "file",
        fail: () => {
          Taro.hideLoading();
          console.log("上传失败");
        },
        success: (result) => {
          Taro.hideLoading();
          try {
            console.log("上传成功：", JSON.parse(result.data).data);
            photoList.value.push(JSON.parse(result.data).data);
            state.imgData = photoList.value.map((item) => {
              return {
                src: item.url,
              };
            });
          } catch (e) {
            console.log(e);
          }
        },
      });
    });
  } catch (err) {}
};

const lookImage = (i) => {
  initNo.value = i;
  state.showPreview = true;
};
const dayTime = () => {
  const now = new Date();

  // 格式化日期和时间
  const year = now.getFullYear(); // 年
  const month = now.getMonth() + 1; // 月，getMonth() 返回的月份是从 0 开始的
  const day = now.getDate(); // 日
  const hours = now.getHours(); // 时
  const minutes = now.getMinutes(); // 分
  const seconds = now.getSeconds(); // 秒

  // 将月、日、时、分、秒转换为两位数格式
  const formattedMonth = month < 10 ? `0${month}` : month;
  const formattedDay = day < 10 ? `0${day}` : day;
  const formattedHours = hours < 10 ? `0${hours}` : hours;
  const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;

  // 组合成 "年-月-日 时：分：秒" 格式的字符串
  return {
    Y: `${year}/${formattedMonth}/${formattedDay}`,
    H: `${formattedHours}:${formattedMinutes}`,
  };
};
const addWatermark = (imageSrc, type = "") => {
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      canvas.width = image.width;
      canvas.height = image.height;
      ctx.drawImage(image, 0, 0);
      ctx.font = type == "FS" ? "30px Arial" : "80px Arial";
      ctx.fillStyle = "white";
      ctx.fillText(dayTime().Y, canvas.width - (type == "FS" ? 200 : 500), 200);
      ctx.fillText(dayTime().H, canvas.width - (type == "FS" ? 200 : 500), 300);
      try {
        if (initUserInfo?.nickName && initUserInfo?.userName)
          ctx.fillText(
            initUserInfo.nickName + "-" + initUserInfo.userName,
            20,
            canvas.height - 300
          );
        if (state.params?.signInAddress)
          ctx.fillText(state.params.signInAddress, 20, canvas.height - 200);
      } catch (err) {}
      canvas.toBlob(
        function (newBlob) {
          resolve(URL.createObjectURL(newBlob));
        },
        "image/jpeg",
        0.5
      );
    };
    image.onerror = reject;
    image.src = imageSrc;
  });
};
const dataURLToBlob = (dataURL) => {
  // 将base64数据转换为二进制字符串
  var base64 = dataURL.split(",")[1];
  var mimeString = dataURL.split(",")[0].split(":")[1].split(";")[0];
  var binaryString = atob(base64);
  var len = binaryString.length;
  var bytes = new Uint8Array(len);
  for (var i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // 创建Blob对象
  return new Blob([bytes], { type: mimeString });
};

const delPhoto = (index) => {
  photoList.value.splice(index, 1);
  state.imgData = photoList.value.map((item) => {
    return {
      src: item.url,
    };
  });
};
const selectTypeClick = () => {
  if (state.id) return;
  visitTypePopupRef.value.open();
};
const visitTypeSelect = (name, code) => {
  state.params.visitTypeName = name;
  state.params.visitTypeCode = code;
};

const signIn = async () => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  if (
    state.crmSinInMInfo.remark === "1" &&
    Number(state.params.signInDistance) >
      Number(state.crmSinInMInfo.configValue)
  ) {
    Taro.hideLoading();
    return Taro.showModal({
      content: state.crmSinInMInfo.configName,
      confirmText: "确定",
      duration: 2000,
      showCancel: false,
    });
  }
  const parmas = {
    visitUserHrCode: initUserInfo.userName,
    visitLevel: "1",
  };
  const { data } = await validIsNotSignOut(parmas);
  if (data) {
    Taro.hideLoading();
    return Taro.showModal({
      content: `您有正进行的拜访，请先结束拜访`,
      confirmText: "去结束",
      success: async function (res) {
        if (res.confirm) {
          Taro.navigateTo({
            url: `/pages/institutionalVisits/visiting?id=${data}`,
          });
        } else if (res.cancel) {
          console.log("用户点击取消");
        }
      },
    });
  }

  const signInPhotoList = photoList.value.map((item) => {
    return {
      attUrl: item.originalUrl,
      ossId: item.ossId,
    };
  });
  const params = {
    ...state.params,
    signInPhotoList,
  };
  const res = await sign(params);
  if (res.code === 200) {
    Taro.showToast({
      title: "签到成功",
      icon: "none",
      duration: 2000,
    });
    if (query.source) {
      Taro.redirectTo({
        url: `/pages/institutionalVisits/visiting?id=${res.data.visitInstitutionId}&source=customer`,
      });
    } else {
      Taro.redirectTo({
        url: `/pages/institutionalVisits/visiting?id=${res.data.visitInstitutionId}`,
      });
    }
  } else {
    Taro.hideLoading();
    if (res?.msg)
      Taro.showToast({
        title: res?.msg,
        icon: "none",
      });
  }
};

const refreshLocation = () => {
  getLocation("refre");
};

const getDistance = (type) => {
  if (state.params.insLon && state.params.insLon != "0.0") {
    const params = {
      origins: `${state.params.signInLon},${state.params.signInLat}`,
      destination: `${state.params.insLon},${state.params.insLat}`,
    };

    visitDistance(params)
      .then((res) => {
        if (res.code === 200 && res?.data.length != 0) {
          state.params.signInDistance = res?.data?.[0];
          state.status = "1";
          if (type) {
            Taro.showToast({
              title: "刷新成功",
              icon: "none",
              duration: 2000,
            });
          }
        } else {
          state.status = "1";
          Taro.showToast({
            title: "获取机构位置与当前位置距离失败",
            icon: "none",
            duration: 2000,
          });
        }
      })
      .catch((res) => {
        state.status = "1";
        Taro.showToast({
          title: "获取机构位置与当前位置距离失败",
          icon: "none",
          duration: 2000,
        });
      });
  } else {
    state.status = "1";
    state.params.signInDistance = 0;
    state.insLonNoFlag = true;
  }
};
const getConvert = (longitude, latitude) =>
  new Promise((res) => {
    Taro.request({
      url: `https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${longitude},${latitude}&key=0446b950e065d1e0b98a9e97f08dfbff&coordsys=gps`,
      success: (result_before) => {
        const [lon, lat] = result_before.data.locations.split(",");
        res({ lon, lat });
      },
    });
  });

const getLocation = (type = "", isSign = true) => {
  if ("geolocation" in navigator) {
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const latitude = position.coords.latitude; // 纬度
        const longitude = position.coords.longitude; // 经度
        const { lon, lat } = await getConvert(longitude, latitude);
        await Taro.request({
          url: `https://restapi.amap.com/v3/geocode/regeo?location=${lon},${lat}&key=0446b950e065d1e0b98a9e97f08dfbff`,
          success: (result) => {
            state.params.signInAddress =
              result.data.regeocode.formatted_address;
            state.params.signInLat = latitude;
            state.params.signInLon = longitude;
            if (isSign) getDistance(type);
          },
          fail: (res) => {
            Taro.showToast({
              title: "定位失败",
              icon: "none",
              duration: 2000,
            });
            state.status = "0";
          },
        });
      },
      function (error) {
        Taro.showToast({
          title: "定位失败",
          icon: "none",
          duration: 2000,
        });
        state.status = "0";
      },
      {}
    );
  } else {
    Taro.showToast({
      title: "请开启定位权限",
      icon: "none",
      duration: 2000,
    });
  }
};
let timer = null;

const getCrmSinInM = async () => {
  const res = await crmSinInM();
  state.crmSinInMInfo = res.rows[0] || {};
};

const calibration = () => {
  calibrationDialogRef.value.open(state.calibrationData);
};

const selectLocation = (location) => {
  console.log("定位校准", location);
  state.insLonNoFlag = false;
  state.params.insLat = location.location.lat.toString();
  state.params.insLon = location.location.lng.toString();
  getDistance();
};
onMounted(async () => {
  Taro.setNavigationBarTitle({ title: "拜访签到" });
  apiAuth();
  getCrmSinInM();
  if (query.source) {
    const customerQuery = Taro.getStorageSync("customerQuery");
    state.params.institutionCode = customerQuery.insCode;
    state.params.institutionName = customerQuery.insName;
    state.params.customerName = customerQuery.customerName;
    state.params.customerCode = customerQuery.customerCode;
    state.params.insDeptName = customerQuery.insDeptName;
    state.params.insLat = customerQuery.latitude;
    state.params.insLon = customerQuery.longitude;
    state.params.jurCode = customerQuery.jurCode;
    state.params.address = customerQuery.address;
    state.params.insLabel = customerQuery.insLabel;
    state.calibrationData = customerQuery;
    Taro.removeStorageSync("customerQuery");
    getLocation();
  }
  getLocation("", false);
});

useDidShow(() => {
  state.imgData = photoList.value.map((item) => {
    return {
      src: item.url,
    };
  });
});

onUnmounted(() => {
  Taro.removeStorageSync("photos");
});
</script>

<style lang="scss">
.sign .nut-cell__value {
  font-size: 16px;
}
.signIn {
  color: #1d212b;
  padding-top: 16px;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC-Regular;

  .sign-select {
    margin: 12px;
    padding: 16px;
    padding-top: 2px;
    margin-top: 8px;
    background: #fff;
    border-radius: 8px;
    .photograph {
      //display: flex;
      //justify-content: space-between;
      .photo-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .photo {
          width: 80px;
          height: 80px;
          position: relative;
          overflow: hidden;
          .del {
            position: absolute;
            font-size: 12px;
            right: -12px;
            top: -10px;
            width: 24px;
            height: 24px;
            background: black;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            color: #fff;
            padding-top: 7px;
            padding-right: 7px;
          }
          img {
            width: 100%;
          }
        }
      }
    }
  }
  .nut-button--plain.nut-button--primary {
    border-color: #e5e6eb;
  }
}
</style>
