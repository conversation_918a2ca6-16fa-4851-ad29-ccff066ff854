<template>
  <view class="addCus">
    <view>
      <view class="section">
        <view class="cusTitle">基本信息</view>
        <nut-form
          class="form"
          :label-width="200"
          :model-value="state.params"
          ref="ruleForm"
        >
          <FormItem
            label="申请人"
            propValue="applicant"
            required
            :readonly="state.infoDisabled"
            v-model="state.params.applicant"
            :rules="state.rules.applicant"
          />
          <!-- <FormItem
            ref="insRef"
            label="机构名称"
            propValue="insName"
            labelWidth="80"
            required
            v-model="state.params.insName"
            @search="searchIns"
            @current:radio="setInsCode"
            placeholder="请选择"
            needSearch
            :popupOption="state.options.insName"
            :rules="state.rules.insName"
          /> -->

          <nut-form-item
            label="部门"
            prop="deptName"
            :label-width="100"
            required
            :rules="[{ required: true, message: '必填' }]"
          >
            <nut-input
              clearable
              input-align="right"
              :border="false"
              readonly
              @click="selectClick('postName')"
            >
              <template #right>
                <view
                  @click="selectClick('postName')"
                  style="font-size: 16px; text-align: right"
                  ><text v-if="state.params.deptName">{{
                    state.params.deptName
                  }}</text
                  ><text v-if="!state.params.deptName" style="color: #c9cdd0"
                    >新增客户，审批通过后自动认领该客户到选择辖区下</text
                  ></view
                >
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <FormItem
            label="岗位"
            propValue="postName "
            readonly
            placeholder=" "
            v-model="state.params.postName"
            :rules="state.rules.postName"
          />
          <!-- <FormItem
            label="岗位"
            propValue="postName"
            required
            :readonly="state.infoDisabled"
            v-model="state.params.postName"
            :rules="state.rules.postName"
          />
          <FormItem
            label="部门"
            propValue="ancestors"
            required
            labelWidth="50"
            type="ellipsis"
            :readonly="state.infoDisabled"
            v-model="state.params.ancestors"
          /> -->
          <FormItem
            label="客户名称"
            propValue="customerName"
            required
            v-model="state.params.customerName"
            :rules="state.rules.customerName"
          />
          <FormItem
            ref="insRef"
            label="机构名称"
            propValue="insName"
            labelWidth="80"
            loadingBrfore
            required
            v-model="state.params.insName"
            @search="searchIns"
            @current:radio="setInsCode"
            :popupRadioDisabled="state.popupRadioDisabled"
            :popupRadioDisabledCallBack="popupRadioDisabledCallBack"
            placeholder="请选择"
            needSearch
            :popupOption="state.options.insName"
            :rules="state.rules.insName"
          />
          <FormItem
            label="机构编码"
            propValue="insMdmCode"
            readonly
            placeholder=" "
            v-model="state.params.insMdmCode"
          />
          <FormItem
            label="主执业点"
            propValue="masterFlag"
            v-model="state.params.masterFlag"
            placeholder="请选择"
            required
            needPopup
            :rules="state.rules.masterFlag"
            :popupOption="state.options.masterFlag"
          />
          <FormItem
            label="标准科室"
            propValue="insDeptName"
            required
            v-model="state.params.insDeptName"
            :rules="state.rules.insDeptName"
            placeholder="请选择"
            needPopup
            :popupOption="state.options.insDeptName"
          />
          <!-- <FormItem
            label="科室标签"
            propValue="insDeptTag"
            v-model="state.params.insDeptTag"
          /> -->
          <!-- placeholder="请选择"
            needPopup
            :popupOption="state.options.insDeptTag" -->
          <FormItem
            label="职称"
            propValue="professionTechTitle"
            required
            v-model="state.params.professionTechTitle"
            :rules="state.rules.professionTechTitle"
            placeholder="请选择"
            needPopup
            :popupOption="state.options.professionTechTitle"
          />
          <FormItem
            label="行政级别"
            propValue="administrationLevel"
            required
            :rules="state.rules.administrationLevel"
            v-model="state.params.administrationLevel"
            placeholder="请选择"
            needPopup
            :popupOption="state.options.administrationLevel"
          />
          <!-- <FormItem
            label="省份"
            propValue="district"
            v-model="state.params.district"
            placeholder="请选择"
            needPopup
            type="city"
          /> -->
          <FormItem
            label="性别"
            propValue="sex"
            v-model="state.params.sex"
            placeholder="请选择"
            needPopup
            :popupOption="state.options.sex"
          />
          <FormItem
            label="备注"
            type="textarea"
            propValue="remark"
            v-model="state.params.remark"
          />
        </nut-form>
      </view>
      <FooterButton
        v-if="query.type !== 'masterFlag'"
        text="提交申请"
        @click-button="clickRight"
        :isLoading="state.subLoading"
      />
      <FooterButtonTwo
        v-else
        rightText="确定"
        @click-left="clickLeft"
        @click-right="clickRight"
        :isLoading="state.subLoading"
        leftText="取消"
      />
    </view>
    <UserPopup ref="userPopupRef" @select-user="selectUser" />
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import FooterButtonTwo from "../../../pages/components/footerButtonTwo/footerButtonTwo";

import { reactive, ref } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { institutionList } from "../../../api/area.js";
import { initiateCust } from "../../../api/area.js";
import { getPostCode } from "../../../utils/area.js";
import FormItem from "../../../pages/components/formItem/index.vue";
import UserPopup from "../../../pages/area/components/userPopup.vue";
import arrow from "../../../images/arrow.png";
import { appCode, tenantId } from "../../../utils/content";
const router = useRouter();
const query = router.params;
const ruleForm = ref(null);
const insRef = ref(null);
const userPopupRef = ref(null);
const state = reactive({
  popupRadioDisabled: false,
  rules: {
    administrationLevel: [{ required: true, message: "必填" }],
    masterFlag: [{ required: true, message: "必填" }],
    applicant: [{ required: true, message: "必填" }],
    deptName: [{ required: true, message: "必填" }],
    customerName: [{ required: true, message: "必填" }],
    insName: [{ required: true, message: "必填" }],
    insDeptName: [{ required: true, message: "必填" }],
    professionTechTitle: [{ required: true, message: "必填" }],
    insDeptTag: [{ required: true, message: "必填" }],
    deptName: [{ required: true, message: "必填" }],
  },
  options: {
    insLevel: [],
    insDeptName: [],
    professionTechTitle: [],
    administrationLevel: [],
    insDeptTag: [],
    insName: [],
    sex: [
      { dictLabel: "男", dictValue: "男" },
      { dictLabel: "女", dictValue: "女" },
    ],
    masterFlag: [
      { dictLabel: "是", dictValue: "是" },
      { dictLabel: "否", dictValue: "否" },
    ],
  },
  params: {
    applicant: "",
    postName: "",
    deptName: "",
    type: "insert",
    applicantCode: "",
    postCode: "",
    deptCode: "",
    ancestors: "",
    postIdList: [],
    insDeptTag: "",
    insName: "",
    district: "",
    postName: "",
    deptName: "",
    postCode: "",
    customerName: "",
    insName: "",
    insMdmCode: "",
    masterFlag: "",
    insDeptName: "",
    professionTechTitle: "",
    administrationLevel: "",
    sex: "",
    remark: "",
    insCode: "",
    jurCode: "",
  },
  masterValue: query.type === "add" ? "是" : "否",
  type: "",
  subLoading: false,
});

const clickRight = () => {
  ruleForm.value.validate().then(async ({ valid, errors }) => {
    if (valid) {
      state.subLoading = true;
      const applicantInfo = {
        applicant: state.params.applicant,
        applicantCode: state.params.applicantCode,
        postCode: state.params.postCode,
        postName: state.params.postName,
        deptCode: state.params.deptCode,
        deptName: state.params.deptName,
        ancestors: state.params.ancestors,
        postIdList: state.params.postIdList,
      };
      const params = {
        applyContent: {
          ...state.params,
        },
        ...applicantInfo,
        customerCode: state.params.customerCode || "",
        enableWorkflow: true,
        customerName: state.params.customerName,
        insCode: state.params.insCode,
        insName: state.params.insName,
        insMdmCode: state.params.insMdmCode,
        appCode: appCode(),
        tenantId: tenantId,
        applyType:
          query.type === "add" ? "2" : query.type === "masterFlag" ? "3" : "",
      };
      const res = await initiateCust(params);
      if (res.msg)
        Taro.showToast({
          title: res.msg,
          icon: "none",
          duration: 2000,
        });
      if (res.code === 200) {
        Taro.reLaunch({
          url: `/pages/customer_manage/index?currentTab=1`,
        });
      }
      state.subLoading = false;
      console.log("success", formData);
    } else {
      console.log("error submit!!", errors);
    }
  });
};

const clickLeft = () => {
  Taro.navigateBack({
    delta: 1,
  });
};
const popupRadioDisabledCallBack = () => {
  return Taro.showToast({
    title: "请先选择部门",
    icon: "none",
    duration: 2000,
  });
};
const searchIns = async (insName = "") => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  // if (insName.length != 0) {
  const res = await institutionList(1, 100, {
    insName,
    jurCodeList: state.params.jurCode.split(","),
  });
  state.options.insName = res?.data?.rows?.map((el) => ({
    ...el,
    dictLabel: el.insName,
    dictValue: el.insName,
  }));
  insRef.value.selectSearch(state.options.insName);
  Taro.hideLoading();

  // }
};

const setInsCode = (v) => {
  state.params.insCode = v.insCode;
  state.params.insMdmCode = v.insMdmCode;
};
const initIns = async () => {
  const res = await institutionList({
    pageNum: 1,
    pageSize: 100,
    jurCodeList: getPostCode(),
  });
  state.options.insName = res?.data?.rows?.map((el) => ({
    ...el,
    dictLabel: el.insName,
    dictValue: el.insName,
  }));
};
const dictInit = () => {
  state.options.insLevel = Taro.getStorageSync("insLevel");
  state.options.insDeptName = Taro.getStorageSync("deptNameList");
  state.options.professionTechTitle = Taro.getStorageSync("professionList");
  state.options.administrationLevel = Taro.getStorageSync("administrationList");
  state.options.insDeptTag = Taro.getStorageSync("departmentLabelList");
  // initIns();
};
const initBase = () => {
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  const orgList =
    initHrInfo?.orgList?.filter((item) => item.postType === "1")[0] || [];
  state.params.applicant = initHrInfo?.empName;
  // state.params.postName = orgList?.name;
  // state.params.deptName = orgList?.value;
  state.params.applicantCode = initHrInfo?.empCode;
  // state.params.postCode = orgList?.code;
  // state.params.deptCode = orgList?.deptCode;
  state.params.ancestors = orgList?.ancestors;
  state.params.postIdList = initHrInfo?.postIdList || [];
};
const selectClick = () => {
  userPopupRef.value.open(state.params.postCode);
};
const selectUser = (item) => {
  state.params.ancestors = item.ancestors;
  state.params.deptName = item.deptName;
  state.params.deptCode = item.deptCode;
  state.params.postCode = item.postCode;
  state.params.postName = item.postName;
  state.params.jurCode = item.jurCode;
  state.popupRadioDisabled = true;
  state.params.insName = "";
  state.params.insMdmCode = "";
  state.params.insCode = "";
};

useDidShow(() => {
  dictInit();
  if (query.type === "add") {
    Taro.setNavigationBarTitle({ title: "新增客户申请" });
    state.params = {
      customerName: decodeURIComponent(query.customerName),
    };
  } else if (query.type === "update") {
    Taro.setNavigationBarTitle({ title: "变更客户申请" });
    state.params = {
      ...JSON.parse(decodeURIComponent(query.item)),
      ...JSON.parse(decodeURIComponent(query.ins)),
    };
  } else {
    Taro.setNavigationBarTitle({ title: "添加执业点" });
    state.params = {
      ...JSON.parse(decodeURIComponent(query.item)),
      ...JSON.parse(decodeURIComponent(query.ins)),
      masterFlag: "否",
    };
  }
  initBase();
});
</script>
<style lang="scss" scoped>
.cusTitle {
  height: 48px;
  line-height: 48px;
  font-size: 14px;
  padding: 0 16px;
  color: #869199;
}
.arrowIcon {
  width: 16px;
  height: 16px;
  margin-top: 2px;
}
.input-text::placeholder {
  // color: red !important; /* 设置placeholder的文本颜色 */
  // text-align: right;
  // padding-right: 10px;
  // font-size: 14px; /* 设置placeholder的字体大小 */
}
.nut-cell__title {
  // font-size: 16px !important;
}
.nut-cell-group__wrap {
  margin-top: 0;
  padding: 0 20px !important;
  box-shadow: none;
}
.addCus {
  padding-bottom: 90px;
  background: #fff;
  .section {
    font-size: 14px;

    .label {
      color: #1d212b;
      &::before {
        content: "*";
        color: red;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }
  .nut-cell__title {
    color: #1d212b;
  }
  .nut-input-left-box {
    margin-right: 10px;
  }
  .nut-input {
    padding: 0;
    border-bottom: none;
  }
}
.input-text::placeholder {
  color: #c9cdd0;
  word-break: break-all;
}
</style>
