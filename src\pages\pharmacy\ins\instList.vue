<template>
  <view class="pharmacy-insList">
    <view class="title">共{{ state.total }}家药店</view>
    <scroll-view
      style="height: 500px"
      :scroll-y="true"
      :scroll-top="state.scrollTop"
      @scrolltolower="onScrollToLower"
    >
      <view
        style="
          position: absolute;
          top: 28%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!state.insList.length && scrollMyFlag"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>

      <view v-for="item in state.insList" :key="item.jurId" class="instItem">
        <view
          @click="gotoInst(item)"
          style="
            display: flex;
            flex-direction: column;
            padding: 16px 16px 12px 16px;
          "
        >
          <view
            style="display: flex; flex-direction: row; align-items: flex-start"
          >
            <view
              style="
                font-weight: 500;
                font-size: 18px;
                color: #1d212b;
                margin-left: 4px;
                max-width: 260px;
                -webkit-line-clamp: 2; /* 显示的行数 */
                -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
                overflow: hidden; /* 隐藏超出的内容 */
                text-overflow: ellipsis;
              "
            >
              {{
                item.dsName.length > 21
                  ? item.dsName.substring(0, 21) + "..."
                  : item.dsName
              }}
              <nut-tag
                plain
                color="#94BFFF"
                text-color="#2551F2"
                style="margin-top: -4px; vertical-align: middle"
              >
                {{ item.dsMdmCode }}
              </nut-tag></view
            >

            <view style="margin-left: auto">
              <img :src="arrowRight" style="width: 15px; height: 17px"
            /></view>
          </view>

          <view class="address">
            <img :src="location_a" alt="" class="icons" v-if="item.address" />
            <view> {{ item.address }}</view>
          </view>

          <view class="address" style="margin-top: 4px">
            <img :src="user" alt="" class="icons" />
            {{ item.respEmpName }}
          </view>
        </view>
      </view>
      <view
        v-if="state.insList.length > 0 && state.insList.length == state.total"
        style="color: #869199; font-size: 16px; text-align: center"
        >已全部加载完毕</view
      >
    </scroll-view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { defineEmits, defineProps, ref, reactive, defineExpose } from "vue";
import arrow from "@/images/arrow.png";
import arrowRight from "@/images/arrow-right.png";
import address from "@/images/location_a.png";
import hospital from "@/images/iconhospital.png";

import { drugstoreList } from "@/api/pharmacy.js";
import { Clock } from "@nutui/icons-vue-taro";
import location_a from "@/images/ph_address.png";
import user from "@/images/user_3.png";
import { getPostIdList } from "@/utils/pharmacy";
import noData from "@/images/no-data.png";
const emit = defineEmits(["changePcdvalue"]);
const scrollMyFlag = ref(false);
const initHrInfo = Taro.getStorageSync("initHrInfo");
const props = defineProps({
  tab: {
    type: String,
    default: "",
  },
});

const state = reactive({
  scrollTop: 0,
  pageNum: 1,
  pageSize: 10,
  params: {
    dsName: "",

    provinceCode: "",
    cityCode: "",
    districtCode: "",
    postIdStr: getPostIdList(),
    identity: initHrInfo.identity,
  },
  total: 0,
  pcdvalue: "",
  insList: [],
});

const getinsList = () => {
  Taro.showToast({
    title: "加载中",
    icon: "loading",
  });

  drugstoreList(state.pageNum, state.pageSize, state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.total;
        if (!state.insList.length) {
          console.log("33333", res.rows);
          state.insList = res.rows || [];
        } else {
          state.insList = [...state.insList, ...res.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = true;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      Taro.hideLoading();
    });
};

const topSearch = (search) => {
  state.params.dsName = search;
  state.total = 0;
  state.pageNum = 1;
  state.insList = [];
  getinsList();
};

const insFilters = () => {
  const info = {
    province: state.params.provinceCode,
    city: state.params.cityCode,
    district: state.params.districtCode,
    pcdvalue: state.pcdvalue,
  };
  Taro.navigateTo({
    url: `/pages/area/ins/insFilters?info=${JSON.stringify(info)}`,
  });
  // Taro.navigateTo({
  //   url: `/pages/area/ins/insFilters?province=${state.params.province}&city=${state.params.city}&district=${state.params.district}&pcdvalue=${state.pcdvalue}`,
  // });
};
const onScrollToLower = () => {
  if (scrollMyFlag.value && state.insList.length < state.total) {
    state.pageNum++;
    scrollMyFlag.value = false;
    getinsList();
  }
};

const initInsList = () => {
  const insFilters = Taro.getStorageSync("insFilters");
  console.log("insFilters", insFilters);
  if (insFilters) {
    state.params.provinceCode = insFilters.province;
    state.params.cityCode = insFilters.city;
    state.params.districtCode = insFilters.district;
    state.pcdvalue = insFilters.pcdvalue;
    Taro.removeStorageSync("insFilters");
  }
  state.total = 0;
  state.pageNum = 1;
  state.insList = [];
  getinsList();
  emit("change-pcdvalue", state.pcdvalue);
};

const gotoInst = (item) => {
  Taro.navigateTo({
    url: `/pages/pharmacy/components/pharmacyDetail?id=${item.id}&type=ins&address=${item.address}`,
  });
};
defineExpose({
  topSearch,
  insFilters,
  initInsList,
});
</script>
<style lang="scss">
.pharmacy-insList {
  padding: 0px 0px;
  width: 100vw;
  // height: 630px;
  font-family: PingFang SC, PingFang SC;
  .title {
    color: #869199;
    font-size: 14px;
    padding: 18px 12px 8px 12px;
  }
  .icons {
    width: 14px;
    height: 14px;
    display: block;
    margin-right: 4px;
    margin-top: 4px;
  }
  .instItem {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    margin-left: 14px;
    margin-right: 14px;
  }
  .address {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    font-size: 14px;
    color: #869199;
  }
}
</style>
