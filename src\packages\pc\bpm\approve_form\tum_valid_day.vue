<template>
  <div class="approve-text">
    <div class="approve-info-content">
      <div>
        <div class="text-[#1D212B] text-[14px] py-[10px]">基本信息</div>
        <div>
          <div class="flex items-center mb-[8px]">
            <div class="text-[#869199] text-[14px] font-400 w-[200px]">时间</div>
            <div class="text-[#1D212B] text-[14px] font-400">{{ props.formData.startTime}}{{ `-${props.formData.endTime}`  }}</div>
          </div>
          <div class="flex items-center mb-[8px]">
            <div class="text-[#869199] text-[14px] font-400 w-[200px]">天数</div>
            <div class="text-[#1D212B] text-[14px] font-400">{{ props.formData.askDay }}</div>
          </div>
          <div class="flex items-center mb-[8px]">
            <div class="text-[#869199] text-[14px] font-400 w-[200px]">原因</div>
            <div class="text-[#1D212B] text-[14px] font-400">{{ props.formData.reason }}
            </div>
          </div>
        </div>
      </div>

    
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  formData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})


</script>
<style lang="scss" scoped>
</style>
