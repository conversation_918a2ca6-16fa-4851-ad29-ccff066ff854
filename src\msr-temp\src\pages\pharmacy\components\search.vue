<template>
  <view class="search-phy">
    <view class="section">
      <nut-input
        v-model="state.params.dsName"
        placeholder="请输入"
        clearable
        input-align
        :border="false"
        :formatter="formatter"
      >
        <template #left>
          <view class="label">药店名称</view>
        </template>
      </nut-input>
      <view class="footer-button">
        <nut-row type="flex" justify="center">
          <nut-col :span="24">
            <nut-button
              color="#2551F2"
              @click="clickSearchButton"
              shape="square"
              block
              :disabled="!state.params.dsName"
              >搜索</nut-button
            >
          </nut-col>
        </nut-row>
      </view>
    </view>
    <view class="result-text" style="font-size: 16px">
      <scroll-view
        style="height: 500px"
        :scroll-y="true"
        :scroll-top="state.scrollTop"
        @scrolltolower="onScrollToLower"
      >
        <view class="result" v-if="num > 0 && !state.loading">
          <view style="color: #2551f2">查询结果如下</view>
          <view style="color: #869199; font-size: 12px"
            >若没有查询到结果，请点击【新增药店】</view
          >
        </view>
        <view v-for="item in state.insList" :key="item.dsCode" class="instItem">
          <view style="text-align: left; display: flex; align-items: center; justify-content: space-between; padding:0 10px">
            <view style="display: flex; justify-content: start; align-items: center; gap: 4px; margin-top: 10px">
              <view class="insName">{{ item.dsName }}              <view v-show="item.dsMdmCode" style="font-size: 12px; color: #2551f2; border: 1px solid #2551f2; padding: 0 4px; display: inline-block; height: 20px; line-height: 20px;">{{ item.dsMdmCode }}</view></view>

            </view>
            <view  class="righeItem"  @click="showTip()" v-if="state.params.dsName.replace(/\s/g, '') === item.dsName">关联药店</view>
          </view>
          <view style="text-align: left; padding: 0 10px; color: #999999; font-size: 14px; margin-bottom: 4px">{{item.province}}{{item.city}}{{item.district}}{{item.address}}</view>
        </view>
        <view
          v-if="state.insList.length > 0 && state.insList.length == state.total"
          style="color: #869199; font-size: 16px; text-align: center"
          >已全部加载完毕</view
        >
      </scroll-view>
    </view>

    <FooterButton
      text="新增药店"
      @click-button="clickAddButton"
      :buttonIcon="arrowRight"
      v-if="num !== 0 || state.params.dsName"
      :disabled="num === 0 || !state.params.dsName"
    >
      <template #icon>
        <Uploader color="#fff" />
      </template>
    </FooterButton>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { onMounted, reactive, watch, ref } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { Uploader } from "@nutui/icons-vue-taro";
import arrowRight from "../../../images/arrow-right.png";

import { formatter } from "../../../utils/customer.js";
import { selectNoBindingList } from "../../../api/pharmacy.js";
import {
  getPostCode,
  getInsLevelColor,
  getInsLevelTextColor,
} from "../../../utils/area.js";
const initHrInfo = Taro.getStorageSync("initHrInfo");
const router = useRouter();
const scrollMyFlag = ref(false);
const num = ref(0);
const query = router.params;
const state = reactive({
  footerButtontext: "搜索",
  insList: [],
  scrollTop: 0,
  total: 0,
  loading: false,
  params: {
    dsName: "",
    pageNum: 1,
    pageSize: 10,
    // jurCodeList: getPostCode(),
    // bu: initHrInfo.bu,
  },
});

const showTip = () => {
  Taro.showToast({
    title: '关联药店申请暂时先找对应SFE或运营，后续入口开放功能敬请期待',
    icon: 'none'
  })
}

const getinsList = () => {
  state.loading = true;
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  selectNoBindingList(state.params)
    .then((res) => {
      if (res.code == 200) {
        console.log("res.", res);
        state.total = res.total;
        if (!state.insList.length) {
          state.insList = res.rows || [];
        } else {
          state.insList = [...state.insList, ...res.rows];
        }
        console.log("res.", state.insList);
        Taro.hideLoading();
        scrollMyFlag.value = true;
        state.loading = false;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      Taro.hideLoading();
      state.loading = false;
    });
};
const clickSearchButton = () => {
  num.value++;
  if (state.loading) return;
  state.params.pageNum = 1;
  state.total = 0;
  state.insList = [];
  state.params.dsName = state.params.dsName.replace(/\s/g, '')
  getinsList();
};

const clickAddButton = () => {
  const flag = state.insList.some(item => item.dsName === state.params.dsName)
    if (flag){
    Taro.showModal({
      title: '系统中存在同名药店！',
      content: '不能重复新增，请仔细核对搜索结果',
      confirmText: "我知道了",
      showCancel: false,
      duration: 2000,
    })
  }
  else {
    Taro.navigateTo({
    url: `pages/pharmacy/components/addPharmacy?dsName=${state.params.dsName}`,
  });
    }

};

const gotoInst = (item) => {
  if (item.isClaim === "0") return;
  Taro.navigateTo({
    url: `/pages/pharmacy/components/pharmacyDetail?id=${item.id}&type=ins`,
  });
};
const onScrollToLower = () => {
  if (scrollMyFlag.value && state.insList.length < state.total) {
    state.params.pageNum++;
    scrollMyFlag.value = false;
    getinsList();
  }
};

watch(
  () => state.params.dsName,
  (newValue) => {
    if (!newValue) {
      num.value = 0;
      state.insList = [];
    }
  }
);
onMounted(() => {
  Taro.setNavigationBarTitle({ title: "新增药店" });
});
</script>
<style lang="scss">
.taro-modal__title {
  color: #F32F29;
  font-weight: 500;
  font-size: 16px;

}
.search-phy {
  padding-bottom: 80px;
  .section {
    background: #fff;
    font-size: 16px;
    padding: 0 16px;
  }
      
  .righeItem {
      min-width: 76px;
      height: 32px;
      background: #e8f0ff;
      border-radius: 100px 100px 100px 100px;
      border: 1px solid #e8f0ff;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #2551f2;
      line-height: 33px;
      text-align: center;
      margin: 4px 0;
    }

  .result-text {
    padding-top: 16px;
    text-align: center;
  }
  .result {
    max-height: 400px !important;
    overflow: auto;
    margin-bottom: 8px;
  }
  .instItem {
    background-color: #ffffff;
    margin-bottom: 10px;
    .insName {
      font-weight: 500;
      font-size: 16px;
      color: #1d212b;
      text-align: left;
    }
  }
  .label {
    &::before {
      content: "*";
      color: red;
      margin-top: 6px;
      margin-right: 5px;
      vertical-align: middle;
    }
  }
  .nut-radio-group {
    padding: 0 16px;
  }
  .footer-button {
    background-color: #ffffff;
    padding: 8px 0;
    width: 100%;

    .nut-button {
      border-radius: 4px;
    }
    .border-none {
      border: none;
    }
    .nut-button__wrap {
      img {
        width: 10px;
        height: 10px;
      }
    }
  }
}
</style>
