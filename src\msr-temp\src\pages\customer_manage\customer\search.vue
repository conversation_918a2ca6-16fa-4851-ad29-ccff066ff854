<template>
  <view class="customer_manage_search">
    <view class="section">
      <nut-form
        class="form"
        :label-width="200"
        :model-value="state.params"
        ref="ruleForm"
      >
      <CellClick @selectClick="selectInsName" :value="state.params.insName" :required="true" label="机构名称"
        placeholder="请选择" :phRight="true" />
        <nut-input v-model="state.params.customerName" placeholder="请输入" clearable :border="false" :formatter="formatter"
        input-align="right">
        <template #left>
          <view class="label">客户名称</view>
        </template>
      </nut-input>
      </nut-form>
      <view class="footer-button">
        <nut-row type="flex" justify="center">
          <nut-col :span="24">
            <nut-button
              color="#2551F2"
              @click="clickSearchButton"
              shape="square"
              :loading="state.buttonLoading"
              block
              :disabled="!state.params.insName || !state.params.customerName"
              >搜索</nut-button
            >
          </nut-col>
        </nut-row>
      </view>
    </view>
    <view class="textInfo" v-if="!(num === 0 || !state.params.insName)">
      <view class="titleTextInfo">查询结果如下</view>
      <view class="subTextInfo"
        >请选择客户进行新增与认领, 若检索不到该客户, 请判断</view
      >
      <view class="subTextInfo"
        >是否为其他机构同名客户多点执业, 如是, 请选择</view
      >
      <view class="subTextInfo">对应客户后添加多点执业信息</view>
    </view>
    <view class="result-text" style="font-size: 16px">
      <scroll-view
        style="height: 500px"
        :scroll-y="true"
        :scroll-top="state.scrollTop"
        @scrolltolower="onScrollToLower"
      >
        <view v-for="item in state.insList" :key="item.jurId" class="instItem">
          <SearchItem :item="item" @buttonChanged="buttonChanged" />
        </view>
        <view
          v-if="state.insList.length > 0 && state.insList.length == state.total"
          style="color: #869199; font-size: 16px; text-align: center"
          >已全部加载完毕</view
        >
      </scroll-view>
    </view>

    <FooterButton
      text="申请添加客户"
      :disabled="num === 0 || !state.params.insName"
      @click-button="clickAddButton"
      :buttonIcon="arrowRight"
    >
      <template #icon>
        <Uploader color="#fff" />
      </template>
    </FooterButton>
  </view>
  <SelectIns ref="selectInsRef" @ins-confirm="insConfirm" />
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { onMounted, reactive, watch, ref, computed } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { Uploader } from "@nutui/icons-vue-taro";
import arrowRight from "../../../images/arrow-right.png";
import { customerDetail, custSearch } from "../../../api/area.js";
import { crmCustomerApplyListApi } from "../../../api/customerAPI.js";
import SearchItem from "./searchItem.vue";
import {
  getPostCode,
} from "../../../utils/area.js";
import SelectIns from './selectIns'
import CellClick from "../../../pages/components/cellClick/index.vue";
import { formatter } from "../../../utils/customer.js";
const initHrInfo = Taro.getStorageSync("initHrInfo");
const router = useRouter();
const scrollMyFlag = ref(false);
const num = ref(0);
const ruleForm = ref(null);
const selectInsRef = ref(null);
const query = router.params;
const state = reactive({
  buttonLoading: false,
  options: {
    insName: [],
  },
  rules: {
    customerName: [{ required: true, message: "必填" }],
    insName: [{ required: true, message: "必填" }],
  },
  footerButtontext: "搜索",
  insList: [],
  scrollTop: 0,
  total: 0,
  params: {
    insName: "",
    customerName: "",
    pageNum: 1,
    pageSize: 10,
    insId: "",
    insMdmCode: "",
    insCode: "",
    jurCodeList: getPostCode()?.split(","),
    // bu: initHrInfo?.bu,
  },
});
const intComputer = computed(() => ({
  insId: state.params.insId,
  insCode: state.params.insCode,
  insName: state.params.insName,
  insMdmCode: state.params.insMdmCode,
}));
const getinsList = () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  state.buttonLoading = true;

  crmCustomerApplyListApi(state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.data.total;
        if (!state.insList.length) {
          state.insList = res.data.rows || [];
        } else {
          state.insList = [...state.insList, ...res.data.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = true;
        state.buttonLoading = false;
      } else {
        Taro.hideLoading();
        state.buttonLoading = false;
      }
    })
    .catch(() => {
      Taro.hideLoading();
    });
};
const clickButton = async (item) => {
  let params = {
    jurCode: item.jurCode,
    insCode: item.insCode,
    customerCode: item.customerCode,
    isClaim: "1",
  };
  const res = await customerDetail(params);
  Taro.setStorage({
    key: "detailInfo",
    data: { ...intComputer.value, ...res.data },
  });

  Taro.navigateTo({
    url: `/pages/area/components/editProduct?type=cust`,
  });
};
const jumpClaimProduct = () => {
  Taro.navigateTo({
    url: `/pages/area/components/claimProduct?currentTab=1`,
  });
};

const goToDetail = item => {
  Taro.navigateTo({
    url: `/pages/area/customer/cusDeatil?id=${item.id}&jurCode=${item.jurCode}&insCode=${item.insCode}&customerCode=${item.customerCode}&claiming=${item.claiming}`,
  });
}
// 多点执业
const addCustomer = (item) => {
  console.log(22222, item);
  Taro.navigateTo({
    url: `/pages/area/customer/addCus?customerName=${
      state.params.customerName
    }&type=masterFlag&item=${JSON.stringify(item)}&ins=${JSON.stringify(
      intComputer.value
    )}`,
  });
};
const buttonChanged = (type, item) => {
  if (type == 1) gotoInst(item);
  if (type == 2) clickButton(item);
  if (type == 3) jumpClaimProduct();
  if (type == 4) addCustomer(item);
  if (type == 5) goToDetail(item);
};
const selectInsName = () => {
  selectInsRef.value.open()
}
const clickSearchButton = () => {
      num.value++;
      state.params.pageNum = 1;
      state.total = 0;
      state.insList = [];
      getinsList();
  
};
// 新增客户
const clickAddButton = () => {
  Taro.navigateTo({
    url: `/pages/area/customer/addCus?customerName=${
      state.params.customerName
    }&type=add&ins=${JSON.stringify(intComputer.value)}`,
  });
};
// 编辑客户
const gotoInst = (item) => {
  Taro.navigateTo({
    url: `/pages/area/customer/addCus?item=${JSON.stringify(
      item
    )}&type=update&ins=${JSON.stringify(intComputer.value)}`,
  });
};
const onScrollToLower = () => {
  if (scrollMyFlag.value && state.insList.length < state.total) {
    state.params.pageNum++;
    scrollMyFlag.value = false;
    getinsList();
  }
};

const insConfirm = (ins) => {
  state.params.insId = ins.id;
      state.params.insCode = ins.insCode;
      state.params.insName = ins.insName;
      state.params.insMdmCode = ins.insMdmCode;
}
watch(
  () => state.params.insName,
  (newValue) => {
    if (!newValue) {
      num.value = 0;
      state.insList = [];
    }
  }
);
onMounted(() => {

  Taro.setNavigationBarTitle({ title: "新增客户查询" });
});
</script>
<style lang="scss">
.customer_manage_search {

.titleTextInfo {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #2551f2;
  line-height: 19px;
  margin-bottom: 9px;
  margin-top: 16px;
}
.subTextInfo {
  width: 100%;
  text-align: center;
  margin-top: 3px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #4e595e;
  line-height: 15px;
}
.textInfo {
  text-align: center;
  margin-bottom: 27px;
}
.nut-cell-group__wrap {
  margin-top: 0;
  box-shadow: none;
}

  padding-bottom: 80px;
  .section {
    background: #fff;
    font-size: 16px;
    padding: 0 16px;
  }
  .result-text {
    padding-top: 16px;
    text-align: center;
    // background: #eff3f5;
  }
  .result {
    background: #fff;
    // padding: 16px 0 0 16px;
    max-height: 400px !important;
    overflow: auto;
  }
  .instItem {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    .insName {
      font-weight: 500;
      font-size: 18px;
      color: #1d212b;
      margin-left: 4px;
      max-width: 260px;
      display: -webkit-box; /* 对Webkit内核浏览器 */
      -webkit-line-clamp: 2; /* 显示的行数 */
      -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
      overflow: hidden; /* 隐藏超出的内容 */
      text-overflow: ellipsis;
      text-align: left;
    }
  }
  .label {
    &::before {
      content: "*";
      color: red;
      margin-top: 6px;
      margin-right: 5px;
      vertical-align: middle;
    }
  }
  .nut-radio-group {
    padding: 0 16px;
  }
  .footer-button {
    background-color: #ffffff;
    padding: 8px 0;
    width: 100%;

    .nut-button {
      border-radius: 4px;
    }
    .border-none {
      border: none;
    }
    .nut-button__wrap {
      img {
        width: 10px;
        height: 10px;
      }
    }
  }

}
</style>
