<template>
  <view class="speakerDetail">
    <view class="section">
      <view class="cus-information">
        <view class="information-name">
          <view style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            ">
            <view style="
                margin-right: 8px;
                color: #1d212b;
                font-size: 20px;
                font-weight: 500;
              ">{{ state.info.name }}
              <nut-tag plain color="#94BFFF" text-color="#2551F2" style="vertical-align: text-bottom">
                {{ state.info.type }}
              </nut-tag>
            </view>
          </view>
        </view>

        <view style="display: flex; align-items: start">
          机构：{{ state.info.insName }}
        </view>
      </view>
    </view>
    <view>
      <nut-tabs v-model="state.currentTab" backgroud="#FFFFFF" @change="tabChange">
        <nut-tab-pane title="基本信息" pane-key="0">
          <SpeakerInfoDetail ref="speakerInfoDetailRef" />
        </nut-tab-pane>
        <nut-tab-pane title="联系信息" pane-key="1">
          <view style="  padding: 12px 16px;background: #fff; border-radius: 8px;">
            <DetailItem label="电子邮箱" :value="relaceApi(state.info.email)" />
            <DetailItem label="手机号码" :value="relaceApi(state.info.phone)" />
            <DetailItem label="个人家庭地址" :value="relaceApi(state.info.idCardAddress)" />
          </view>
        </nut-tab-pane>
        <nut-tab-pane title="付款信息" pane-key="2">
          <SpeakerpaymentDetail ref="speakerpaymentDetail" />
        </nut-tab-pane>
      </nut-tabs>
    </view>
    <FooterButton text="变更讲者信息" textColor="#2551f2" color="#E8F0FF" @click-button="clickButton">
    </FooterButton>
  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref, computed } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import SpeakerpaymentDetail from "../components/speakerpaymentDetail.vue";
import SpeakerInfoDetail from "../components/speakerInfoDetail.vue";
import { speakerDetailApi } from "../../../api/speaker.js";
import location_a from "../../../images/ph_address.png";
import DetailItem from "../../../pages/components/detailItem/index.vue";
const router = useRouter();
const query = router.params;
const speakerInfoDetailRef = ref(null);
const speakerpaymentDetail = ref(null)
const state = reactive({
  info: {},
  approvalStatus: "1",
});

const getDetail = async () => {
  Taro.showToast({
    title: "加载中",
    icon: "loading",
  });
  const res = await speakerDetailApi({code:query.code});
  state.info = res.data;
  speakerInfoDetailRef.value.setInfo(res.data);
  speakerpaymentDetail.value.setInfo(res.data);
  Taro.hideLoading();
};
const relaceApi = (value) => {
  if(value) return  '*'.repeat(value.length);

}
const clickButton = () => {
  Taro.navigateTo({
      url: `/pages/speaker/components/addSpeaker?type=edit&info=${JSON.stringify(state.info)}`,
    });
}

useDidShow(async () => {
  Taro.setNavigationBarTitle({ title: "讲者详情" });
  await getDetail();
});
</script>

<style lang="scss">
.speakerDetail {
  padding-bottom: 93px;

  .section {
    font-size: 14px;
    color: #869199;
    background: #fff;

    padding: 16px;

    .cus-information {
      padding: 16px;
      background: #f4f5f7;
      background-image: url("../../../images/card-bg.png");
      background-size: 100% 100%;

      .information-name {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .hosType {
        height: 22px;
        border-radius: 2px 2px 2px 2px;
        border: 1px solid #94bfff;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #2551f2;
        line-height: 22px;
        padding: 0 4px;
        margin-right: 4px;
      }
    }
  }

  .icons {
    width: 14px;
    height: 14px;
    display: block;
    margin-right: 4px;
    margin-top: 3px;
  }

  .nut-tab-pane {
    font-size: 14px;

    padding: 0;
    background: none;
    padding: 12px;

  }
}
</style>
