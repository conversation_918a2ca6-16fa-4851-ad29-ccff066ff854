<template>
  <view class="globa-radio-group">
    <nut-radio-group
      v-model="value[0].id"
      text-position="right"
      @change="changeBox"
    >
      <nut-radio
        :label="item.id"
        v-for="item in options"
        :key="item.id"
        style="padding: 0; border: none"
      >
        <template #icon>
          <img :src="icon" alt="" class="radioIcon" />
        </template>
        <template #checkedIcon>
          <img :src="checkedIcon" alt="" class="radioIcon" />
        </template>
        {{ item.content }}
      </nut-radio>
    </nut-radio-group>
  </view>
</template>
<script setup>
import { defineEmits } from "vue";
import icon from "../../../images/radio_icon.png";
import checkedIcon from "../../../images/radio_checked_icon.png";
const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  options: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(["emitChangeBox"]);
const changeBox = (val) => {
  emits("emitChangeBox", val);
};
</script>
<style lang="scss">
.radioIcon {
  width: 16px;
  height: 16px;
  display: inline-block;
}
// ::v-deep {
.nut-radio__label {
  font-size: 14px !important;
  color: #1d1e1e !important;
}
// }
</style>
