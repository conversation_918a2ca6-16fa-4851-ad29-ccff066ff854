import httpRequest from "@/servers/http";

export const createProcessApi = (data) => {
  return httpRequest.post(`/bpm/workflow/process/create`,data);
};

export const updateProcessApi = (data) => {
  return httpRequest.post('/bpm/workflow/process/update', data);
};
export const processListApi = (params) => {
  return httpRequest.get(`/bpm/workflow/process/page`,params);
};
export const processDetailApi = (id) => {
  return httpRequest.get(`/bpm/workflow/process/${id}`);
};
export const fromModifyApi = (data) => {
  return httpRequest.put(`/bpm/workflow/form/update`, data);
};
export const formListApi = (params) => {
  return httpRequest.get(`/bpm/workflow/form/page`, params);
};
export const formDetailApi = (id) => {
  return httpRequest({
    url: `/bpm/workflow/form/${id}`,
    method: 'get'
  });
};
export const formDeleteApi = (id) => {
  return httpRequest({
    url: `/bpm/workflow/form/${id}`,
    method: 'delete'
  });
};

// 发启流程
export const startProcessApi = (data) => {
  return httpRequest.post(`/bpm/workflow/process/start`, data);
};

// 我发起的流程
export const taskApplyListApi = (data) => {
  return httpRequest.post(`/bpm/workflow/task/applyList`, data);
};

// 同意
export const agreeTaskApi = (data) => {
  return httpRequest.post(`/bpm/workflow/task/agree`, data);
};

// 拒绝
export const refuseTaskApi = (data) => {
  return httpRequest.post(`/bpm/workflow/task/refuse`, data);
};

// 待审批任务
export const todoTaskApi = (data) => {
  return httpRequest.post(`/bpm/workflow/task/todoTask`,data);
};

// 关联要点审批列表
export const insDsApplyApi = (params) => {
  return httpRequest.post(`/mdm/ins-ds/apply/list?bpmType=${params.bpmType}&pageSize=${params.pageSize}&pageNum=${params.pageNum}`,);
};

// 撤销流程
export const revokeTaskApi = (data) => {
  return httpRequest.post(`/bpm/workflow/task/revoke`, data);
};

// 转办
export const transferTaskApi = (data) => {
  return httpRequest.post(`/bpm/workflow/task/transfer`, data);
};

// 抄送我的
export const ccTaskListApi = (data) => {
  return httpRequest.post(`/bpm/workflow/task/ccList`, data);
};

// 已审批列表
export const doneListApi = (data) => {
  return httpRequest.post(`/bpm/workflow/task/doneList`, data);
};

// 查询实例表单数据（审批表单）
/*
* processId
integer <int64>
必需
instanceId
string
* */
export const taskInstanceFormApi = (params) => {
  return httpRequest.get(`/bpm/workflow/task/instanceForm`, params);
};
// 查询实例动作记录(审批记录)
/*
必需
instanceId
string
* */
export const taskInstanceActionApi = (params) => {
  return httpRequest.get(`/bpm/workflow/task/instanceAction`, params);
};
// 查询节点信息(节点信息)
/*
processId
string
必需
nodeKey
string
必需
* */
export const taskNodeInfoApi = (params) => {
  return httpRequest.get(`/bpm/workflow/task/getNodeInfo`, params);
};

export const getFileInfo = (id) => {
  return httpRequest.get(`fle/oss-file/tenant/${id}`);
};

export const taskInstanceDetailApi = (params) => {
  return httpRequest.get(`/bpm/workflow/task/instanceHistory`, params);
};

/**
 * 查询用户列表
 * @param query
 */
export const listUserApi = (params) => {
  return httpRequest.get('/plt/user/list', params);
};
export const imageUrl = (url) => {
  return httpRequest.get(`/fle/oss-file/tenant/${url}`);
};