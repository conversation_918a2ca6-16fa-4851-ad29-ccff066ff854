<template>
  <view class="bg-white px-[16px]">
    <view>
      <view class="text-[#1D212B] text-[14px] py-[10px]">基本信息</view>
      <view>
        <view
          class="flex items-top mb-[8px] justify-between"
          v-if="props.formData.postName"
        >
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >岗位</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.postName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >类型</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.typeValue
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[80px]">时间</view>
          <view class="text-[#1D212B] text-[14px] font-400 text-right"
            >{{ props.formData.startTime }} - {{ props.formData.endTime }}</view
          >
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >天数</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.askDay
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[80px]">备注</view>
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.reason
          }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
const ruleForm = ref(null);
const props = defineProps(["formData"]);
</script>
<style lang="scss">
.nut-tab-pane {
  padding: 0;
}
</style>
