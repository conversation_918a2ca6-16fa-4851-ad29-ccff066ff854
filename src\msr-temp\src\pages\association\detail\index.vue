<template>
  <view class="association-detail">
    <view class="section">
      <view class="cus-information">
        <view class="information-name">
          <view
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <view
              style="
                margin-right: 8px;
                color: #1d212b;
                font-size: 20px;
                font-weight: 500;
              "
              >{{ decodeURIComponent(query.insName) }}
              <nut-tag
                plain
                color="#94BFFF"
                text-color="#2551F2"
                style="vertical-align: text-bottom"
              >
                {{ decodeURIComponent(query.insMdmCode) }}
              </nut-tag></view
            >
          </view>
        </view>

        <view style="display: flex; align-items: center">
          <img :src="location_a" alt="" class="icons" />
          {{ decodeURIComponent(query.area) }}
        </view>
      </view>
    </view>
  </view>
  <TabDetail ref="detailRef" @change-type="getDetail" />
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
import { Edit } from "@nutui/icons-vue-taro";
import TabDetail from "./tabDetail.vue";
import { associationList } from "../../../api/pharmacy.js";
import location_a from "../../../images/ph_address.png";

const router = useRouter();
const query = router.params;
const detailRef = ref(null);
const state = reactive({
  info: {},
});

const getDetail = async () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });

  const res = await associationList({
    insCode: query.insCode,
    jurCode: query.jurCode,
  });
  state.info = res.rows;
  detailRef.value.setInfo(res.rows);
  Taro.hideLoading();
};

useDidShow(async () => {
  Taro.setNavigationBarTitle({ title: "关联药店管理" });
  await getDetail();
});
</script>

<style lang="scss">
.association-detail {
  .section {
    font-size: 14px;
    color: #869199;
    background: #fff;

    padding: 16px;
    .cus-information {
      padding: 16px;
      background: #f4f5f7;
      background-image: url("../../../images/card-bg.png");
      background-size: 100% 100%;
      .information-name {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
  .icons {
    width: 14px;
    height: 14px;
    display: block;
    vertical-align: middle;
    margin-right: 4px;
  }
}
</style>
