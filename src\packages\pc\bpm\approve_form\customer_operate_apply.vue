<template>
  <div>
    <div class="text-[#1D212B] text-[14px] py-[10px]">基本信息</div>
    <div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          辖区负责人
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.jurMgrName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          辖区负责人工号
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.jurMgrCode }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">部门</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.ancestors }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">岗位</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.postName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          机构名称
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.insName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          机构编码
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.insCode }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          客户名称
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.customerName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          月门诊量更新前
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.oldMonthlyOutpatient }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          月门诊量更新后
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.monthlyOutpatient }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["formData"]);
</script>
<style lang="scss"></style>
