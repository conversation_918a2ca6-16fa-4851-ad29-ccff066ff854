<template>
  <view class="editProduct">
    <view class="section">
      <view class="cus-information" v-if="query.type === 'ins'">
        <view class="information-name">
          <view style="display: flex; align-items: center">
            <view style="margin-right: 8px; color: #1d212b; font-size: 20px">{{
              state.info.insName
            }}</view>
          </view>
        </view>
        <view style="margin-top: 8px">
          <text
            class="hosType"
            v-if="state.info.insGrade || state.info.insLevel"
          >
            {{ state.info.insGrade }}{{ state.info.insLevel }}
          </text>
          <text> 机构编码：{{ state.info.insMdmCode }}</text>
        </view>
        <view
          style="color: #869199; font-size: 14px; margin-top: 5px"
          v-if="state.info.mgr && state.info.mgr.length > 0"
          >代表：{{ state.info.mgr.map((item) => item.value).join("、") }}
        </view>
      </view>
      <view class="cus-information" v-else>
        <view class="information-name">
          <view style="display: flex; align-items: center">
            <view style="margin-right: 8px; color: #1d212b; font-size: 20px">{{
              state.info.customerName
            }}</view>
            <view class="hosType" v-if="state.info.professionTech">{{
              state.info.professionTech
            }}</view>
            <img
              :src="vector"
              alt=""
              class="sexIcon"
              v-if="state.info.sex === '1'"
            />
            <img
              :src="man"
              alt=""
              class="sexIcon"
              v-if="state.info.sex === '0'"
            />
<!--            <view> {{ state.info.customerCode }}</view>-->
          </view>
        </view>

        <view style="margin-top: 8px"
          ><img :src="cus_ins" alt="" class="insIcon" />{{
            state.info.insName
          }}</view
        >
        <view v-if="state.info.insDeptName"
          >科室： {{ state.info.insDeptName }}</view
        >
        <view
          style="color: #869199; font-size: 14px; margin-top: 5px"
          v-if="state.info.mgr && state.info.mgr.length > 0"
          >代表：{{ state.info.mgr.map((item) => item.value).join("、") }}
        </view>
      </view>
    </view>
    <view class="section-title" v-if="query.type === 'ins'">机构内产品</view>
    <view class="section-title" v-else>客户内产品</view>
    <view class="content">
      <view class="item" v-for="item in productList" :key="item.code">
        <view style="display: flex; align-items: center">
          <view style="font-size: 16px">{{ item.name }}</view>
          <view class="productTag" v-if="item.claimStatus === '2'">
            <text style="margin-left: 2px">审批中</text>
          </view>
          <view class="productTag2" v-if="item.claimStatus === '3'">
            <text style="margin-left: 2px">待申请</text>
          </view>
        </view>

        <view @click="deleteProduct(item)" v-if="item.claimStatus !== '2'"
          ><img :src="product" class="delete-product"
        /></view>
      </view>
    </view>

    <FooterButton
      text="添加新产品"
      :plain="true"
      textColor="#2551f2"
      @click-button="clickAddButton"
    >
      <template #icon>
        <Uploader color="#2551f2" size="14px" />
      </template>
    </FooterButton>
    <FooterButtonTwo
      v-if="state.list.length"
      rightText="提交申请"
      @click-left="clickAddButton"
      @click-right="clickSubmit"
      leftText="新增"
      :plainFlag="false"
      leftColor="#E8F0FF"
      leftTextColor="#2551F2"
      :disabledRight="state.submitlag"
    >
      <template #iconLeft>
        <Uploader color="#2551f2" size="14px" />
      </template>
    </FooterButtonTwo>
  </view>
  <AddProduct ref="addProductRef" @select-pro="selectPro" />
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { reactive, ref, computed } from "vue";
import product from "../../../images/product_del.png";
import FooterButtonTwo from "../../../pages/components/footerButtonTwo/footerButtonTwo";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { Uploader } from "@nutui/icons-vue-taro";
import AddProduct from "./addProduct.vue";
import {
  productQuery,
  productDelete,
  approveInitiate,
  getApproval,
  checkUnique
} from "../../../api/area";
import { Clock } from "@nutui/icons-vue-taro";
import { getPostCode } from "../../../utils/area.js";
import vector from "../../../images/woman.png";
import man from "../../../images/man.png";
import cus_ins from "../../../images/cus_ins.png";
import { appCode, tenantId } from "../../../utils/content";
const detailInfo = Taro.getStorageSync("detailInfo");
const initHrInfo = Taro.getStorageSync("initHrInfo");
const router = useRouter();
const query = router.params;
const addProductRef = ref(null);
const state = reactive({
  product: [],
  list: [],
  info: {},
  params: {},
  submitlag: false,
  jurCode: getPostCode(),
  newList: [],
});
const productList = computed(() => [...state.product, ...state.list]);

const getProduct = async () => {
  const res = await productQuery(state.params);
  state.product = res.data.filter(
    (item) => item.claimStatus === "1" || item.claimStatus === "2"
  );
};

const deleteProduct = async (item) => {
  const productList = state.product.filter((item) => item.claimStatus === "1");
  if (productList.length === 1 && query.type !== "cust") {
    return Taro.showToast({
      title: "产品为必填项",
      icon: "none",
      duration: 2000,
    });
  }
  const text =
    productList.length === 1 && query.type !== "cust"
      ? "若辖区下机构和客户存在相同产品，该产品信息会被同步删除"
      : `确认删除「${item.name}」`;
  Taro.showModal({
    content: text,
    success: async function (res) {
      if (res.confirm) {
        if (item.id) {
          const params = {
            type: state.params.type,
            id: item.id,
          };
          const res = await productDelete(params);
          if (res.code === 200) {
            getProduct();
          }
        } else {
          state.list = state.list.filter((v) => v.code !== item.code);
        }
        Taro.showToast({
          title: "删除成功",
          icon: "none",
          duration: 2000,
        });
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};

const selectPro = (list) => {
  state.list = list;
};
const clickAddButton = () => {
  const checked = state.list.map((item) => item.code);
  addProductRef.value.open("edit", state.params, checked);
};

const getCheckUnique = async (data) => {
  const params = {
    customerName: data.customerName,
    insName: data.insName,
    jurCode: data.jurCode,
    jurCustomerCode: data.customerCode,
    jurInsCode: data.insCode,
    productList:  data.applyContent.products
  }
  const res = await checkUnique(params)
  if(res.data) {
    state.submitlag = false;
    const obj = res.data
    const productName = obj.productList.map(item => item.name).join('；')
    const text1 = `【${obj.insName} ${obj.insMdmCode} ${obj.customerName} ${productName}】已被他人认领，请联系助理或运营确认，是否可调整客户架构关系`
    Taro.showModal({
      content: text1,
      confirmText: "确定",
      duration: 2000,
      showCancel: false,
    });
     return true

   } else {
    return false
  }

}

const clickSubmit = async () => {
 
  state.info.applicantCode = initHrInfo.empCode;
  state.info.applicant = initHrInfo.empName;
  state.info.postIdList = initHrInfo.postIdList;
  state.info.type = query.type;
  state.info.products = state.list;
  delete state.info.status;
  state.submitlag = true;
  const applicantInfo = {
    applicant: state.info.applicant,
    applicantCode: state.info.applicantCode,
    postCode: state.info.postCode,
    postName: state.info.postName,
    deptCode: state.info.deptCode,
    deptName: state.info.deptName,
    ancestors: state.info.ancestors,
    postIdList: state.info.postIdList,
  };
  const params = {
    applyContent: {
      ...state.info,
    },
    insCode: state.info.insCode,
    insMdmCode: state.info.insMdmCode,
    insName: state.info.insName,
    appCode: appCode,
    tenantId: tenantId,
    applyType: query.type === "cust" ? "6" : query.type === "ins" ? "5" : "",
    jurCode: state.params.jurCode,
    ...applicantInfo,
  };
  if (query.type === "cust") {
    params.customerCode = state.info.customerCode;
    params.customerName = state.info.customerName;
    const key = Taro.getStorageSync('jurCustomerUnique')
    if(key === '1') {
      if(await getCheckUnique(params)) return 
    }

  }
  const res = await approveInitiate(params);
  if (res.code === 200) {
  
    Taro.showToast({
      title: "添加成功",
      icon: "none",
      duration: 2000,
    });
    Taro.navigateBack({
      delta: 1,
    });
    state.submitlag = false;
  } else {
 
    state.submitlag = false;
  }
};

useDidShow(() => {
  Taro.setNavigationBarTitle({ title: "编辑产品" });
  state.info = detailInfo;
  state.params = {
    insCode: state.info.insCode,
    jurCode: detailInfo.jurCode,
    type: query.type,
  };
  if (query.type === "cust") {
    state.params.customerCode = state.info.customerCode;
    state.params.jurCode = detailInfo.jurCode;
  }

  getProduct();
});
</script>
<style lang="scss">
.editProduct {
  padding-bottom: 24px;
  .section-title {
    color: #869199;
    font-size: 14px;
    padding: 8px 0 0 12px;
  }
  .section {
    font-size: 14px;
    color: #869199;
    background: #fff;

    padding: 16px;

    .cus-information {
      padding: 16px;
      background: #f4f5f7;
      background-image: url("../../../images/card-bg.png");
      background-size: 100% 100%;
      .information-name {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .sexIcon {
          display: inline-block;
          margin-right: 5px;
          width: 18px;
          height: 18px;
          vertical-align: text-bottom;
        }
      }
      .hosType {
        height: 22px;
        border-radius: 2px 2px 2px 2px;
        border: 1px solid #94bfff;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #2551f2;
        line-height: 22px;
        padding: 0 4px;
        margin-right: 4px;
      }
      .insIcon {
        display: inline-block;
        margin-right: 6px;
        width: 18px;
        height: 18px;
        vertical-align: text-bottom;
      }
    }
  }
  .title {
    padding: 20px 0 12px 28px;
    font-size: 16px;
    color: #869199;
  }
  .content {
    padding: 0 12px;
    margin-top: 8px;
    max-height: 480px;
    overflow: hidden;
    overflow-y: scroll;
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 54px;
      background: #fff;
      padding: 0 16px;
      margin-bottom: 8px;
      border-radius: 4px;
    }
    .delete-product {
      width: 20px;
      height: 20px;
    }
    .productTag {
      font-size: 12px;
      text-align: center;
      background: #fff3e8;
      color: #ff7d00;
      height: 20px;
      line-height: 20px;
      margin-left: 4px;
      border-radius: 2px;
      padding: 0 2px;
    }
    .productTag2 {
      font-size: 12px;
      text-align: center;
      background: #e8f0ff;
      color: #2551f2;
      margin-left: 4px;
      height: 20px;
      line-height: 20px;
      border-radius: 2px;
      padding: 0 2px;
    }
  }
}
</style>
