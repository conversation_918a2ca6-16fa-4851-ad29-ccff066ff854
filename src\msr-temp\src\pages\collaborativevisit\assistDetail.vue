<template>
  <view class="assistDetail-page">
    <!-- <navBar @click-back="clickBack" leftText="协访详情"></navBar> -->
    <DetailTop
      :assistStatus="state.info?.assistDetail?.status"
      :status="state.info?.status"
      :visitTime="state.info.visitTime"
      :completeTime="state.info.completeTime"
      :planFlagTime="state.info.planFlag"
      :assistUserHrName="state.info?.assistDetail?.assistUserHrName"
      :activeIndex="query.activeIndex"
    />
    <view style="padding: 0 16px">
      <SignTime
        signType="assist"
        :visitTime="state.info.visitTime"
        :visitUserName="state.info.visitUserName"
        :signInTime="state.info.signInTime"
        :signOutTime="state.info.signOutTime"
        :duration="state.info.duration"
      />
      <view class="visit-address">
        <view
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
        >
          <view style="display: flex; align-items: center">
            <view>
              <text style="font-size: 20px; font-weight: 500">{{
                state.info.customerName
              }}</text>
              <img
                :src="
                  state.info.sex === '1'
                    ? vector
                    : state.info.sex === '0'
                    ? man
                    : ''
                "
                v-if="state.info.sex === '1' || state.info.sex === '0'"
                alt=""
                class="sexIcon"
              />
            </view>
            <view class="tag-s" v-if="state.info.planFlag === '1'"
              >计划拜访
            </view>
            <view class="tag-f" v-if="state.info.planFlag === '0'"
              >临时拜访
            </view>
            <!-- <nut-tag
              color="#E8F0FF"
              text-color="#2551F2"
              v-if="state.info.planFlag === '1'"
              style="vertical-align: text-bottom"
              >计划拜访
            </nut-tag>
            <nut-tag
              color="#FFF3E8"
              text-color="#F77234"
              v-if="state.info.planFlag === '0'"
              style="vertical-align: text-bottom"
              >临时拜访
            </nut-tag> -->
          </view>
          <view>
            <nut-tag
              type="primary"
              color="#E6F8F2"
              text-color="#00B578"
              plain
              v-if="state.info.assistRemark === '1' && query.activeIndex != 1"
            >
              您已评价
            </nut-tag>
          </view>
        </view>

        <view style="font-size: 14px; color: #869199; margin-top: 10px">
          <view class="m_b">
            <img :src="cus_ins" alt="" class="address" />
            <text>{{ state.info.institutionName }}</text>
            <nut-divider direction="vertical" />
            <text>{{ state.info.insDeptName }}</text>
            <nut-divider direction="vertical" />
            <text>{{
              state.info.visitTypeCode === "0" ? "院内拜访" : "院外拜访"
            }}</text>
          </view>
          <view class="m_b">
            <img :src="adress" alt="" class="address" />{{
              state.info.insAddress
            }}
          </view>
        </view>
      </view>

      <!-- 拜访信息 -->
      <view
        style="margin-top: 8px"
        class="visit-info"
        v-if="state.info.visitPurposeCode"
      >
        <view class="info-title" @click="clickVisit">
          <view>拜访信息</view>
          <view>
            <img
              :src="!!state.infoIconFlag ? arrowDown : arrowUp"
              alt=""
              style="width: 13px; height: 13px; padding-left: 10px"
            />
            <!-- <view>暂无</view> -->
          </view>
        </view>
        <view class="info-content" v-if="!state.infoIconFlag">
          <nut-divider dashed :style="{ color: '#E5E6EB', marginBottom: 0 }" />
          <view style="border-radius: 0 0 8px 8px">
            <nut-cell class="cell" title="拜访目的">
              <template #desc>
                <span style="word-break: break-all">
                  {{ visitPurposeName(state.info.visitPurposeCode) }}</span
                >
              </template>
            </nut-cell>
            <nut-cell class="cell" title="上次沟通内容" v-if="!is048">
              <template #desc>
                <span style="word-break: break-all">
                  {{ state.info.lastCommunicateContent }}</span
                >
              </template>
            </nut-cell>
            <nut-cell class="cell" title="本次沟通内容" v-if="!is048">
              <template #desc>
                <span style="word-break: break-all">
                  {{ state.info.communicateContent }}</span
                >
              </template>
            </nut-cell>
            <nut-cell title="协访人" class="assistUserVOs cell">
              <template #desc>
                <view
                  v-for="item in state.info.assistUserVOs"
                  :key="item.id"
                  style="margin-right: 5px; margin-bottom: 10px"
                >
                  <view style="margin-bottom: 4px"
                    >{{ item.empName
                    }}<text
                      style="
                        background: #e6f8f2;
                        color: #00b578;
                        padding: 3px 5px;
                        border-radius: 2px;
                      "
                      v-if="item.confirmInviteFlag === '1'"
                      >已接受</text
                    >
                    <text
                      style="
                        background: #feeaea;
                        color: #f32f29;
                        padding: 3px 5px;
                        border-radius: 2px;
                      "
                      v-if="item.confirmInviteFlag === '0'"
                      >已拒绝</text
                    ></view
                  >
                </view>
              </template>
            </nut-cell>
            <nut-cell
              title="协访需求"
              class="cell"
              :desc="assistPurposeName(state.info.assistRequirement)"
              style="border-bottom: none"
            >
            </nut-cell>
          </view>
        </view>
      </view>
      <!-- 沟通产品 -->
      <view
        style="margin-top: 8px"
        class="pro-info"
        v-if="state.info.visitPurposeCode"
      >
        <view class="info-title" @click="clickPro">
          <view>沟通产品</view>
          <view style="display: flex; align-items: center">
            <view class="pro-name">{{ getProName() }}</view>
            <img
              :src="!!state.proFlag ? arrowDown : arrowUp"
              alt=""
              style="width: 13px; height: 13px; padding-left: 10px"
            />
          </view>
        </view>
        <view v-if="!state.proFlag">
          <nut-divider dashed :style="{ color: '#E5E6EB', marginBottom: 0 }" />
          <view style="padding: 0 12px">
            <view
              class="pro-content"
              v-for="item in state.info.productForms"
              :key="item.productCode"
            >
              <view class="pro-title">
                <view style="color: #2551f2; font-weight: 500">{{
                  item.productName
                }}</view>
              </view>
              <nut-divider :style="{ color: '#EEEEEE', margin: '0' }" />
              <view
                style="
                  padding: 12px;
                  padding-bottom: 0;
                  color: #666666;
                  font-size: 14px;
                "
                v-for="(j, index) in item.productInfoList"
                :key="j.infoId"
              >
                <view> {{ j.info }}</view>
                <view
                  style="color: #f32f29; font-size: 14px; text-align: right"
                  v-if="j.cusProdRespCode === '0'"
                >
                  <img
                    :src="proN"
                    style="width: 18px; height: 18px; vertical-align: bottom"
                  />
                  不认可
                </view>
                <view
                  style="color: #00b578; font-size: 14px; text-align: right"
                  v-if="j.cusProdRespCode === '1'"
                >
                  <img
                    :src="proY"
                    style="width: 18px; height: 18px; vertical-align: bottom"
                  />
                  认可
                </view>
                <view
                  style="color: #ffb42f; font-size: 14px; text-align: right"
                  v-if="j.cusProdRespCode === '2'"
                >
                  <img
                    :src="proZ"
                    style="width: 18px; height: 18px; vertical-align: bottom"
                  />
                  中立
                </view>
                <nut-divider
                  v-if="index !== item.productInfoList.length - 1"
                  dashed
                  :style="{ color: '#E5E6EB', margin: '5px 0 0 0' }"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 协访评价无数据 -->
      <view class="evaluate-no" v-if="state.info.assistRemark != '1'">
        <view class="evaluate-title">协访评价</view>
        <nut-divider :style="{ color: '#E5E6EB', margin: 0 }" />
        <view class="evaluate-title evaluate-content">
          <view>协访评价</view>
          <view style="color: #1d212b">暂无</view>
        </view>
        <nut-divider :style="{ color: '#E5E6EB', margin: 0 }" />
        <view class="evaluate-title evaluate-content">
          <view>协访建议</view>
          <view style="color: #1d212b"> 暂无</view>
        </view>
      </view>
      <!-- 协访评价有数据 -->
      <view class="evaluate" v-else>
        <view style="position: relative">
          <view class="evaluate-title">
            <view>协访评价</view>
            <view
              style="
                font-size: 14px;
                color: #869199;
                display: flex;
                align-items: center;
                justify-content: end;
              "
            >
              <view style="color: #00b578" class="assist-name">{{
                assistName(state.info.assistScoreVos)
              }}</view>
              <view>已做出评价</view>
            </view>
          </view>
        </view>
        <nut-divider dashed :style="{ color: '#E5E6EB', margin: 0 }" />
        <view
          v-for="(item, index) in state.info.assistScoreVos"
          :key="item.index"
          :style="{ borderTop: index !== 0 ? '1px solid #E5E6EB' : '' }"
        >
          <view style="position: relative">
            <view class="evaluate-name">
              <view
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 16px 0;
                "
                @click="assistClick(item.assistUser)"
              >
                <view style="display: flex; align-items: center">
                  <view>
                    {{ item.assistUser }}
                    <!-- <nut-tag type="primary" color="#E6F8F2" text-color="#00B578">
                    协访人
                  </nut-tag> -->
                  </view>
                  <view class="tag-green"> 协访人 </view>
                </view>

                <view>
                  <img
                    :src="!!item.assistIcon ? arrowDown : arrowUp"
                    alt=""
                    style="width: 13px; height: 13px; padding-left: 10px"
                /></view>
              </view>
            </view>
            <view class="appraiseButton">
              <nut-button
                color="#E8F0FF"
                @click="clickButton('edit', item)"
                style="z-index: 10"
                v-if="
                  item.isCanEdit === 1 &&
                  item.isLoginUser === 1 &&
                  query.activeIndex != 1
                "
                >修改
              </nut-button>
            </view>
          </view>

          <view class="evaluate-content" v-if="!item.assistIcon">
            <view class="evaluate-title">
              <view style="color: #869199">协访评分</view>
              <view>综合评价{{ item.total }}</view>
            </view>
            <view
              class="evaluate-scoring"
              v-for="j in item.scores"
              :key="j.scoreItemCode"
              style="padding-left: 4px"
            >
              <div class="rate">
                <div class="rate-content">
                  <div class="rate-title">
                    {{ j.scoreItemName }}
                  </div>
                  <nut-rate v-model="j.score" active-color="#FFB637" readonly />
                </div>
              </div>
            </view>
            <nut-divider :style="{ color: '#F4F5F7', margin: 0 }" />
            <view
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 0;
              "
            >
              <view style="color: #869199; min-width: 80px">协访建议</view>
              <view>{{ item.assistSuggestInfo }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <FooterButton
      text="去评价"
      color="#E8F0FF"
      textColor="#2551F2"
      @click-button="clickButton"
      v-if="
        state.info?.assistDetail?.isShowEvaluateBtn == 1 &&
        query.activeIndex != 1
      "
    />
    <FooterButtonTwo
      rightText="接受"
      @click-left="clickButtonTwo('0')"
      @click-right="clickButtonTwo('1')"
      leftText="拒绝"
      :plainFlag="false"
      leftColor=""
      leftTextColor="#4E595E"
      v-if="
        query.activeIndex != 1 &&
        !state.info?.assistDetail?.confirmInviteFlag &&
        (state.info?.assistDetail?.status === '0' ||
          state.info?.assistDetail?.status === '3')
      "
    />
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { reactive, ref } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import SignTime from "../institutionalVisits/sign/components/signTime.vue";
import vector from "../../images/woman.png";
import man from "../../images/man.png";
import cus_ins from "../../images/hospital_v3.png";
import adress from "../../images/location.png";
import arrowDown from "../../images/arrow-down.png";
import arrowUp from "../../images/up.png";
import FooterButtonTwo from "../../pages/components/footerButtonTwo/footerButtonTwo";
import {
  institutionalVisitsDetail,
  visitDistance,
  sign,
} from "../../api/institutionalVisitsApi.js";
import { assistApplyHandle } from "../../api/collaborativevisit.js";
import {
  dictDataValueList,
  visitPurposeName,
  assistPurposeName,
} from "../../utils/content.js";
import FooterButton from "../../pages/components/footerButton/index.vue";
import NavBar from "../../pages/components/navBar/index.vue";
import DetailTop from "./components/detailTop";
import proN from "../../images/pro-1.png";
import proY from "../../images/pro-2.png";
import proZ from "../../images/pro-3.png";
const router = useRouter();
const query = router.params;

const is048 = ref(sessionStorage.getItem('048') ==='1')
const state = reactive({
  info: {},
  signOutStatus: "",
  infoIconFlag: false,
  proFlag: false,
  list: [{ code: "1", score: 3 }],
  //评分
  assistScoreVos: [],
});

const getDetail = async () => {
  const params = {
    visitInstitutionId: query.visitInstitutionId,
    assistId: query.assistId,
  };
  const res = await institutionalVisitsDetail(params);
  state.info = res?.data;
  if (state.info.assistScoreVos.length) {
    state.assistScoreVos = state.info.assistScoreVos[0];
  }
};

const clickVisit = () => {
  state.infoIconFlag = !state.infoIconFlag;
};
const clickPro = () => {
  state.proFlag = !state.proFlag;
};
const clickBack = () => {
  if (query.isVisitAssist === "1") {
    Taro.reLaunch({
      url: "/pages/collaborativevisit/index",
    });
  } else {
    Taro.navigateBack({
      delta: 1,
    });
  }
};
const getProName = () => {
  if (state.info.productForms && state.info.productForms.length) {
    return state.info.productForms.map((item) => item.productName).join(",");
  }
};

const assistName = (arr) => {
  return arr.map((item) => item.assistUser).join(",");
};
const assistClick = (name) => {
  state.info.assistScoreVos.forEach((item) => {
    if (item.assistUser === name) {
      item.assistIcon = !item.assistIcon;
    }
  });
  state.assistIcon = !state.assistIcon;
};
const clickButton = (type, item) => {
  if (type) {
    Taro.navigateTo({
      url: `/pages/collaborativevisit/components/appraise?visitInstitutionId=${
        query.visitInstitutionId
      }&assistId=${query.assistId}&obj=${JSON.stringify(item)}`,
    });
  } else {
    Taro.navigateTo({
      url: `/pages/collaborativevisit/components/appraise?visitInstitutionId=${query.visitInstitutionId}&assistId=${query.assistId}`,
    });
  }
};

const clickButtonTwo = (type) => {
  const params = {
    id: state.info.assistDetail.id,
    confirmInviteFlag: type,
  };
  assistApplyHandle(params).then((res) => {
    if (res.code === 200) {
      Taro.showToast({
        title: type === "1" ? "已接受" : "已拒绝",
        icon: "none",
        duration: 2000,
      });
      getDetail();
    }
  });
};

useDidShow(() => {
  Taro.setNavigationBarTitle({
    title: "协访详情",
  });
  getDetail();
});
</script>
<style lang="scss">
.cell,
.nut-cell__title {
  font-size: 14px;
}
.tag-green {
  margin-left: 4px;
  color: #00b578;
  height: 18px;
  line-height: 18px;
  background: #e6f8f2;
  width: fit-content;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
}
.tag-f {
  margin-left: 4px;
  color: #f77234;
  background: #fff3e8;
  height: 24px;
  min-width: 48px;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
  height: 20px;
  line-height: 20px;
}
.tag-s {
  margin-left: 4px;
  color: #2551f2;
  height: 24px;
  background: #e8f0ff;
  min-width: 48px;
  padding: 3.5px 6px;
  font-size: 12px;
  font-weight: 400;
  font-family: PingFang SC;
  border-radius: 2px;
  height: 20px;
  line-height: 20px;
}
.assistDetail-page {
  font-size: 16px;
  // padding-top: 94px;
  padding-bottom: 75px;
  .sign-time {
    margin: 0;
  }

  .visit-address {
    margin-top: 8px;
    padding: 12px 16px;
    background: #fff;
    border-radius: 8px;
    .sexIcon {
      display: inline-block;
      margin-right: 5px;
      width: 18px;
      height: 18px;
      vertical-align: text-top;
    }
    .address {
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: text-bottom;
      margin-right: 4px;
    }
    .m_b {
      // margin-bottom: 8px;
    }
  }
  .visit-info {
    padding: 16px;
    border-radius: 8px;
    background: #fff;
    .info-title {
      display: flex;
      justify-content: space-between;
    }
    .nut-button__wrap {
      color: #2551f2;
    }
    .assistUserVOs {
      .nut-cell__title {
        max-width: 100px;
      }
    }
  }
  .pro-info {
    padding: 16px 0;
    border-radius: 8px;
    background: #fff;
    .info-title {
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .pro-content {
      border-radius: 4px;
      background: #f8fafe;
      padding-bottom: 12px;
      margin-top: 5px;
    }
    .pro-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
    }
    .pro-name {
      width: 200px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-align: end;
    }
  }
  .evaluate-no {
    margin-top: 8px;
    background: #fff;
    padding: 0 16px;
    border-radius: 8px;

    .evaluate-title {
      height: 54px;
      line-height: 54px;
    }
    .evaluate-content {
      color: #869199;
      display: flex;
      justify-content: space-between;
    }
  }
  .evaluate {
    border-radius: 8px;
    margin-top: 8px;
    background: #fff;
    .appraiseButton {
      position: absolute;
      right: 12%;
      top: 19%;
      .nut-button__wrap {
        color: #2551f2;
      }
    }
    .evaluate-title {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .assist-name {
        width: 150px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: end;
      }
    }
    .evaluate-name {
      padding: 0 16px;
      .nut-tag {
        color: #00b578;
      }
    }
    .evaluate-content {
      padding: 0 16px;
      .evaluate-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #f4f5f7;
      }
      .rate {
        background-color: #fff;
        .rate-content {
          height: 40px;
          display: flex;
          align-items: center;
          .rate-title {
            color: #000;
            font-weight: 400;
            line-height: 22px;
            font-size: 14px;
          }

          .nut-rate {
            margin-left: 20px;
          }
        }
        .rate-score {
          text-align: right;
          margin-top: 16px;
          font-size: 14px;
        }
      }
    }
  }
  .nut-cell__title {
    color: #869199;
    max-width: 100px;
  }

  .nut-button {
    height: 32px;
    line-height: 32px;
    font-size: 16px;
  }
}
</style>
