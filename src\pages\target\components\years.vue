<template>
  <view class="years">
    <GlobalPopup ref="popupRef">
      <nut-radio-group v-model="state.radioChecked" text-position="left">
        <GlobalNutRadio v-for="item in props.list" :value="item" :key="item.id">
          <template #name>
            <view>
              {{ item }}
            </view>
          </template>
        </GlobalNutRadio>
      </nut-radio-group>
      <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import GlobalPopup from "@/pages/components/globalPopup/globalPopup.vue";
import GlobalNutRadio from "@/pages/components/globalNutRadio/globalNutRadio.vue";
const emit = defineEmits(["confirm"]);
const popupRef = ref(null);
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});
const state = reactive({
  radioChecked: "",
  list: "",
});

const open = (code) => {
  popupRef.value.open();
  state.radioChecked = code;
};

const confirm = () => {
  emit("confirm", state.radioChecked);
  popupRef.value.close();
};
defineExpose({
  open,
});
</script>
<style lang="scss">
.years {
  // background: #f4f5f7;
  .nut-radio-group {
    padding: 0 16px;
  }
  .nut-radio {
    background: #fff;
  }

  .footer-comfirm {
    height: 60px;
    padding: 16px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    padding-right: 0;
  }
}
</style>
