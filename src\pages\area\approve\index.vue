<template>
  <view class="area-approve">
    <view class="top-filter">
      <view class="filter-item" @click="approveType">
        <text class="title" v-if="!state.params.applyType">申请类型</text>
        <text
          class="title"
          v-if="state.params.applyType"
          style="color: #2551f2"
          >{{ getApproveType() }}</text
        >
        <IconFont
          name="triangle-down"
          color="#2551F2"
          size="8px"
          v-if="state.params.applyType"
        ></IconFont>
        <IconFont
          name="triangle-down"
          color="#4e595e"
          size="8px"
          v-if="!state.params.applyType"
        ></IconFont>
      </view>
      <view class="filter-item" @click="approveStatus">
        <text class="title" v-if="!state.params.status">审批状态</text>

        <text class="title" v-if="state.params.status" style="color: #2551f2">{{
          getApproveStatus()
        }}</text>
        <IconFont
          name="triangle-down"
          color="#2551F2"
          size="8px"
          v-if="state.params.status"
        ></IconFont>
        <IconFont
          name="triangle-down"
          color="#4e595e"
          size="8px"
          v-if="!state.params.status"
        ></IconFont>
      </view>
    </view>
    <scroll-view
      style="height: 528px"
      :scroll-y="true"
      :scroll-top="state.scrollTop"
      @scrolltolower="onScrollToLower"
    >
      <view
        style="
          position: absolute;
          top: 28%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!state.insList.length && !scrollMyFlag"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>
      <view>
        <view v-for="item in state.insList" :key="item.id" class="content">
          <view class="content-item" @click="gotoInst(item)">
            <view>
              <view class="item-top">
                {{ getName(item) }}
              </view>
              <nut-tag plain color="#94BFFF" text-color="#2551F2">
                {{ getApproveTypeItem(item.applyType) }}
              </nut-tag>
              <view class="approvalStatus" :class="statusStyleClass(item)">
                <text style="margin-left: 2px">{{ statusText(item) }}</text>
              </view>
            </view>
            <view style="margin-left: auto">
              <img :src="arrowRight" style="width: 15px; height: 17px"
            /></view>
          </view>
        </view>
      </view>
      <view
        v-if="state.insList.length > 0 && state.insList.length == state.total"
        style="color: #869199; font-size: 16px; text-align: center"
        >已全部加载完毕</view
      >
      <PopupRadio ref="popupRadioRef" @radio-confirm="radioConfirm" />
    </scroll-view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import {
  defineEmits,
  defineProps,
  ref,
  reactive,
  defineExpose,
  computed,
} from "vue";
import arrow from "@/images/arrow.png";
import arrowRight from "@/images/arrow-right.png";
import { approveList } from "@/api/area.js";
import { IconFont } from "@nutui/icons-vue-taro";
import PopupRadio from "@/pages/components/popupRadio/popupRadio";
import noData from "@/images/no-data.png";
const emit = defineEmits(["change-filter"]);
const scrollMyFlag = ref(false);
const initHrInfo = Taro.getStorageSync("initHrInfo");
const applyFormTypeList = Taro.getStorageSync("applyFormTypeList").filter(
  (item) => item.dictValue !== "4"
);
const popupRadioRef = ref(null);

const state = reactive({
  scrollTop: 0,
  search: [],
  params: {
    pageNum: 1,
    pageSize: 10,
    applyType: "1",
    status: "1",
    search: "",
  },
  total: 0,
  pcdvalue: "",
  insList: [],
  list: [
    { text: "待审批", key: "1", active: false },
    { text: "已审批", key: "2", active: false },
    { text: "我发起", key: "3", active: false },
  ],
  statusMap: [
    { code: "1", text: "审批中", color: "approve" },
    { code: "2", text: "主数据清洗中", color: "approve" },
    {
      code: "3",
      text: "主数据审批通过",
      color: "agree",
    },
    { code: "-1", text: "已拒绝", color: "reject" },
    { code: "-2", text: "已撤销", color: "back" },
  ],
  statusMap2: [
    { code: "1", text: "审批中", color: "approve" },
    { code: "2", text: "审批通过", color: "approve" },

    { code: "-1", text: "已拒绝", color: "reject" },
    { code: "-2", text: "已撤销", color: "back" },
  ],
  show1: false,
  statusList: [],
  statusType: applyFormTypeList,
  topType: "",
});

const getList = () => {
  scrollMyFlag.value = true;
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  approveList(state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.data.total;
        if (!state.insList.length) {
          console.log("res.data.rows", res.data.rows);
          state.insList = res.data.rows || [];
        } else {
          state.insList = [...state.insList, ...res.data.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = false;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      scrollMyFlag.value = false;
      Taro.hideLoading();
    });
};

const getName = (item) => {
  if (state.params.applyType === "1" || state.params.applyType === "5") {
    return item.insName;
  } else {
    return item.customerName;
  }
};
const topSearch = (search) => {
  state.params.search = search;
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getList();
};

const approveFilters = () => {
  const info = {
    startDate: state.params.startDate,
    endDate: state.params.endDate,
  };
  Taro.navigateTo({
    url: `/pages/pharmacy/approve/approveFiltes?info=${JSON.stringify(info)}`,
  });
};
const onScrollToLower = () => {
  if (!scrollMyFlag.value && state.insList.length < state.total) {
    state.params.pageNum++;
    getList();
  }
};

const statusText = (i) => {
  console.log("i", i);
  if (!i) return;
  if (i.applyType === "5" || i.applyType === "6") {
    return state.statusMap2.filter((item) => item.code === i.status)[0].text;
  } else {
    return (
      state.statusMap.filter((item) => item.code === i.status)[0].text || ""
    );
  }
};
const statusStyleClass = (i) => {
  if (!i) return;
  if (i.applyType === "5" || i.applyType === "6") {
    return state.statusMap.filter((item) => item.code === i.status)[0].color;
  } else {
    return (
      state.statusMap.filter((item) => item.code === i.status)[0].color || ""
    );
  }
};
const gotoInst = (item) => {
  window.location.href = `${process.env.TARO_APP_APPROVE_URL}/ae/automation/inbox?usingInstanceId=${item.aeFlowId}&hideAction=false&hideForm=false&hideProcess=false&hideTitle=false`;
};

const approveStatus = () => {
  state.topType = "status";
  popupRadioRef.value.open(state.statusList, state.params.status);
};
const approveType = () => {
  state.topType = "type";
  popupRadioRef.value.open(state.statusType, state.params.applyType);
};
const radioConfirm = (val) => {
  console.log("val", val);
  if (state.topType === "type") {
    state.params.applyType = val;
    state.params.status = "";
    const list = state.statusType.filter(
      (item) => item.dictValue === state.params.applyType
    )[0].remark;
    state.statusList = JSON.parse(list).data;
  } else {
    state.params.status = val;
  }
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getList();
};

const getApproveType = () => {
  const list = state.statusType.filter(
    (item) => item.dictValue === state.params.applyType
  );
  return (list[0] && list[0].dictLabel) || "";
};

const getApproveTypeItem = (type) => {
  console.log(type, state.statusType);
  const list = state.statusType.filter((item) => item.dictValue === type);
  return (list[0] && list[0].dictLabel) || "";
};
const getApproveStatus = () => {
  if (!state.params.status && state.statusList.length) return;
  const list = state.statusList.filter(
    (item) => item.dictValue === state.params.status
  );

  return (list[0] && list[0].dictLabel) || "";
};

const initList = () => {
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getList();
  const list = state.statusType.filter(
    (item) => item.dictValue === state.params.applyType
  )[0].remark;
  state.statusList = JSON.parse(list).data;
};

defineExpose({
  topSearch,
  approveFilters,
  initList,
});
</script>
<style lang="scss">
.area-approve {
  padding: 0px 0px;
  width: 100vw;
  position: relative;
  // height: 630px;
  font-family: PingFang SC, PingFang SC;
  .top-filter {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    .filter-item {
      height: 27px;
      padding: 0px 9px;
      border-radius: 16px;
      background: #fff;
      font-size: 14px;
      line-height: 27px;
      margin-right: 8px;
      display: inline-block;

      color: #4e595e;
      border: 1px solid #fff;
      .title {
        margin-right: 4px;
      }
    }
  }
  .content {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    margin-left: 12px;
    margin-right: 12px;
    position: relative;
    .content-item {
      padding: 16px 16px 12px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .item-top {
        font-size: 18px;
        margin-bottom: -5px;
        font-weight: 500;
      }
    }
    .item-time {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #869199;
    }
    .item-type {
      font-size: 14px;
      color: #869199;
    }
  }

  .approve {
    color: #ff7d00;
    background: #fff3e8;
  }
  .agree {
    color: #00b578;
    background: #e6f8f2;
  }
  .reject {
    color: #f32f29;
    background: #feeaea;
  }
  .back {
    color: #4e595e;
    background: #f3f4f5;
  }

  .approvalStatus {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 12px;
    text-align: center;
    height: 20px;
    padding: 0 7px;
    line-height: 20px;
    border-radius: 2px 8px 0 10px;
  }

  .nut-divider.nut-divider-vertical {
    top: 0;
  }
  .add-pharmacy {
    position: absolute;
    right: 16px;
    top: 53%;
  }
  .nut-overlay {
    position: absolute !important;
  }
  .nut-popup {
    position: absolute !important;
  }
}
</style>
