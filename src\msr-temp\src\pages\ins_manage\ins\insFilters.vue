<template>
  <view class="insFilters">
    <CellClick
      @selectClick="selectClick('pcdvalue')"
      :value="state.params.pcdvalue"
      label="省市区"
      placeholder="请选择"
      :phRight="true"
    />
  </view>
  <DistrictPopup ref="districtPopupRef" @district-confirm="districtConfirm" />
  <FooterButtonTwo
    rightText="确定"
    @click-left="clickButtonLeft"
    @click-right="clickButtonRight"
    leftText="重置"
    :plainFlag="false"
    leftColor=""
    leftTextColor="#4E595E"
  />
</template>
<script setup>
import { onMounted, reactive, ref } from "vue";
import Taro, { useRouter } from "@tarojs/taro";
import CellClick from "../../../pages/components/cellClick/index.vue";
import DistrictPopup from "../../../pages/components/districtPopup/index";
import FooterButtonTwo from "../../../pages/components/footerButtonTwo/footerButtonTwo";
import SearchBar from "../../../pages/components/searchBar";

const districtPopupRef = ref(null);
const router = useRouter();
const query = router.params;
const state = reactive({
  params: {
    pcdvalue: "",
    province: "",
    city: "",
    district: "",
  },
});

const selectClick = (type) => {
  if (type === "pcdvalue") {
    districtPopupRef.value.open([
      state.params.province,
      state.params.city,
      state.params.district,
    ]);
  }
};

const districtConfirm = (value, codeList) => {
  state.params.pcdvalue = value;
  state.params.province = codeList[0] || "";
  state.params.city = codeList[1] || "";
  state.params.district = codeList[2] || "";
};

const clickButtonLeft = () => {
  state.params = {
    pcdvalue: "",
    province: "",
    city: "",
    district: "",
  };
};
const clickButtonRight = () => {
  Taro.setStorage({ key: "insFilters", data: state.params });
  Taro.navigateBack({
    delta: 1,
  });
};

onMounted(() => {
  console.log("eeee", query);
  Taro.setNavigationBarTitle({ title: "筛选" });
  const infoString = query.info;
  let infoObject;
  try {
    const decodedInfo = decodeURIComponent(infoString);
    infoObject = JSON.parse(decodedInfo);
    state.params = {
      province: decodeURIComponent(infoObject.province),
      city: decodeURIComponent(infoObject.city),
      district: decodeURIComponent(infoObject.district),
      pcdvalue: decodeURIComponent(infoObject.pcdvalue),
    };
  } catch (error) {
    console.error("解码或解析参数时发生错误:", error);
  }
});
</script>
<style lang="scss">
.insFilters {
  padding: 0 16px;
  background: #fff;
}
</style>
