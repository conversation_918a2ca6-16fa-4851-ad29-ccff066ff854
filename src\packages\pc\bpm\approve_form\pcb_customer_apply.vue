<template>
  <div>
    <div class="text-[#1D212B] text-[14px] py-[10px]">基本信息</div>
    <div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">部门</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.ancestors }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">岗位</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.postName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          客户名称
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.custName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          机构名称
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.insName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          机构主数据编码
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.insMdmCode }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          是否主执业点
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.masterFlag }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          标准科室
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.insDeptName }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">职称</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.professionTechTitle }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">
          行政级别
        </div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.administrationLevel }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">性别</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.sex }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">备注</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{ props.formData.remark }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["formData"]);
</script>
