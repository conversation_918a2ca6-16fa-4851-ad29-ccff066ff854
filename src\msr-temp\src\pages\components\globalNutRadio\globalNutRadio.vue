<template>
  <view class="globalNutRadio">
    <nut-radio :label="props.value">
      <slot name="name" />
      <template #icon>
        <img :src="icon" alt="" class="radioIcon" />
      </template>
      <template #checkedIcon>
        <img :src="checkedIcon" alt="" class="radioIcon" />
      </template>
    </nut-radio>
  </view>
</template>
<script setup>
import icon from "../../../images/radio_icon.png";
import checkedIcon from "../../../images/radio_checked_icon.png";
import { defineEmits, defineProps, nextTick, onMounted, reactive } from "vue";
const props = defineProps({
  value: {
    type: String,
    default: "",
  },
  key: {
    type: String,
    default: "",
  },
});
const state = reactive({});
</script>
<style lang="scss">
.globalNutRadio {
  font-size: 16px;
  .radioIcon {
    width: 20px;
    height: 20px;
    display: inline-block;
  }
}
</style>
