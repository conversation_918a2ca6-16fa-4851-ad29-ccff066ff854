<template>
  <view>
    <view
      class="draggable-item"
      :style="{ left: `${positionX}px`, top: `${positionY}px` }"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      拖拽我
    </view>
  </view>
</template>

<script setup>
import Taro from '@tarojs/taro';
import { onMounted, reactive } from 'vue'

const state = reactive({
  isDragging: false,
  startX: 0,
  startY: 0,
  positionX: 300,
  positionY: 0,
});

const handleTouchStart = (e) => {
  const { clientX, clientY } = e.touches[0];
  state.startX = clientX;
  state.startY = clientY;
  state.isDragging = true;
};

const handleTouchMove = (e) => {
  if (!state.isDragging) return;

  const { clientX, clientY } = e.touches[0];
  const deltaX = clientX - state.startX;
  const deltaY = clientY - state.startY;

  state.positionX += deltaX;
  state.positionY += deltaY;

  state.startX = clientX;
  state.startY = clientY;
};

const handleTouchEnd = () => {
  state.isDragging = false;
};

const getPageBottomCoordinate = () => {
  const query = Taro.createSelectorQuery();
  query.selectViewport().boundingClientRect();
  query.exec((res) => {
    if (res && res[0]) {
      console.log("res[0]",res[0])
      state.positionY = res[0].bottom; 
      // 设置底部坐标
    }
  });
};

onMounted(() => {
  getPageBottomCoordinate();
});
</script>

<style lang="scss">
.draggable-item {
  width: 20px;
  height: 20px;
  position: fixed;
  background-color: lightblue;
  padding: 10px;
  cursor: grab;
}
</style>
