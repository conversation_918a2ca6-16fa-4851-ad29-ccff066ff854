<template>
  <view ref="popupRef" class="dataPicker">
    <nut-popup v-model:visible="state.show" position="bottom">
      <nut-date-picker
        v-model="state.val"
        :min-date="state.min"
        :three-dimensional="false"
        @confirm="confirm"
        @cancel="cancel"
      ></nut-date-picker>
    </nut-popup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
const emit = defineEmits("picker-confirm");
const popupRef = ref(null);
const state = reactive({
  show: false,
  min: new Date(),
  val: new Date(),
});
const confirm = ({ selectedValue }) => {
  state.show = false;
  emit("picker-confirm", selectedValue);
};
const open = (time) => {
  console.log(time);
  state.show = true;
};

const cancel = () => {
  state.show = false;
};

defineExpose({
  open,
});
</script>
<style lang="scss"></style>
