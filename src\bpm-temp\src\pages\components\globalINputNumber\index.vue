<template>
  <view class="globa-input-number">
    <nut-input-number
      v-model="value[0].content"
      :min="0"
      :max="unit === '位' ? 100000 : 100"
    />

    <span class="tag">{{ unit }}</span>
  </view>
</template>
<script setup>
import { reactive } from 'vue';
const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  unit: {
    type: String,
    default: '%',
  },
});
const state = reactive({});
</script>
<style lang="scss">
.globa-input-number {
  position: relative;
  height: 28px;
  font-size: 14px !important;
  .tag {
    position: absolute;
    top: 3px;
    left: 62px;
    color: #1a1a1a;
  }
  .nut-input-number .h5-input,
  .nut-input-number__text--input {
    // width: var(--nut-inputnumber-input-width, 50px);
    padding-right: 25px;
    height: 28px !important;
  }
}
</style>
