<template>
  <view class="gdmap">
    <view style="background: #fff">
      <TopSearch
        @top-search="search"
        placeholder="请输入"
        ref="topSearchRef"
        :searchFlag="true"
      />
    </view>
    <view id="container" />
    <view class="address">
      <nut-radio-group v-model="state.radioChecked" text-position="left">
        <GlobalNutRadio
          v-for="item in state.pois"
          :value="item.id"
          :key="item.id"
        >
          <template #name>
            <view @click="selectAdress(item)">
              <text>{{ item.name }}</text>
            </view>
          </template>
        </GlobalNutRadio>
      </nut-radio-group>
    </view>
  </view>
  <view>
    <view>
      <FooterButton text="确定" @click-button="clickAddButton">
        <template #icon>
          <Uploader color="#fff" />
        </template>
      </FooterButton>
    </view>
  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive } from "vue";
import TopSearch from "../../../pages/components/topSearch/topSearch.vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import GlobalNutRadio from "../../../pages/components/globalNutRadio/globalNutRadio.vue";

const state = reactive({
  leftTetx: "地图",
  text: "确认",
  pois: [],
  value: "",
  tuningMap: null,
  currentMarker: null,
  position: null,
  radioChecked: "",
});

const search = (value) => {
  if (!value) return;
  state.value = value;
  autoInput();
};

const clickAddButton = () => {
  const location = state.pois.filter(
    (item) => item.id === state.radioChecked
  )[0];

  Taro.setStorage({ key: "location", data: location });
  Taro.navigateBack({
    delta: 1,
  });
};

// // 获取搜索信息
const autoInput = () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  AMap.plugin("AMap.AutoComplete", function () {
    var autocomplete = new AMap.AutoComplete();
    autocomplete.search(state.value, function (status, result) {
      if (status === "complete") {
        console.log(result);
        state.pois = result?.tips;
        Taro.hideLoading();
      } else {
        Taro.hideLoading();
        Taro.showToast({
          title: result,
          icon: "none",
          duration: 2000,
        });
      }
    });
  });
};

 

const selectAdress = (item) => {
  state.currentMarker.setPosition([item.location.lng, item.location.lat]);
  state.tuningMap.setCenter([item.location.lng, item.location.lat]);
};

onMounted(() => {
  const map = new AMap.Map("container", {
    zoom: 15,
    resizeEnable: true,
  });
  state.tuningMap = map;
  AMap.plugin(["AMap.Geolocation", "AMap.PlaceSearch"], () => {
    const geolocation = new AMap.Geolocation({
      enableHighAccuracy: true, // 是否使用高精度定位，默认:true
      timeout: 10000, // 超过10秒后停止定位，默认：5s
      position: "RB", // 定位按钮的停靠位置
      offset: [10, 20], // 定位按钮与设置的停靠位置的偏移量，默认：[10, 20]
      zoomToAccuracy: true, // 定位成功后是否自动调整地图视野到定位点
    });

    geolocation.getCurrentPosition((status, result) => {
      if (status === "complete") {
        const { position } = result;
        const { lng, lat } = position;
        map.setCenter([lng, lat]);

        const marker = new AMap.Marker({
          icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
          position: [lng, lat],
          anchor: "bottom-center",
        });
        state.currentMarker = marker;
        map.add(marker);
        map.setFitView();
        const placesearch = new AMap.PlaceSearch({
          pageSize: 10,
          pageIndex: 1,
          types: 120000,
        });
        placesearch.searchNearBy("", [lng, lat], 1000, (status, result) => {
          // console.log(status, result)
          if (status === "complete") {
            state.pois = result?.poiList.pois;
            // console.log('result.poiList.pois', result.poiList.pois)
          }
        });
      } else {
        console.error("定位失败");
      }
    });
  });
});
</script>

<style lang="scss">
.gdmap {
  padding-bottom: 65px;
  .address {
    padding: 0 12px;
    background: #fff;
    height: 230px;
    overflow: hidden;
    overflow-y: scroll;
  }
  .searchBar .nut-searchbar {
    width: 100%;
  }
  .searchBar .nut-searchbar__search-input {
    background: #fff;
  }
}

#container {
  width: 100%;
  height: 300px;
  margin-bottom: 8px;
}

#panel {
  background-color: white;
}
</style>
