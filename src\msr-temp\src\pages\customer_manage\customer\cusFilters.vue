<template>
  <view class="insFilters">
    <CellClick
      @selectClick="selectClick('professionList')"
      :value="state.params.teachTitle"
      label="职称"
      placeholder="请选择"
      :phRight="true"
    />
    <CellClick
      @selectClick="selectClick('adminisLevel')"
      :value="state.params.job"
      label="行政级别"
      placeholder="请选择"
      :phRight="true"
    />
    <PopupRadio ref="cusPopupRef" @radio-confirm="radioConfirm" />
  </view>

  <FooterButtonTwo
    rightText="确定"
    @click-left="clickButtonLeft"
    @click-right="clickButtonRight"
    leftText="重置"
    :plainFlag="false"
    leftColor=""
    leftTextColor="#4E595E"
  />
</template>
<script setup>
import { onMounted, reactive, ref } from "vue";
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import CellClick from "../../../pages/components/cellClick/index.vue";
import FooterButtonTwo from "../../../pages/components/footerButtonTwo/footerButtonTwo";
import PopupRadio from "../../../pages/components/popupRadio/popupRadio";
import SelectIns from "../components/selectIns.vue";
const cusPopupRef = ref(null);
const selectInsRef = ref(null);
const router = useRouter();
const query = router.params;
const state = reactive({
  type: "",
  params: {
    teachTitle: "",
    job: "",
  },
});

const selectClick = (type) => {
  state.type = type;
  let options = [];

  if (type === "professionList") {
    options = Taro.getStorageSync("professionList");
    cusPopupRef.value.open(options, state.params.teachTitle);
  }
  if (type === "adminisLevel") {
    options = Taro.getStorageSync("administrationList");
    cusPopupRef.value.open(options, state.params.job);
  }
};

const radioConfirm = (val) => {
  if (state.type === "professionList") {
    state.params.teachTitle = val;
  }
  if (state.type === "adminisLevel") {
    state.params.job = val;
  }
};
const clickButtonLeft = () => {
  state.params = {
    teachTitle: "",
    job: "",
  };
  state.type = "";
};
const clickButtonRight = () => {
  Taro.setStorage({ key: "cusFilters", data: state.params });
  Taro.navigateBack({
    delta: 1,
  });
};

onMounted(() => {
  Taro.setNavigationBarTitle({ title: "筛选" });
  const infoString = query.info;
  let infoObject;
  try {
    const decodedInfo = decodeURIComponent(infoString);
    infoObject = JSON.parse(decodedInfo);
    state.params.teachTitle = decodeURIComponent(infoObject.teachTitle);
    state.params.job = decodeURIComponent(infoObject.job);
  } catch (error) {
    console.error("解码或解析参数时发生错误:", error);
  }
});
</script>
<style lang="scss">
.insFilters {
  padding: 0 16px;
  background: #fff;
}
</style>
