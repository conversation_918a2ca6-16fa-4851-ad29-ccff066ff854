<template>
  <view class="appraise-page">
    <view class="rate">
      <view class="title-required">协访评分</view>
      <view
        v-for="(item, index) in state.scores"
        :key="index"
        class="rate-content"
        style="padding-left: 8px; margin-top: 10px"
      >
        <view class="rate-title">{{ item.scoreItemName }}</view>
        <nut-rate
          v-model="item.score"
          active-color="#FFB637"
          @change="changeRate"
        />
      </view>
      <view class="rate-score">综合评价{{ state.score }}分</view>
    </view>
    <view class="illustrate">
      <view class="label">协访建议</view>
      <nut-textarea
        v-model="state.assistSuggestInfo"
        limit-show
        max-length="200"
        placeholder="请输入协访建议"
      />
    </view>
    <footer-button
      text="保存"
      :isLoading="state.isLoading"
      @click-button="clickButton"
    />
  </view>
</template>
<script setup>
import { reactive, onMounted } from "vue";
import Taro, { useRouter } from "@tarojs/taro";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { assistEvaluate } from "../../../api/collaborativevisit.js";
import { dictDataList } from "../../../utils/content.js";

const router = useRouter();
const query = router.params;
const state = reactive({
  isLoading: false,
  leftTetx: "协访评价",
  assistSuggestInfo: "",
  scores: [],
  score: 0,
  footerButtontext: "保存",
});

const clickButton = () => {
  const flag = state.scores.some((item) => item.score);
  if (!flag) {
    Taro.showToast({
      title: "请至少选择一项进行评价",
      icon: "none",
      duration: 2000,
    });
  } else {
    state.isLoading = true;
    const params = {
      visitAssistId: query.assistId,
      assistSuggestInfo: state.assistSuggestInfo,
      scores: state.scores,
    };
    assistEvaluate(params)
      .then((res) => {
        state.isLoading = false;

        if (res.code === 200) {
          Taro.showToast({
            title: "评价成功",
            icon: "none",
            duration: 2000,
          });
          Taro.navigateBack({
            delta: 1,
          });
        }
      })
      .catch((err) => {
        state.isLoading = false;
      });
  }
};
const changeRate = () => {
  const totalValue = state.scores.reduce((sum, item) => sum + item.score, 0);
  state.score = (totalValue / state.scores.length).toFixed(1);
};

onMounted(() => {
  Taro.setNavigationBarTitle({
    title: "协访评价",
  });
  if (query.obj) {
    const obj = JSON.parse(decodeURIComponent(query.obj));
    state.scores = obj.scores.map((item) => {
      return {
        ...item,
        score: Number(item.score),
      };
    });
    state.score = Number(obj.total);
    state.assistSuggestInfo = obj.assistSuggestInfo;
  } else {
    state.scores = dictDataList;
  }
});
</script>
<style lang="scss">
.appraise-page {
  font-size: 16px;
  .illustrate {
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;

    .label {
      margin-bottom: 16px;
    }

    .nut-textarea {
      height: 65px;
      padding: 0;
      height: 88px;
      background: #f4f5f7;
    }

    .nut-input {
      padding: 0;
    }

    textarea {
      left: -5px;
      z-index: 0;
      height: 88px;
    }

    .nut-textarea__textarea {
      padding: 16px;
    }
  }

  .rate {
    background-color: #fff;
    padding: 16px;
    .title-required {
      &::before {
        content: "*";
        color: red;
        margin-top: 2px;
        margin-right: 5px;
      }
    }
    .rate-content {
      height: 40px;
      display: flex;
      align-items: center;
      .rate-title {
        color: #000;
        font-weight: 400;
        line-height: 22px;
        font-size: 14px;
      }

      .nut-rate {
        margin-left: 20px;
      }
    }
    .rate-score {
      text-align: right;
      margin-top: 16px;
      font-size: 14px;
    }
  }
}
</style>
