<template>
  <view class="signIn">
    <SignTime
      signType="signIn"
      :visitUserName="initUserInfo.nickName"
      status="0"
    />
    <view class="sign-select">
      <CellClick
        @selectClick="selectInsClick"
        :value="state.params.institutionName"
        :required="true"
        label="医疗机构"
        placeholder="请选择"
        :phRight="true"
      />

      <CellClick
        @selectClick="selectTypeClick"
        :value="state.params.visitTypeName"
        label="拜访类型"
        placeholder="请选择"
        :required="true"
        :phRight="true"
      />
      <!-- 使用 form-create-mobile 组件 -->
      <form-create-mobile
        :rule="rule"
        v-model:api="fApi"
        v-model="formData"
        :option="options"
      />
      <UploadImage ref="uploadImageRef" :params="state.params" type="signIn" />
    </view>
    <SignInOut
      type="signIn"
      :signStatus="state.status"
      @refresh-location="refreshLocation"
      @sign="signIn"
      :signAddress="state.params.signInAddress"
      :distance="state.params.signInDistance"
      :institutionName="state.params.institutionName"
      :insLonNoFlag="state.insLonNoFlag"
      @calibration="calibration"
    />
  </view>
  <SelectIns ref="insPopupRef" @ins-confirm="insConfirm" />
  <VisitTypePopup
    @visit-type="visitTypeSelect"
    ref="visitTypePopupRef"
  ></VisitTypePopup>
  <CalibrationDialog
    ref="calibrationDialogRef"
    @selectLocation="selectLocation"
  />
</template>

<script setup>
import { onMounted, reactive, ref, onUnmounted, onDeactivated } from "vue";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import SignTime from "./components/signTime";
import CellClick from "../../../pages/components/cellClick/index.vue";
import SignInOut from "./components/signInOut";
import VisitTypePopup from "../components/visitTypePopup";
import {
  validIsNotSignOut,
  visitDistance,
  crmSinInM,
} from "../../../api/institutionalVisitsApi";
import { insSign } from "../../../api/insSign";
import SelectIns from "../components/selectIns";
import UploadImage from "../../../pages/components/uploadImage/index.vue";
import { apiAuth } from "../../../utils/feishuAuth";
import { formListApi } from "../../../api/bpm";
import formCreate from "@form-create/element-ui";

import CalibrationDialog from "../../components/calibrationDialog.vue";
const router = useRouter();
const query = router.params;
const insPopupRef = ref(null);
const visitTypePopupRef = ref(null);
const initUserInfo = Taro.getStorageSync("initUserInfo").user;
const uploadImageRef = ref(null);
const calibrationDialogRef = ref(null);
const visitTypeList = Taro.getStorageSync("visitTypeList");

const fApi = ref({});
const formData = ref({
  visitCustomerType: "存量有客情",
});
const options = {
  onSubmit: (formData) => {
    // alert(JSON.stringify(formData)); // 提交表单时弹出表单数据
    // signIn();
  },
  labelWidth: "9rem",
  resetBtn: false, // 显示重置按钮
  submitBtn: false,
};
const option = ref({});
const rule = ref([]);

const state = reactive({
  // 拜访类型字段
  status: "", // 0 定位失败 1定位成功
  params: {
    isPlan: "0", //根据拜访状态判断,
    signType: "0",
    visitTypeCode: "0",
    visitTypeName: "院内拜访",
    signInLat: "",
    signInLon: "",
    institutionCode: "",
    institutionName: "",
    signInDistance: 0,
    signInAddress: "",
    insLat: null,
    insLon: null,
  },
  insLonNoFlag: false,
  showPreview: false,
  imgData: [],
  crmSinInMInfo: {},
  calibrationData: {},
});
const selectInsClick = () => {
  insPopupRef.value.open();
};

const insConfirm = (obj) => {
  state.params.institutionCode = obj.insCode;
  state.params.institutionName = obj.insName;
  state.params.insLat = obj.latitude;
  state.params.insLon = obj.longitude;
  state.params.jurCode = obj.jurCode;
  state.calibrationData = obj;
  console.log("EEEEEE");
  formData.value["address"] = obj.address;
  getLocation();
};
const selectTypeClick = () => {
  if (state.id) return;
  visitTypePopupRef.value.open();
};
const visitTypeSelect = (name, code) => {
  state.params.visitTypeName = name;
  state.params.visitTypeCode = code;
};

function isPhoneNumber(input) {
  // 定义手机号码的正则表达式
  const pattern = /^1\d{10}$/;

  // 使用正则表达式进行匹配
  return pattern.test(input);
}

function isLandlinePhoneNumber(phoneNumber) {
  // 定义座机号码的正则表达式模式
  const pattern = /0\d{2,3}\d{7,8}/;
  // 或者八位数字
  const pattern2 = /^\d{8}$/;

  // 使用正则表达式进行匹配
  return pattern.test(phoneNumber) || pattern2.test(phoneNumber);
}

const signIn = async () => {
  fApi.value.submit(async (_formData) => {
    // const visitCustomerContact = _formData && _formData.visitCustomerContact || '';
    // if(!(isLandlinePhoneNumber(visitCustomerContact) || isPhoneNumber(visitCustomerContact))) {
    //   Taro.showModal({
    //     content: '请输入正确手机号/座机号',
    //     confirmText: "确定",
    //     duration: 2000,
    //     showCancel: false,
    //   })
    //   return false;
    // }

    if (
      state.crmSinInMInfo.remark === "1" &&
      Number(state.params.signInDistance) >
        Number(state.crmSinInMInfo.configValue)
    ) {
      Taro.hideLoading();
      return Taro.showModal({
        content: state.crmSinInMInfo.configName,
        confirmText: "确定",
        duration: 2000,
        showCancel: false,
      });
    }

    const parmas = { visitUserHrCode: initUserInfo.userName, visitLevel: "0" };
    const { data } = await validIsNotSignOut(parmas);
    if (data) {
      Taro.hideLoading();
      return Taro.showModal({
        content: `您有正进行的拜访，请先结束拜访`,
        confirmText: "去结束",
        success: async function (res) {
          if (res.confirm) {
            Taro.navigateTo({
              url: `/pages/insSignSkn/visiting?id=${data}`,
            });
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    }

    let signInPhotoList = [];
    if (uploadImageRef.value.photoList.length) {
      signInPhotoList = uploadImageRef.value.photoList.map((item) => {
        return {
          attUrl: item.originalUrl,
          ossId: item.ossId,
        };
      });
    }

    const params = {
      ...state.params,
      extendInfo: JSON.stringify(formData.value),
      signInPhotoList,
    };
    // 签到接口
    const res = await insSign(params);
    if (res.code === 200) {
      Taro.showToast({
        title: "签到成功",
        icon: "none",
        duration: 2000,
      });
      Taro.redirectTo({
        url: `/pages/insSignSkn/visiting?id=${res.data.visitInstitutionId}`,
      });
    } else {
      Taro.hideLoading();
      if (res?.msg)
        Taro.showToast({
          title: res?.msg,
          icon: "none",
        });
    }
  });
};

const refreshLocation = () => {
  getLocation("refre");
};

const getDistance = (type) => {
  if (state.params.insLon && state.params.insLon != "0.0") {
    const params = {
      origins: `${state.params.signInLon},${state.params.signInLat}`,
      destination: `${state.params.insLon},${state.params.insLat}`,
    };

    visitDistance(params)
      .then((res) => {
        if (res.code === 200 && res?.data.length != 0) {
          state.params.signInDistance = res?.data?.[0];
          state.status = "1";
          if (type) {
            Taro.showToast({
              title: "刷新成功",
              icon: "none",
              duration: 2000,
            });
          }
        } else {
          state.status = "1";
          Taro.showToast({
            title: "获取机构位置与当前位置距离失败",
            icon: "none",
            duration: 2000,
          });
        }
      })
      .catch((res) => {
        state.status = "1";
        Taro.showToast({
          title: "获取机构位置与当前位置距离失败",
          icon: "none",
          duration: 2000,
        });
      });
  } else {
    state.status = "1";
    state.params.signInDistance = 0;
    state.insLonNoFlag = true;
  }
};
const getConvert = (longitude, latitude) =>
  new Promise((res) => {
    Taro.request({
      url: `https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${longitude},${latitude}&key=0446b950e065d1e0b98a9e97f08dfbff&coordsys=gps`,
      success: (result_before) => {
        const [lon, lat] = result_before.data.locations.split(",");
        res({ lon, lat });
      },
    });
  });

const getLocation = (type = "", isSign = true) => {
  if ("geolocation" in navigator) {
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const latitude = position.coords.latitude; // 纬度
        const longitude = position.coords.longitude; // 经度
        const { lon, lat } = await getConvert(longitude, latitude);
        await Taro.request({
          url: `https://restapi.amap.com/v3/geocode/regeo?location=${lon},${lat}&key=0446b950e065d1e0b98a9e97f08dfbff`,
          success: (result) => {
            state.params.signInAddress =
              result.data.regeocode.formatted_address;

            state.params.signInLat = latitude;
            state.params.signInLon = longitude;
            if (isSign) getDistance(type);
          },
          fail: (res) => {
            Taro.showToast({
              title: "定位失败",
              icon: "none",
              duration: 2000,
            });
            state.status = "0";
          },
        });
      },
      function (error) {
        Taro.showToast({
          title: "定位失败",
          icon: "none",
          duration: 2000,
        });
        state.status = "0";
      },
      {}
    );
  } else {
    Taro.showToast({
      title: "请开启定位权限",
      icon: "none",
      duration: 2000,
    });
  }
};
const getCrmSinInM = async () => {
  const res = await crmSinInM();
  state.crmSinInMInfo = res.rows[0] || {};
};

const getFormRule = async () => {
  try {
    const res = await formListApi({
      code: "pk1",
    });
    const _ = (res.rows && res.rows[0]) || {};
    rule.value = _.formContentH5 && formCreate.parseJson(_.formContentH5).rule;
  } catch (e) {
    /* empty */
  }
};

const calibration = () => {
  calibrationDialogRef.value.open(state.calibrationData);
};

const selectLocation = (location) => {
  console.log("定位校准", location);
  state.insLonNoFlag = false;
  state.params.insLat = location.location.lat.toString();
  state.params.insLon = location.location.lng.toString();
  getDistance();
};
onMounted(() => {
  getFormRule();
  getCrmSinInM();
  Taro.setNavigationBarTitle({ title: "机构签到" });
  apiAuth();
  const info = Taro.getStorageSync("insInfo");
  Taro.removeStorageSync("insInfo");
  if (info) {
    state.params.institutionCode = info.insCode;
    state.params.institutionName = info.insName;
    state.params.insLat = info.latitude;
    state.params.insLon = info.longitude;
    state.params.jurCode = info.jurCode;
    state.calibrationData = info;
    formData.value["address"] = info.address;
    getLocation();
  } else {
    getLocation("", false);
  }
});
</script>

<style lang="scss">
.sign .nut-cell__value {
  font-size: 16px;
}
.signIn {
  color: #1d212b;
  padding-top: 16px;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC-Regular;

  .van-field__label {
    width: 6rem;
    margin: 0;
    font-size: 0.8rem;
    padding: 0.85rem 0;
    border-bottom: 0.05rem solid #f4f5f7;
    text-align: left;
  }
  .van-cell__value .van-field__value {
    padding: 0.85rem 0;
    font-size: 0.8rem;
  }

  .van-cell__right-icon {
    margin-top: 18px;
  }
  .van-cell {
    padding-left: 0;
    padding-right: 0;
  }

  .sign-select {
    margin: 12px;
    padding: 16px;
    padding-top: 2px;
    margin-top: 8px;
    background: #fff;
    border-radius: 8px;
    .photograph {
      //display: flex;
      //justify-content: space-between;
      .photo-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .photo {
          width: 80px;
          height: 80px;
          position: relative;
          overflow: hidden;
          .del {
            position: absolute;
            font-size: 12px;
            right: -12px;
            top: -10px;
            width: 24px;
            height: 24px;
            background: black;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            color: #fff;
            padding-top: 7px;
            padding-right: 7px;
          }
          img {
            width: 100%;
          }
        }
      }
    }
  }
  .nut-button--plain.nut-button--primary {
    border-color: #e5e6eb;
  }
  .photograph {
    display: block !important;
  }
}
</style>
