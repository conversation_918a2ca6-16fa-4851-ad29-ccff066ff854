<template>
  <view class="ins-addIns">
    <view>
      <nut-form :model-value="props.formData" ref="ruleForm">
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">基本信息</view>
        <view class="section">
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">姓名</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.name }}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">医疗机构</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.insName }}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">讲者类型</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.type }}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">性别</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.sexName }}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">标准科室</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.deptName }}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">职称</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.professionTechTitle }}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">行政级别</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.job }}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">省市区</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{props.formData.province}}{{props.formData.city}}{{props.formData.district}}</p>
          </div>
        </view>
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">讲者信息</view>
        <view>
          <view class="section">
            <div class="sm:pb-0">
              <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">讲者级别</p>
              <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.academicRating }}</p>
            </div>
            <div class="sm:pb-0">
              <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">是否退休返聘</p>
              <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.isRehired }}</p>
            </div>
            <div class="sm:pb-0">
              <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">电子邮箱</p>
              <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.email }}</p>
            </div>
            <div class="sm:pb-0">
              <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">手机号码</p>
              <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ relaceApi(props.formData.phone) }}</p>
            </div>
            <div class="sm:pb-0">
              <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">个人家庭住址</p>
              <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ relaceApi(props.formData.homeAddress) }}</p>
            </div>
          </view>
        </view>
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">补充-身份证</view>
        <view class="section">
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">证件类型</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{ props.formData.idCardName }}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">身份证号码</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{relaceApi(props.formData.idCardNum)}}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">地址</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{relaceApi(props.formData.idCardAddress)}}</p>
          </div>

        </view>
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">补充-银行卡</view>
        <view class="section">
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">开户银行</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{relaceApi(props.formData.bankName)}}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">银行卡号</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{relaceApi(props.formData.bankNum)}}</p>
          </div>
          <div class="sm:pb-0">
            <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">开户省市区/县</p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{relaceApi(props.formData.bankDistrict)}}</p>
          </div>
          <view class="photo">
            <div class="sm:pb-0">
              <p class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950">备注</p>
              <p class="mt-1 max-w-lg text-sm/6 text-gray-600">{{props.formData.remark}}</p>
            </div>
            <nut-form-item label="附件" prop="fileList" star-position="left">
              <view class="upload">
                <view class="item-img" v-for="(item, index) in state.fileList" :key="index">
                  <img :src="item.url || item.attUrl" style="width: 80px; height: 80px" @click="handlePreview(index)"/>
                  <img :src="fileClose" class="file-close"/>
                </view>
                <view>
                  <img :src="CameraImg" class="camera-img" />
                </view>
              </view>
            </nut-form-item>
            <nut-image-preview v-if="props.formData.fileList" :autoplay="0" :show="showPreview" :images="state.fileList.map((d) => ({ src: d.url || d.attUrl }))
              " @close="hideImgPreview" :init-no="imgPreviewIndex" />
          </view>
        </view>
      </nut-form>
    </view>
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
import CameraImg from "@/images/uploader.png";
import fileClose from "@/images/file-close.png";
import { imageUrl } from "@/bpm-temp/src/api/bpm"

const initHrInfo = Taro.getStorageSync("000010-initHrInfo");
const orgList = initHrInfo?.orgList?.filter((item) => item.postType === "1")[0] || {};
const router = useRouter();
const query = router.params;
const ruleForm = ref(null);
const imgPreviewIndex = ref(0);
const showPreview = ref(0);
const processId = ref('');

const props = defineProps(['formData'])
const state = reactive({
  rightText: "确定", //根据进来的情况判断
  fileList: [],
  info: {
    applicant: initHrInfo.empName,
    applicantCode: initHrInfo.empCode,
    postCode: orgList.code,
    postName: orgList.name,
    deptCode: orgList.deptCode,
    deptName: orgList.value,
    ancestors: orgList.ancestors,
    postIdList: initHrInfo.postIdList,
  },
  params: {
    name: '',
    insName: '',
    insCode: '',
    type: '',
    isRehired: '',
    provinceCode: '',
    cityCode: '',
    districtCode: '',
    district: '',
    academicRating: '',
    brandRating: '',
    accessRating: '',
    antiAgingRating: '',
    therapyArea: '',
    sex: '',
    sexName: '',
    phone: '',
    email: '',
    idCardType: '',
    idCardName:'',
    idCardNum: '',
    idCardAddress: '',
    bankCityCode: '',
    bankNum: '',
    bankProvinceCode: '',
    bankDistrictCode: '',
    bankDistrict: '',
    remark: '',
    fileList: []
  },
});

const handlePreview = (index) => {
  imgPreviewIndex.value = index;
  showImgPreview();
};

const hideImgPreview = () => {
  showPreview.value = false;
};

const showImgPreview = () => {
  showPreview.value = true;
};

const viewList = (list) => {
  list.forEach(async (item) => {
    const res = await imageUrl(item);
    state.params.fileList.push(res.data.rows[0]);
  });
};

const relaceApi = (value) => {
  if(value) return  '*'.repeat(value.length);
}

onMounted(() => {
  if (props.formData.fileList) {
    props.formData.ossIdList = [];
    // props.formData.fileList = props.formData.fileList;
    props.formData.fileList.forEach(async (i) => {
      const res = await imageUrl(i.ossId);
      props.formData.ossIdList.push(res.data.rows[0].url);
      state.fileList.push({ url: res.data.rows[0].url });
    });
  }
});

</script>
<style lang="scss">
.ins-addIns {
  padding-bottom: 90px;

  .section {
    font-size: 14px;
    padding: 0 16px;
    background: #fff;

    .label {
      color: #1d212b;

      &::before {
        content: "*";
        color: red;
        vertical-align: middle;
        margin-right: 5px;
      }
    }

    .photos {
      width: 164px;
      height: 122px;
      background-color: #F3F4F5;
      text-align: center;
    }

    .photo {
      .nut-cell {
        display: block;
      }

      .nut-form-item__label.nut-cell__title {
        width: 100%;
      }

      .upload {
        margin-top: 8px;
        display: flex;

        .item-img {
          position: relative;
          margin-right: 8px;

          .file-close {
            width: 20px;
            height: 20px;
            position: absolute;
            top: 0;
            right: 0;
          }
        }

        .camera-img {
          width: 80px;
          height: 80px;
        }
      }
    }
  }

  .nut-cell__title {
    color: #1d212b;
  }

  .nut-input-left-box {
    margin-right: 10px;
  }

  .arrowIcon {
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }

  .location_ins {
    width: 24px;
    height: 24px;
  }

  .nut-cell-group__wrap {
    margin-top: 0;
    background: none;
  }

  .nut-cell__title {
    font-size: 16px;
  }

  // .nut-input__inner {
  //   white-space: pre-wrap !important;
  // }
  // .nut-input {
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-form-item__body__slots {
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-cell__value {
  //   display: block;
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-cell__value .nut-form-item__body {
  //   display: block;
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
}
</style>
