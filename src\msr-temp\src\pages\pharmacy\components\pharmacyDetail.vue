<template>
  <view class="pharmacyDetail">
    <view class="section">
      <view class="cus-information">
        <view class="information-name">
          <view
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <view
              style="
                margin-right: 8px;
                color: #1d212b;
                font-size: 20px;
                font-weight: 500;
              "
              >{{ state.info.dsName }}
              <nut-tag
                plain
                color="#94BFFF"
                text-color="#2551F2"
                style="vertical-align: text-bottom"
              >
                {{ state.info.dsMdmCode }}
              </nut-tag></view
            >
          </view>
        </view>

        <view style="display: flex; align-items: start">
          <img :src="location_a" alt="" class="icons" />
          <view>{{ decodeURIComponent(query.address) }}</view>
        </view>
      </view>
    </view>
    <PharmacyDetail ref="insDetailRef" />
  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref, computed } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { Edit } from "@nutui/icons-vue-taro";
import PharmacyDetail from "../components/pharmacyTabDetail.vue";
import { relationDrugstoreDetail } from "../../../api/pharmacy.js";

import location_a from "../../../images/ph_address.png";
const router = useRouter();
const query = router.params;
const insDetailRef = ref(null);
const state = reactive({
  info: {},
  approvalStatus: "1",
});

const getDetail = async () => {
  Taro.showToast({
    title: "加载中",
    icon: "loading",
  });
  const res = await relationDrugstoreDetail(query.id);
  state.info = res;
  insDetailRef.value.setInfo(res);
  Taro.hideLoading();
};

useDidShow(async () => {
  Taro.setNavigationBarTitle({ title: "药店详情" });
  await getDetail();
});
</script>

<style lang="scss">
.pharmacyDetail {
  padding-bottom: 93px;
  .section {
    font-size: 14px;
    color: #869199;
    background: #fff;

    padding: 16px;
    .cus-information {
      padding: 16px;
      background: #f4f5f7;
      background-image: url("../../../images/card-bg.png");
      background-size: 100% 100%;
      .information-name {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .hosType {
        height: 22px;
        border-radius: 2px 2px 2px 2px;
        border: 1px solid #94bfff;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #2551f2;
        line-height: 22px;
        padding: 0 4px;
        margin-right: 4px;
      }
    }
  }

  .icons {
    width: 14px;
    height: 14px;
    display: block;
    margin-right: 4px;
    margin-top: 3px;
  }
}
</style>
