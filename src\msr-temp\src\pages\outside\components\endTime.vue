<template>
  <view>
    <GlobalPopup ref="popupRef">
      <view style="padding: 16px; height: 350px">
        <view class="timePopup-content">
          <nut-date-picker
            v-model="val"
            type="datetime"
            :min-date="min"
            :max-date="max"
            :three-dimensional="false"
            :filter="filter"
            @confirm="confirm"
            @cancel="cancel"
          ></nut-date-picker>
        </view>
      </view>
    </GlobalPopup>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { ref, onMounted, reactive } from "vue";
import GlobalPopup from "@/pages/components/globalPopup/globalPopup.vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
const emit = defineEmits("endTime");
const popupRef = ref(null);
const min = new Date(dayjs().startOf("year").format());
const max = new Date(dayjs().add(1, "year").endOf("day").format());
const val = ref(new Date(dayjs().add(1, "day").startOf("day")));
const state = reactive({
  endTime: [],
});
const confirm = ({ selectedValue }) => {
  console.log("selectedValue", selectedValue);
  const [year, month, day, hour] = selectedValue;
  let formattedHour = hour === "00" ? "00" : "12";
  state.endTime = [year, month, day, formattedHour];

  popupRef.value.close();
  emit("endTime", state.endTime);
};
const filter = (type, options) => {
  if (type === "minute") {
    return [];
  }
  if (type === "hour") {
    return (options = [
      { text: "上午", value: "00" },
      { text: "下午", value: "12" },
    ]);
  }
  return options;
};
const open = (start) => {
  val.value = new Date(dayjs(start.slice(0, -1).join("-")).startOf("day"));
  popupRef.value.open();
};
const cancel = () => {
  popupRef.value.close();
};
defineExpose({
  open,
});
</script>

<style lang="scss"></style>
