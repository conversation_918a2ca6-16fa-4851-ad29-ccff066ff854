<template>
  <div class="mt-[10px] px-[12px]">
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <div v-for="item in props.list" :key="item.processId" class="relative rounded-[8px] bg-white p-[12px] mb-[8px]" @click="handleToDetail(item)">
        <!--      <div class="absolute top-0 right-0 text-[12px] text-[#F77234] text-center px-[6px] h-[25px] leading-[25px] bg-[#FFF3E8]" style="border-radius: 0 8px 0 8px">审批中</div>-->
        <div class="absolute top-0 right-0">
          <ProcessStatusTag :instanceState="item.instanceState" />
        </div>
        <div class="header flex">
          <div class="name text-[18px] text-[#1D212B]">{{ item.processName }}</div>
        </div>
        <div class="flex justify-between items-center mt-[10px]">
          <div class="flex items-center">
            <div class="w-[25px] h-[25px] rounded-[50%] bg-[#92A8F8] text-center leading-[25px] text-[12px] text-white">{{ item?.startUser?.name?.slice(-1) }}</div>
            <div class="text-[14px] text-[#1D212B] pl-[8px]">{{ item?.startUser?.name }}</div>
          </div>
          <div class="flex">
            <div class="text-[#4E595E] text-[14px]">{{ item.taskStartTime }}</div>
          </div>
        </div>
      </div>
    </van-list>

  </div>
</template>
<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import ProcessStatusTag from "./processStatusTag.vue";
const router = useRouter();

const loading = ref(false);
const finished = ref(false);

const props = defineProps({
  list: {
    type: Array,
    default: []
  },
  onLoad: {
    type: Function,
    default: () => {}
  },
  total: {
    type: Number,
    default: 0
  },
  active: {
    type: String,
    default: '1'
  }
})
const handleToDetail = (item) => {
  router.push({
    path: '/approve/detail',
    query: {
      processId: item.processId,
      instanceId: item.instanceId,
      currentNode: item.currentNode,
      taskId: item.taskId || '',
      instanceState: item.instanceState
    }
  })
}

const onLoad = async () => {
  loading.value = true;
  await props.onLoad(props.active, true);
  loading.value = false;
  finished.value = props.list.length === props.total
}

watch(
  () => ([props.list, props.total]),
  () => {
    console.log(props.list, props.total, 'props.list, props.total')

  }
)
</script>
<style lang="scss"></style>