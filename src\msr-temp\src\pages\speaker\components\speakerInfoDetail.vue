<template>
  <view class="speakerInfoDetail">
  
      <DetailItem label="性别" :value="state.info.sex === '1' ? '女' : state.info.sex === '0' ? '男' : '' " />

      <DetailItem label="行政级别" :value="state.info.job" />
 
      <DetailItem
        label="省市区"
        :value="`${state.info.province || ''} ${state.info.city || ''} ${
          state.info.district || ''
        }`"
      />
      <DetailItem label="是否退休返聘" :value="state.info.isRehired" />
      <DetailItem label="学术评级" :value="state.info.academicRating" />
      <DetailItem
        label="品牌评级"
        :value="state.info.brandRating"
      />
      <DetailItem label="准入评级" :value="state.info.accessRating" />
      <DetailItem label="抗衰凭借" :value="state.info.antiAgingRating" />
      <DetailItem label="治疗领域" :value="state.info.therapyArea" />
    </view>

</template>
<script setup>
import { View } from "@tarojs/components";
import { reactive } from "vue";
import DetailItem from "../../../pages/components/detailItem/index.vue";
const state = reactive({
  info: {
    fileList: [],
  },
});

const setInfo = (info) => {
  state.info = info;
};

defineExpose({
  setInfo,
});
</script>
<style lang="scss">
.speakerInfoDetail::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}
.speakerInfoDetail {
  font-size: 14px;
  color: #869199;
  // background: #fff;
  overflow: hidden;
  overflow-y: scroll;
  font-size: 14px;

  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;

  .bcfff {
    background: #fff;
    padding: 12px 16px;
    border-radius: 8px;
    margin: 12px;
  }
  .title {
    font-size: 20px;
    color: #1d212b;
  }
  .cusDetail-item {
    margin-top: 10px;
    .cusDetail-item-title {
      display: flex;
      align-items: center;
      padding: 6px 0;
      .label {
        width: 30%;
      }
      .value {
        color: #1d212b;
        width: 70%;
        display: block;
      }
    }
  }
}
</style>
