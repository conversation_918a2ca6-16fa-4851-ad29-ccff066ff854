<template>
  <view class="popupRadio">
    <GlobalPopup ref="popupRef">
      <TopSearch
        v-if="props.needSearch"
        @top-search="search"
        searchFlag
        :placeholder="props.placeholder"
      />
      <view display="flex">
        <view class="radio-group">
          <nut-radio-group
            v-if="state.options?.length != 0"
            v-model="state.radioChecked"
            text-position="left"
          >
            <GlobalNutRadio
              v-for="item in state.options"
              :value="item.dictValue"
              :key="item.dictValue"
            >
              <template #name>
                <view>
                  {{ item.dictLabel }}
                </view>
              </template>
            </GlobalNutRadio>
          </nut-radio-group>
          <view class="null-text" v-else>{{ "无数据" }}</view>
        </view>
        <view
          class="footer-button"
          @click="radioConfirm"
          style="z-index: 999999"
          >确定</view
        >
      </view>
    </GlobalPopup>
  </view>
</template>
<script setup>
import TopSearch from "../../../pages/components/topSearch/topSearch.vue";
import Taro, { useRouter } from "@tarojs/taro";
import { reactive, ref, defineProps } from "vue";
import GlobalPopup from "../../../pages/components/globalPopup/globalPopup.vue";
import GlobalNutRadio from "../../../pages/components/globalNutRadio/globalNutRadio.vue";
const emit = defineEmits(["radio-confirm", "search"]);
// const ready = ref(true);

const props = defineProps({
  placeholder: {
    type: String,
    default: "输入后搜索",
  },
  needSearch: {
    type: Boolean,
    default: false,
  },
});
const popupRef = ref(null);
const state = reactive({
  options: [],
  radioChecked: "",
});
const search = (name) => {
  emit("search", name, () => {
    // ready.value = false;
  });
};

const open = (option, key) => {
  // Taro.showLoading({
  //   mask: true,
  //   title: "加载中",
  // });
  document.body.style.overflow = "hidden";
  popupRef.value.open();
  state.options = option;
  Taro.hideLoading();

  if (key) {
    state.radioChecked = key;
  }
};
const radioConfirm = () => {
  document.body.style.overflow = "";
  popupRef.value.close();
  emit("radio-confirm", state.radioChecked);
  state.radioChecked = "";
};

defineExpose({
  open,
});
</script>
<style lang="scss">
.null-text {
  max-height: 60vh;
  min-height: 10vh;
  text-align: center;
  color: #869199;
  font-size: 14px;
  padding-top: 30px;
}
.popupRadio {
  background: #f4f5f7;
  .radio-group {
    max-height: 450px;
    min-height: 150px;
    overflow: hidden;
    overflow-y: scroll;
    background: #fff;
  }
  .footer-button {
    margin-top: 8px;
    color: #2551f2;
    background-color: #ffffff;
    height: 40px;
    padding-top: 14px;
    text-align: center;
  }
  .footer-comfirm {
    height: 60px;
    padding: 16px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .nut-popup {
    margin-top: 6px;
    // padding-bottom: 110px;
    background: #f4f5f7;
  }
  .nut-radio-group {
    padding: 0 16px;
  }
}
</style>
