export default defineAppConfig({
  animation: {
    duration: 0,
    delay: 50,
  },
  pages: [
    "pages/msr/index",
    "pages/login/index",
    "pages/index/index",
    "pages/institutionalVisits/index",
    "pages/collaborativevisit/index",
    "pages/institutionalVisits/visiting",
    "pages/institutionalVisits/components/selectProducts",
    "pages/institutionalVisits/temporary",
    "pages/collaborativevisit/assistDetail",
    "pages/institutionalVisits/creacWaitplan/index",
    "pages/institutionalVisits/planDetail",
    "pages/institutionalVisits/sign/signIn",
    "pages/collaborativevisit/components/appraise",
    "pages/area/index",
    "pages/area/customer/cusDeatil",
    "pages/area/ins/insDeatil",
    "pages/area/ins/insFilters",
    "pages/area/customer/cusFilters",
    "pages/area/components/editProduct",
    "pages/area/components/claimProduct",
    "pages/area/ins/search",
    "pages/area/customer/search",
    "pages/area/customer/addCus",
    "pages/area/ins/addIns",
    "pages/pharmacy/index",
    "pages/pharmacy/approve/approveFiltes",
    "pages/pharmacy/components/search",
    "pages/pharmacy/components/pharmacyDetail",
    "pages/pharmacy/components/addPharmacy",
    "pages/components/gdmap",
    "pages/association/index",
    "pages/association/detail/index",
    "pages/target/index",
    "pages/target/detail",
    "pages/insSign/index",
    "pages/insSign/visiting",
    "pages/insSign/sign/signIn",
  ],
  window: {
    backgroundTextStyle: "light",
    navigationBarBackgroundColor: "#fff",
    navigationBarTextStyle: "black",
    // navigationStyle: "custom",
  },
  permission: {
    "scope.userLocation": {
      desc: "获取地理位置信息的用途描述",
    },
  },
});
