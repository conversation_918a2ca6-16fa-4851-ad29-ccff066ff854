<template>
  <view class="speakerpaymentDetail">
  
      <DetailItem label="身份证号" :value="relaceApi(state.info.idCardNum)" />
      <DetailItem label="开户银行" :value="relaceApi(state.info.bankName)" />
      
      <DetailItem label="银行卡号" :value="relace4Api(state.info.bankNum)" />
      <DetailItem label="开户省市区" :value="relaceApi(`${state.info.bankProvinceCode || ''} ${state.info.bankCityCode || ''} ${
          state.info.bankDistrictCode || ''
        }`)" />
      <DetailItem
        label="备注"
        :value="state.info.remark"
      />
      <DetailItem
        :isImageUrl="true"
        label="附件"
        :value="state.fileList"
      />
    </view>

</template>
<script setup>
import { View } from "@tarojs/components";
import { reactive } from "vue";
import DetailItem from "../../../pages/components/detailItem/index.vue";
import { imageUrl } from "../../../api/institutionalVisitsApi";
const state = reactive({
  info: {
    fileList: [],
  },
  fileList:[]
});
const viewList = (list) => {
 list.forEach(async (item) => {
    const res = await imageUrl(item);
    state.fileList.push(res.data.rows[0].url);
  });
};
const setInfo = (info) => {
  state.info = info;
  if(state.info.fileList) {
    viewList(state.info.fileList)
  }
};
const relace4Api = (value) => {
  if(!value) return 
  const maskLength = value.length - 4;
  const maskedPart = '*'.repeat(maskLength);
  return maskedPart + value.slice(-4);
}
const relaceApi = (value) => {
  if(value) return  '*'.repeat(value.length);

}
defineExpose({
  setInfo,
});
</script>
<style lang="scss">
.speakerpaymentDetail::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}
.speakerpaymentDetail {
  font-size: 14px;
  color: #869199;
  // background: #fff;
  overflow: hidden;
  overflow-y: scroll;
  font-size: 14px;

  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  .bcfff {
    background: #fff;
    padding: 12px 16px;
    border-radius: 8px;
    margin: 12px;
  }
  .title {
    font-size: 20px;
    color: #1d212b;
  }
  .cusDetail-item {
    margin-top: 10px;
    .cusDetail-item-title {
      display: flex;
      align-items: center;
      padding: 6px 0;
      .label {
        width: 30%;
      }
      .value {
        color: #1d212b;
        width: 70%;
        display: block;
      }
    }
  }
}
</style>
