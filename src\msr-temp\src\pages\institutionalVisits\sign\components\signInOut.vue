<template>
  <view style="padding-bottom: 16px;">
    <!-- 签到 签退-->
    <view
      class="signIcon"
      :class="[props.signStatus === '1' ? 'normal-sign' : '']"
      @click="sign"
    >
      <view v-if="props.type === 'signIn'">签到</view>
      <view v-else>签退</view>
      <view class="statusTextDate">{{ state.t }}</view>
    </view>

    <view class="sign_address">
      <view style="font-size: 14px" v-if="props.signStatus === '0'">
        定位失败，请检查移动网络和GPS是否已打开
      </view>
      <view style="display: flex;align-items: center;justify-content: center;">
      <view
        style="color: #2551f2;margin-right: 10px;"
        @click="refreshLocation"
        v-if="props.signStatus === '1'"
        >刷新定位</view
      >
      <view
        style="color: red"
        v-if="(props.distance > 3000 || props.insLonNoFlag) && props.type === 'signIn'"
        @click="calibration"
        >机构定位校准</view
      >
    </view>
      <view style="color: #2551f2" v-if="!props.institutionName"
        >请选择客户</view
      >
      <view style="font-size: 14px" v-if="props.signStatus === '1'">
        <view
          style="
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
          "
        >
          <view v-if="props.distance < 3000 && !props.insLonNoFlag">
            <img :src="normal" alt="" class="distanceIcon" />已在正确范围内:
            {{ props.institutionName }}
          </view>
          <view v-if="props.distance > 3000 || props.insLonNoFlag">
            <img :src="warning" alt="" class="distanceIcon" />不在正确范围内:
            {{ props.institutionName }}
          </view>
        </view>
        <view style="color: #869199">当前地址:{{ props.signAddress }}</view>
        <view style="color: #f32f29" v-if="props.distance > 3000"
          >距离相差{{ (props.distance / 1000).toFixed(2) }}km</view
        >
        <view style="color: #f32f29" v-if="props.insLonNoFlag"
          >该机构没有经纬度，不能计算距离</view
        >
      </view>
    </view>
  </view>
</template>
<script setup>
import { onMounted, reactive, onUnmounted } from "vue";
import Taro, { useRouter, usePullDownRefresh, useDidShow } from "@tarojs/taro";
import { currentTime } from "../../../../utils/content.js";
import normal from "../../../../images/checked.png";
import warning from "../../../../images/warning.png";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { IconFont, } from "@nutui/icons-vue-taro";
import calibrationIcon from '../../../../images/calibration.png'
const state = reactive({
  t: "",
  intervalId: null,
  dateTime: "",
});
const props = defineProps({
  type: {
    type: String,
    default: "",
  },
  signStatus: {
    type: String,
    default: "",
  },
  signAddress: {
    type: String,
    default: "",
  },
  distance: {
    type: Number,
    default: "",
  },
  institutionName: {
    type: String,
    default: "",
  },
  insLonNoFlag: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["sign", "refresh-location",'calibration']);

const updateTime = () => {
  const dateInstance = dayjs(state.dateTime);
  const incrementedDate = dateInstance.add(1, "second");
  state.t = incrementedDate.format("HH:mm:ss");
  state.dateTime = dayjs(incrementedDate).format("YYYY-MM-DD HH:mm:ss");
};

const getCurrentTime = () => {
  const time = currentTime();
  state.dateTime = time;
  state.t = dayjs(time).format("HH:mm:ss");
  state.intervalId = setInterval(() => {
    updateTime();
  }, 1000);
};

const refreshLocation = () => {
  emit("refresh-location");
};
let timer = null;
const sign = () => {
  if (props.signStatus !== "1") return;
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
  timer = setTimeout(() => {
    emit("sign");
  }, 300);
};
const calibration = () => {
emit('calibration')
}
onUnmounted(() => {
  clearInterval(state.intervalId); // 页面卸载时清理定时器
});
onMounted(() => {
  getCurrentTime();
});
</script>
<style lang="scss">
.signIcon {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  margin: 16px auto 0;
  width: 142.33px;
  height: 142.33px;
  border-radius: 50%;
  font-size: 24px;
  font-weight: 500;
  font-family: Songti SC, Songti SC-Black;
  color: #fff;
  background: #cccccc;
  .statusTextDate {
    font-size: 16px;
    font-weight: 400;
    line-height: normal;
    margin-top: 10px;
  }
}
.sign_address {
  margin-top: 10px;
  text-align: center;
}
.distanceIcon {
  width: 18px;
  height: 18px;
  vertical-align: middle;
}

.normal-sign {
  background: var(
    --brand-color,
    linear-gradient(144deg, #597dff 2.72%, #2551f2 64.55%)
  );
}
</style>
