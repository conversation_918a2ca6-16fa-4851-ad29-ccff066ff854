<template>
  <view class="sign-time">
    <view style="display: flex; align-items: center">
      <view v-if="initUserInfo.avatar"
        ><img :src="avatar" style="width: 48px; height: 48px"
      /></view>
      <view class="nameAvatar" v-else>{{ props.visitUserName.slice(-2) }}</view>

      <view class="sign-name">
        <view class="name">{{ props.visitUserName }}</view>
        <view class="time" v-if="props.signType === 'signIn'">{{
          state.dateTime
        }}</view>
        <view
          class="time"
          v-if="
            props.status === '3' ||
            props.status === '4' ||
            props.signType === 'assist'
          "
          >{{ dayjs(props.visitTime).format("YYYY-MM-DD") }}</view
        >
      </view>
    </view>
    <view style="display: flex">
      <view
        style="color: #ffb42f; margin-right: 10px; align-self: end"
        v-if="props.duration"
        >停留{{ props.duration }}min</view
      >
      <view
        v-else
        style="color: #ffb42f; margin-right: 10px; align-self: end"
        v-show="!!diff"
      >
        停留{{diff}}min
      </view>
      <view style="font-size: 14px">
        <view v-if="!props.signInTime" style="margin-bottom: 4px">
          <text style="font-weight: 500">签到</text>
          <text style="color: #869199; margin-left: 4px">未签到</text>
        </view>
        <view v-else style="margin-bottom: 4px">
          <text>{{ dayjs(props.signInTime).format("HH:mm") }}</text>
          <text style="color: #869199; margin-left: 4px">已签到</text>
        </view>
        <view v-if="!props.signOutTime">
          <text style="font-weight: 500">签退</text>
          <text style="color: #869199; margin-left: 4px">未签退</text>
        </view>
        <view v-else>
          <text>{{ dayjs(props.signOutTime).format("HH:mm") }}</text>

          <text
            style="color: #f32f29; margin-left: 4px"
            v-if="props.sysSignOutFlag"
            >自动签退</text
          >
          <text style="color: #869199; margin-left: 4px" v-else>已签退</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import avatar from "../../../../images/avatar.png";
import Taro, { useDidShow } from "@tarojs/taro";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import {onMounted, onUnmounted, reactive,ref} from "vue";

const timer = ref(null);
const diff = ref('');

const props = defineProps({
  signType: {
    type: String,
    default: "",
  },
  year: {
    type: String,
    default: "",
  },
  time: {
    type: String,
    default: "",
  },
  signInTime: {
    type: String,
    default: "",
  },

  signOutTime: {
    type: String,
    default: "",
  },
  duration: {
    type: String,
    default: "",
  },
  visitUserName: {
    type: String,
    default: "",
  },
  sysSignOutFlag: {
    type: Boolean,
    default: false,
  },
  status: {
    type: String,
    default: "",
  },
  visitTime: {
    type: String,
    default: "",
  },
});
const state = reactive({
  dateTime: dayjs().format("YYYY-MM-DD"),
});

const initUserInfo = Taro.getStorageSync("initUserInfo").user;

const stopTime = () => {
  diff.value = dayjs(new Date()).diff(dayjs(props.signInTime), 'minute');
}
onMounted(() => {
  setTimeout(() => {
    stopTime();
    timer.value = setInterval(stopTime, 1000 * 60)
  }, 1000 * 3)
})

onUnmounted(()=> {
  clearInterval(timer.value)
})
</script>
<style lang="scss">
.sign-time {
  position: relative;
  margin: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 10px 16px;
  border-radius: 8px;
  margin-top: 0;
  .nameAvatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #92a8f8;
    color: #fff;
    line-height: 50px;
    text-align: center;
  }
  img {
    width: 48px;
    height: 48px;
    vertical-align: middle;
  }
  .sign-name {
    margin-left: 8px;
    font-size: 18px;
    .name {
      font-weight: 500;
    }
  }
  .time {
    color: #869199;
    font-size: 12px;
  }
}
</style>
