<template>
  <view class="planDetail-page">
    <!-- <navBar @click-back="clickBack" :leftText="state.leftTetx" /> -->

    <DetailTop
      :status="state.info.status"
      :visitTime="state.info.visitTime"
      :completeTime="state.info.completeTime"
      :planFlagTime="state.info.planFlag"
    />
    <view style="padding: 0px 8px">
      <SignTime
        :signInTime="state.info.signInTime"
        :signOutTime="state.info.signOutTime"
        :duration="state.info.duration"
        :visitUserName="state.info.visitUserName"
        :sysSignOutFlag="state.info.sysSignOutFlag"
        :status="state.info.status"
      />
      <view class="visit-address">
        <view
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
        >
          <view style="display: flex; align-items: center">
            <view>
              <text style="font-size: 20px; font-weight: 500">{{
                state.info.customerName
              }}</text>
              <img
                v-if="state.info.sex === '1' || state.info.sex === '0'"
                :src="
                  state.info.sex === '1'
                    ? vector
                    : state.info.sex === '0'
                    ? man
                    : ''
                "
                alt=""
                class="sexIcon"
              />
            </view>
            <view class="tag-s" v-if="state.info.planFlag === '1'"
              >计划拜访
            </view>
            <view class="tag-f" v-if="state.info.planFlag === '0'"
              >临时拜访
            </view>
            <!-- <nut-tag
              color="#E8F0FF"
              text-color="#2551F2"
              v-if="state.info.planFlag === '1'"
              style="vertical-align: text-bottom"
              >计划拜访
            </nut-tag>
            <nut-tag
              color="#FFF3E8"
              text-color="#F77234"
              v-if="state.info.planFlag === '0'"
              style="vertical-align: text-bottom"
              >临时拜访
            </nut-tag> -->
          </view>
          <view
            style="min-width: 110px; text-align: right"
            v-if="state.info.assistRemark === '1'"
          >
            <nut-tag type="primary" color="#E6F8F2" text-color="#00B578" plain>
              协访已评价
            </nut-tag>
          </view>
        </view>

        <view style="font-size: 14px; color: #869199; margin-top: 10px">
          <view class="m_b">
            <img :src="cus_ins" alt="" class="address" />
            <text>{{ state.info.institutionName }}</text>
            <nut-divider direction="vertical" />
            <text>{{ state.info.insDeptName }}</text>
            <nut-divider direction="vertical" />
            <text>{{
              state.info.visitTypeCode === "0" ? "院内拜访" : "院外拜访"
            }}</text>
          </view>
          <view class="m_b">
            <img :src="adress" alt="" class="address" />{{
              state.info.insAddress
            }}
          </view>
        </view>
      </view>

      <!-- 拜访信息 -->
      <view style="margin-top: 8px" class="visit-info">
        <view class="info-title">
          <view
            @click="clickVisit"
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <view>拜访信息</view>
            <view>
              <img
                v-if="state.info.visitPurposeCode"
                :src="!state.infoIconFlag ? arrowDown : arrowUp"
                alt=""
                style="width: 13px; height: 13px; padding-left: 10px"
              />
            </view>
          </view>
          <view
            v-if="state.info.status == '2' && query.activeIndex != 1"
            style="display: flex"
            class="buttonIcons"
          >
            <nut-button
              color="#E8F0FF"
              size="mini"
              @click="addInfo('add')"
              v-if="!state.info.visitPurposeCode"
              style="z-index: 10; font-size: 14px"
              >去填写
            </nut-button>
            <nut-button
              color="#E8F0FF"
              size="mini"
              @click="addInfo('edit')"
              v-else
              style="z-index: 10; font-size: 14px"
              >修改
            </nut-button>
          </view>
        </view>

        <view v-show="state.infoIconFlag">
          <nut-divider dashed :style="{ color: '#E5E6EB', marginBottom: 0 }" />
          <view style="padding: 0 16px">
            <nut-cell title="拜访目的">
              <template #desc>
                <span style="word-break: break-all">
                  {{ visitPurposeName(state.info.visitPurposeCode) }}</span
                >
              </template>
            </nut-cell>
            <nut-cell title="上次沟通内容">
              <template #desc>
                <span style="word-break: break-all">
                  {{ state.info.lastCommunicateContent }}</span
                >
              </template>
            </nut-cell>
            <nut-cell title="本次沟通内容">
              <template #desc>
                <span style="word-break: break-all">
                  {{ state.info.communicateContent }}</span
                >
              </template>
            </nut-cell>
            <nut-cell title="协访人" class="assistUserVOs">
              <template #desc>
                <view
                  v-for="item in state.info.assistUserVOs"
                  :key="item.id"
                  style="margin-right: 5px; margin-bottom: 10px"
                >
                  <view style="margin-bottom: 4px"
                    >{{ item.empName
                    }}<text
                      style="
                        background: #e6f8f2;
                        color: #00b578;
                        padding: 3px 5px;
                        border-radius: 2px;
                        font-size: 12px;
                      "
                      v-if="item.confirmInviteFlag === '1'"
                      >已接受</text
                    >
                    <text
                      style="
                        background: #feeaea;
                        color: #f32f29;
                        padding: 3px 5px;
                        border-radius: 2px;
                        font-size: 12px;
                      "
                      v-if="item.confirmInviteFlag === '0'"
                      >已拒绝</text
                    ></view
                  >
                </view>
              </template>
            </nut-cell>
            <nut-cell
              title="协访需求"
              :desc="assistPurposeName(state.info.assistRequirement)"
              style="border-bottom: none"
            >
            </nut-cell>
            <ImagePreview ref="imagePreviewRef"/>
          </view>
        </view>
      </view>
      <!-- 沟通产品 -->
      <view style="margin-top: 8px" class="pro-info">
        <view class="info-title" @click="clickPro">
          <view>沟通产品</view>

          <view
            v-if="state.info.visitPurposeCode"
            style="display: flex; align-items: center"
          >
            <view class="pro-name">{{ getProName() }}</view>
            <img
              :src="!state.proFlag ? arrowDown : arrowUp"
              alt=""
              style="width: 13px; height: 13px; padding-left: 10px"
            />
          </view>
        </view>
        <view v-if="state.proFlag">
          <nut-divider dashed :style="{ color: '#E5E6EB', marginBottom: 0 }" />
          <view style="padding: 0 12px">
            <view
              class="pro-content"
              v-for="item in state.info.productForms"
              :key="item.productCode"
            >
              <view class="pro-title">
                <view style="color: #2551f2; font-weight: 500">{{
                  item.productName
                }}</view>
              </view>
              <nut-divider :style="{ color: '#EEEEEE', margin: '0' }" />
              <view
                style="
                  padding: 12px;
                  padding-bottom: 0;
                  color: #666666;
                  font-size: 14px;
                "
                v-for="(j, index) in item.productInfoList"
                :key="j.infoId"
              >
                <view> {{ j.info }}</view>
                <view
                  style="color: #f32f29; font-size: 14px; text-align: right"
                  v-if="j.cusProdRespCode === '0'"
                >
                  <img
                    :src="proN"
                    style="width: 18px; height: 18px; vertical-align: bottom"
                  />
                  不认可
                </view>
                <view
                  style="color: #00b578; font-size: 14px; text-align: right"
                  v-if="j.cusProdRespCode === '1'"
                >
                  <img
                    :src="proY"
                    style="width: 18px; height: 18px; vertical-align: bottom"
                  />
                  认可
                </view>
                <view
                  style="color: #ffb42f; font-size: 14px; text-align: right"
                  v-if="j.cusProdRespCode === '2'"
                >
                  <img
                    :src="proZ"
                    style="width: 18px; height: 18px; vertical-align: bottom"
                  />
                  中立
                </view>
                <nut-divider
                  v-if="index !== item.productInfoList.length - 1"
                  dashed
                  :style="{ color: '#E5E6EB', margin: '5px 0 0 0' }"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 协访评价无数据 -->
      <view
        class="evaluate-no"
        v-if="
          state.info.assistRemark != '1' &&
          state.info.status == '1' &&
          !state.info.assistScoreVos.length
        "
      >
        <view class="evaluate-title">协访评价</view>
        <nut-divider :style="{ color: '#E5E6EB', margin: 0 }" />
        <view class="evaluate-title evaluate-content">
          <view>协访评价</view>
          <view style="color: #1d212b">暂无</view>
        </view>
        <nut-divider :style="{ color: '#E5E6EB', margin: 0 }" />
        <view class="evaluate-title evaluate-content">
          <view>协访建议</view>
          <view style="color: #1d212b"> 暂无</view>
        </view>
      </view>
      <!-- 协访评价有数据 -->
      <view
        class="evaluate"
        v-if="
          state.info.assistRemark === '1' &&
          state.info.status == '1' &&
          state.info.assistScoreVos.length
        "
      >
        <view class="evaluate-title">
          <view>协访评价</view>
          <view style="font-size: 14px; color: #869199"
            ><text style="color: #00b578">{{
              assistName(state.info.assistScoreVos)
            }}</text
            >对您做出评价</view
          >
        </view>
        <nut-divider dashed :style="{ color: '#E5E6EB', margin: 0 }" />
        <view
          v-for="(item, index) in state.info.assistScoreVos"
          :key="item.index"
          :style="{ borderTop: index !== 0 ? '1px solid #E5E6EB' : '' }"
        >
          <view class="evaluate-name">
            <view
              style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 0;
              "
            >
              <view>
                {{ item.assistUser }}
                <nut-tag type="primary" color="#E6F8F2" text-color="#00B578">
                  协访人
                </nut-tag>
              </view>
              <view>
                <img
                  :src="!item.assistIcon ? arrowDown : arrowUp"
                  alt=""
                  style="width: 13px; height: 13px; padding-left: 10px"
                  @click="assistClick(item.assistUser)"
              /></view>
            </view>
          </view>
          <view class="evaluate-content" v-if="item.assistIcon">
            <view class="evaluate-title">
              <view style="color: #869199">协访评分</view>
              <view>综合评价{{ item.total }}</view>
            </view>
            <view
              class="evaluate-scoring"
              v-for="j in item.scores"
              :key="j.scoreItemCode"
              style="padding-left: 4px"
            >
              <div class="rate">
                <div class="rate-content">
                  <div class="rate-title">
                    {{ j.scoreItemName }}
                  </div>
                  <nut-rate v-model="j.score" active-color="#FFB637" readonly />
                </div>
              </div>
            </view>
            <nut-divider :style="{ color: '#F4F5F7', margin: 0 }" />
            <view
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 0;
              "
            >
              <view style="color: #869199; min-width: 80px">协访建议</view>
              <view>{{ item.assistSuggestInfo }}</view>
            </view>
          </view>
        </view>
      </view>

      <SignInOut
        type="signOut"
        :signStatus="state.signOutStatus"
        @refresh-location="refreshLocation"
        @sign="signOut"
        :signAddress="state.params.signOutAddress"
        :distance="state.params.signOutDistance"
        :institutionName="state.info.institutionName"
        v-if="state.info.status === '2' && query.activeIndex != 1"
        :insLonNoFlag="state.insLonNoFlag"
      />
      <view
        style="text-align: center; color: #f32f29; font-size: 14px"
        v-if="
          state.info.status === '2' &&
          query.activeIndex != 1 &&
          !state.info.visitPurposeCode
        "
        >请填写拜访信息</view
      >
    </view>
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { reactive, onMounted,ref } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import SignTime from "./sign/components/signTime";
import vector from "../../images/woman.png";
import man from "../../images/man.png";
import cus_ins from "../../images/hospital_v3.png";
import adress from "../../images/location.png";
import arrowDown from "../../images/arrow-down.png";
import arrowUp from "../../images/up.png";
import SignInOut from "./sign/components/signInOut";
import {
  institutionalVisitsDetail,
  visitDistance,
  sign,
  crmSignOutTime,
  getCurrentTime,
} from "../../api/institutionalVisitsApi.js";
import { Plus } from "@nutui/icons-vue-taro";
import {
  dictDataValueList,
  visitPurposeName,
  assistPurposeName,
} from "../../utils/content.js";
import proN from "../../images/pro-1.png";
import proY from "../../images/pro-2.png";
import proZ from "../../images/pro-3.png";
import NavBar from "../../pages/components/navBar/index.vue";
import DetailTop from "./components/detailTop";
import { currentTime } from "../../utils/content";
import ImagePreview  from "./components/imagePreview.vue"
const router = useRouter();
const query = router.params;
const imagePreviewRef = ref(null)
const state = reactive({
  visitInstitutionId: "",
  info: {},
  signOutStatus: "",
  infoIconFlag: false,
  proFlag: false,
  leftTetx: "",
  scoresList: [], //评分的数组
  //评分
  //签退参数
  params: {
    signType: "1",
    signOutLat: "",
    signOutLon: "",
    signOutDistance: 0,
    signOutAddress: "",
  },
  insLonNoFlag: false,
  crmSignOutTimeInfo: {},
});

const clickBack = () => {
  if (query.source) {
    Taro.reLaunch({
      url: "/pages/institutionalVisits/index",
    });
  } else {
    Taro.navigateBack({
      delta: 1,
    });
  }
};
const getDetail = async () => {
  const params = {
    visitInstitutionId: state.visitInstitutionId,
  };
  const res = await institutionalVisitsDetail(params);
  state.info = res?.data;
  imagePreviewRef.value.getImgList(res?.data.signInPhotoList)
  if (state.info.status === "2") {
    state.leftTetx = "拜访签退";
  }

  if (state.info.status === "1") {
    state.leftTetx = "拜访详情";
  }
  if (
    state.info.status === "2" &&
    query.activeIndex != 1 &&
    state.info.visitPurposeCode
  ) {
    getLocation();
  }
  console.log("详情", state.info);
};

const clickVisit = () => {
  if (!state.info.visitPurposeCode) return;
  state.infoIconFlag = !state.infoIconFlag;
};
const clickPro = () => {
  if (!state.info.visitPurposeCode) return;
  state.proFlag = !state.proFlag;
};

const assistClick = (name) => {
  state.info.assistScoreVos.forEach((item) => {
    if (item.assistUser === name) {
      item.assistIcon = !item.assistIcon;
    }
  });
  state.assistIcon = !state.assistIcon;
};
const assistName = (arr) => {
  return arr.map((item) => item.assistUser).join(",");
};

const getProName = () => {
  if (state.info.productForms && state.info.productForms.length) {
    return state.info.productForms.map((item) => item.productName).join(",");
  }
};
const addInfo = (type) => {
  if (type === "add") {
    const info = {
      visitInstitutionId: state.info.id,
      lastCommunicateContent: state.info.lastCommunicateContent,
      insCode: state.info.institutionCode,
      communicateContent: state.info.planVisitContent,
    };

    Taro.navigateTo({
      url: `/pages/institutionalVisits/temporary?type=add&visitInstitutionId=${
        state.info.id
      }&info=${JSON.stringify(info)}`,
    });
  } else {
    Taro.navigateTo({
      url: `/pages/institutionalVisits/temporary?type=edit&visitInstitutionId=${state.info.id}`,
    });
  }
};

const refreshLocation = () => {
  getLocation("refre");
};

const getDistance = async (type) => {
  if (state.info.insLon && state.info.insLon != "0.0") {
    let ins = "";
    if (state.info.insLon) {
      ins = `${state.info.insLon},${state.info.insLat}`;
    } else {
      ins = "";
    }
    const params = {
      origins: `${state.params.signOutLon},${state.params.signOutLat}`,
      destination: ins,
    };
    const res = await visitDistance(params);

    if (res.code === 200) {
      if (res.data[0]) {
        state.params.signOutDistance = Number(res.data[0]);
      } else {
        state.params.signOutDistance = 0;
      }
      state.signOutStatus = "1";
      if (type) {
        Taro.showToast({
          title: "刷新成功",
          icon: "none",
          duration: 2000,
        });
      }
    } else {
      state.signOutStatus = "1";
      Taro.showToast({
        title: "获取机构位置与当前位置距离失败",
        icon: "none",
        duration: 2000,
      });
    }
  } else {
    state.signOutStatus = "1";
    state.params.signOutDistance = 0;
    state.insLonNoFlag = true;
  }
};
const getConvert = (longitude, latitude) =>
  new Promise((res) => {
    console.log("转换前的经纬度", longitude, latitude);
    Taro.request({
      url: `https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${longitude},${latitude}&key=0446b950e065d1e0b98a9e97f08dfbff&coordsys=gps`,
      success: (result_before) => {
        console.log(result_before, "-!result_before");
        console.log("转换后的经度", result_before.data.locations);
        const [lon, lat] = result_before.data.locations.split(",");
        res({ lon, lat });
        console.log("转换后的经度params", lon, lat);
      },
    });
  });
const getLocation = (type) => {
  if ("geolocation" in navigator) {
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const latitude = position.coords.latitude; // 纬度
        const longitude = position.coords.longitude; // 经度
        const { lon, lat } = await getConvert(longitude, latitude);

        Taro.request({
          url: `https://restapi.amap.com/v3/geocode/regeo?location=${lon},${lat}&key=0446b950e065d1e0b98a9e97f08dfbff`,
          success: (result) => {
            state.params.signOutAddress =
              result.data.regeocode.formatted_address;
            state.params.signOutLat = latitude;
            state.params.signOutLon = longitude;
            getDistance(type);
            // const mapCtx = Taro.createMapContext("map-container");
            // mapCtx.moveToLocation();
          },
          fail: function (res) {
            Taro.showToast({
              title: "定位失败",
              icon: "none",
              duration: 2000,
            });
            state.signOutStatus = "0";
          },
        });
      },
      function (error) {
        Taro.showToast({
          title: "定位失败",
          icon: "none",
          duration: 2000,
        });
        state.signOutStatus = "0";
      },
      {
        // enableHighAccuracy: true,
        // timeout: 1000,
        // maximumAge: 0,
      }
    );
  } else {
    Taro.showToast({
      title: "请开启定位权限",
      icon: "none",
      duration: 2000,
    });
  }
  // try {
  //   Taro.getLocation({
  //     type: "gcj02",
  //     success: function (res) {
  //       console.log("taro定位返回的res", res);
  //       Taro.request({
  //         url: `https://restapi.amap.com/v3/geocode/regeo?location=${res.longitude},${res.latitude}&key=0446b950e065d1e0b98a9e97f08dfbff`,
  //         success: (result) => {
  //           console.log("逆向解析的地址", result);
  //           state.params.signOutAddress =
  //             result.data.regeocode.formatted_address;
  //           state.params.signOutLat = res.latitude;
  //           state.params.signOutLon = res.longitude;
  //           getDistance(type);
  //           const mapCtx = Taro.createMapContext("map-container");
  //           mapCtx.moveToLocation();
  //         },
  //         fail: function (error) {
  //           console.log("逆向解析失败", error);
  //         },
  //       });
  //     },
  //     fail: function (res) {
  //       Taro.showToast({
  //         title: "定位失败",
  //         icon: "none",
  //         duration: 2000,
  //       });
  //       state.signOutStatus = "0";
  //     },
  //   });
  // } catch (err) {
  //   console.log("定位失败", err);
  // }
};

const signOut = async () => {
  if (!state.info.visitPurposeCode) {
    Taro.showToast({
      title: "请添加拜访信息后再签退",
      icon: "none",
    });
    return;
  }
  Taro.showLoading({
    mask: true,
    title: "加载中",
  })
  const { data } = await getCurrentTime();
  const currentTime_2 = new Date(data);
  const signInTime = new Date(state.info.signInTime);
  const timeDifference = currentTime_2.getTime() - signInTime.getTime();
  const differenceInMinutes = Math.floor(timeDifference / (1000 * 60));
  if (
    state.crmSignOutTimeInfo.remark === "1" &&
    differenceInMinutes < Number(state.crmSignOutTimeInfo.configValue )
  ) {
    Taro.hideLoading();
    return Taro.showModal({
      content: state.crmSignOutTimeInfo.configName,
      confirmText: "确定",
      duration: 2000,
      showCancel: false,
    });
  }
  const infoParams = {
    visitInstitutionId: state.visitInstitutionId,
    customerCode: state.info.customerCode,
    customerName: state.info.customerName,
    institutionCode: state.info.institutionCode,
    institutionName: state.info.institutionName,
    insLat: state.info.insLat,
    insLon: state.info.insLon,
    isPlan: state.info.planFlag,
    id: state.info.visitSignId,
  };
  const params = {
    ...state.params,
    ...infoParams,
  };
  const res = await sign(params);
  if (res.code === 200) {
    Taro.hideLoading();
    Taro.showToast({
      title: "签退成功",
      icon: "none",
      duration: 2000,
    });
    Taro.hideLoading();
    getDetail();
  } else {
    Taro.hideLoading();
    // Taro.showToast({
    //   title: res.msg,
    //   icon: "none",
    // });
  }
};
let timer = null;

const getCrmSignOutTime = async () => {
  const res = await crmSignOutTime();
  state.crmSignOutTimeInfo = res.rows[0] || {};
};

useDidShow(() => {
  getCrmSignOutTime();
  state.visitInstitutionId = query.id;
  // getLocation("", false);

  getDetail();
});
</script>
<style lang="scss">
.tag-f {
  margin-left: 4px;
  color: #f77234;
  background: #fff3e8;
  min-width: 48px;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
  height: 20px;
  line-height: 20px;
}
.tag-s {
  margin-left: 4px;
  color: #2551f2;
  background: #e8f0ff;
  min-width: 48px;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
  height: 20px;
  line-height: 20px;
}
.planDetail-page {
  font-size: 16px;
  // padding-top: 94px;
  padding-bottom: 50px;
  .sign-time {
    margin: 0;
  }

  .visit-address {
    margin-top: 8px;
    padding: 12px 16px 12px 16px;
    background: #fff;
    border-radius: 8px;
    .sexIcon {
      display: inline-block;
      margin-right: 5px;
      width: 18px;
      height: 18px;
      vertical-align: text-top;
    }
    .address {
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: text-bottom;
      margin-right: 4px;
    }
    .m_b {
      // margin-bottom: 8px;
    }
  }
  .visit-info {
    padding: 16px 0;
    border-radius: 8px;
    background: #fff;
    .info-title {
      position: relative;
      padding: 0 16px;
    }
    .buttonIcons {
      position: absolute;
      top: -6px;
      left: 75%;
    }
    .nut-button__wrap {
      color: #2551f2;
    }
    .assistUserVOs {
      .nut-cell__title {
      }
    }
    .nut-button {
      height: 32px;
      line-height: 32px;
      font-size: 16px;
    }
  }
  .pro-info {
    padding: 16px 0;
    border-radius: 8px;
    background: #fff;
    .info-title {
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .pro-content {
      border-radius: 4px;
      background: #f8fafe;
      padding-bottom: 12px;
      margin-top: 5px;
    }
    .pro-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
    }
    .pro-name {
      width: 200px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-align: end;
    }
  }
  .evaluate-no {
    margin-top: 8px;
    background: #fff;
    padding: 0 16px;
    border-radius: 8px;

    .evaluate-title {
      height: 54px;
      line-height: 54px;
    }
    .evaluate-content {
      color: #869199;
      display: flex;
      justify-content: space-between;
    }
  }
  .evaluate {
    border-radius: 8px;
    margin-top: 8px;
    background: #fff;
    .evaluate-title {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .evaluate-name {
      padding: 0 16px;
      .nut-tag {
        color: #00b578;
      }
    }
    .evaluate-content {
      padding: 0 16px;
      .evaluate-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #f4f5f7;
      }
      .rate {
        background-color: #fff;
        .rate-content {
          height: 40px;
          display: flex;
          align-items: center;
          .rate-title {
            color: #000;
            font-weight: 400;
            line-height: 22px;
            font-size: 14px;
          }

          .nut-rate {
            margin-left: 20px;
          }
        }
        .rate-score {
          text-align: right;
          margin-top: 16px;
          font-size: 14px;
        }
      }
    }
  }
  .nut-cell__title {
    max-width: 100px;
    color: #869199;
  }
}
</style>
