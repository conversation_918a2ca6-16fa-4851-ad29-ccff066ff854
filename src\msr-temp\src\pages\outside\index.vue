<template>
  <view class="outside">
    <nut-tabs
      v-model="state.params.bpmType"
      backgroud="#FFFFFF"
      @change="tabChange"
    >
      <nut-tab-pane title="我发起" pane-key="0"> </nut-tab-pane>
      <nut-tab-pane title="待办" pane-key="1"> </nut-tab-pane>
      <nut-tab-pane title="已办" pane-key="2"> </nut-tab-pane>
    </nut-tabs>
    <view class="outside-top-search">
      <view
        class="top-search-item"
        style="border: 1px solid"
        v-if="state.params.bpmType === '0'"
        >区域外时间{{ state.totalObj.actDayTotal }}</view
      >
      <view
        class="top-search-item"
        v-for="item in topFilterList"
        :key="item.key"
      >
        <view style="white-space: nowrap">
          {{ item }}
        </view>
      </view>
      <view class="top-search-right" @click="selecType">
        <img :src="screen" alt="" />
      </view>
    </view>

    <view class="approveList">
      <scroll-view
        style="height: 620px; position: relative"
        :scroll-y="true"
        :scroll-top="state.scrollTop"
        @scrolltolower="onScrollToLower"
      >
        <view
          style="
            position: absolute;
            top: 21%;
            left: 50%;
            transform: translate(-50%);
          "
          v-if="!state.approveList.length && !scrollMyFlag"
        >
          <img :src="noData" alt="" style="width: 180px; height: 180px" />
          <view style="text-align: center; color: #86909c; font-size: 14px"
            >暂无数据</view
          >
        </view>
        <view v-else>
          <view
            v-for="item in state.approveList"
            :key="item.id"
            class="content"
          >
            <view class="content-item">
              <view @click="gotoInst(item)">
                <view class="item-top">
                  <text style="color: #2551f2">{{ item.createByName }}</text>
                  <nut-divider direction="vertical" /><text
                    >{{ item.applyType === "valid_day_cancel" ? "销假 - " : ""
                    }}{{ item.typeValue }}</text
                  >
                </view>

                <view class="item-time">
                  <view style="margin: 4px 0"
                    >区域外时间: {{ item.startTime }}-{{ item.endTime }}</view
                  >
                  <view style="margin: 4px 0">天数: {{ item.askDay }}</view>
                  <view style="margin: 4px 0"
                    >申请时间: {{ item.createTime }}</view
                  >
                </view>

                <view
                  class="approvalStatus"
                  :style="{
                    color: statusColor(item),
                    background: statusBgcolor(item),
                  }"
                >
                  <text style="margin-left: 2px">{{ statusText(item) }}</text>
                </view>
                <!-- <view style="color: red;font-size: 12px;">审批意见{{ item.remark }}</view> -->
              </view>

              <nut-button
                type="info"
                color="#F3F4F5"
                style="
                  color: #4e595e;
                  position: absolute;
                  right: 16px;
                  bottom: 16px;
                "
                size="small"
                @click="approveCancle(item)"
                v-if="item.revokeFlag === '1'"
                >销假</nut-button
              >

              <view style="position: absolute; right: 0; bottom: -10px">
                <img
                  :src="destroyIcon"
                  alt=""
                  class="status-icon"
                  v-if="item.applyType === 'valid_day_cancel'"
                />
                <img
                  :src="disabledIcon"
                  alt=""
                  class="status-icon"
                  v-if="item.isCancel === '1'"
                />
                <img
                  :src="rejetIcon"
                  alt=""
                  class="status-icon"
                  v-if="item.status === '-1'"
                />
                <img
                  :src="cancleIcon"
                  alt=""
                  class="status-icon"
                  v-if="item.status === '-2'"
                />
              </view>

              <!-- <nut-button
                type="info"
                color="#F3F4F5"
                style="
                  color: #4e595e;
                  position: absolute;
                  right: 16px;
                  bottom: 16px;
                "
                size="small"
                v-if="item.isCancel === '1'"
                >已销假</nut-button
              > -->
            </view>
          </view>
        </view>

        <view
          v-if="
            state.approveList.length > 0 &&
            state.approveList.length == state.total
          "
          style="color: #869199; font-size: 16px; text-align: center"
          >已全部加载完毕</view
        >
      </scroll-view>
    </view>

    <view class="footerFiexd">
      <view class="drag" @click="dragClick">
        <img :src="plus" alt="" class="add" />
      </view>
    </view>
    <TopFilter
      ref="topFilterRef"
      @confirm="confirm"
      :bpmType="state.params.bpmType"
    />
    <GlobalDialog
      ref="globalDialogRef"
      title="请输入销假理由"
      subTetx="提交申请"
      @confirm="cancelButton"
    >
      <view class="num-input">
        <nut-input
          v-model="state.reason"
          max-length="200"
          placeholder="请输入"
        />
      </view>
    </GlobalDialog>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { ref, onMounted, reactive, computed } from "vue";
import plus from "../../images/plus.png";
import screen from "../../images/screen.png";
import TopFilter from "./components/topFilter";
import { validApplyList, validApply, validTotalApi } from "../../api/outside";
import GlobalDialog from "../../pages/components/globalDialog/index";
import noData from "../../images/no-data.png";
import cancleIcon from "../../images/cancleIcon.png";
import destroyIcon from "../../images/destroyIcon.png";
import disabledIcon from "../../images/disabledIcon.png";
import rejetIcon from "../../images/rejetIcon.png";
import { getOutsideTypeApi } from "../../utils/content.js";
const topFilterRef = ref(null);
const globalDialogRef = ref(null);
const scrollMyFlag = ref(false);
const applyFormTypeList = Taro.getStorageSync("applyFormTypeList");
const state = reactive({
  list: [],
  params: {
    applyType: "",
    bpmType: "0",
    typeId: "",
    userId: "",
    userNameList: [],
    typeName: [],
    status: "",
    statusName: [],
  },
  pageNum: 1,
  pageSize: 10,
  total: 0,
  reason: "",
  approveList: [],
  item: {},
  scrollTop: 0,
  statusType: applyFormTypeList,
  totalObj: {},
});
const topFilterList = computed(() => {
  if (state.params.applyType === "valid_day_cancel") {
    return [
      ...state.params.userNameList,
      ...state.params.statusName,
      ...state.params.typeName,
      "销假",
    ];
  } else {
    return [
      ...state.params.userNameList,
      ...state.params.statusName,
      ...state.params.typeName,
    ];
  }
});
const dragClick = () => {
  Taro.navigateTo({
    url: `/pages/outside/components/add`,
  });
};
const tabChange = (tab) => {
  if (tab.paneKey === "0") {
    state.params.userId = "";
    state.params.userNameList = [];
  }

  state.params.pageSize = 1;
  state.approveList = [];
  getList();
};
const getList = async () => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  scrollMyFlag.value = true;
  const params = {
    ...state.params,
    pageNum: state.pageNum,
    pageSize: state.pageSize,
  };
  const res = await validApplyList(params);
  if (res.code == 200) {
    Taro.hideLoading();
    state.total = res.total;
    if (!state.approveList.length) {
      state.approveList = res.rows;
    } else {
      state.approveList = [...state.approveList, ...res.rows];
    }
    scrollMyFlag.value = false;
  }
};
const selecType = () => {
  topFilterRef.value.open(state.params);
};

const confirm = (obj) => {
  state.params.applyType =
    !obj.applyType && obj.typeId
      ? "valid_day"
      : !obj.applyType
      ? ""
      : obj.applyType;
  state.params.typeId = obj.typeId;
  state.params.userId = obj.userId;
  state.params.userNameList = obj.userNameList;
  state.params.typeName = obj.typeName;
  state.params.status = obj.status;
  state.params.statusName = obj.statusName;
  state.params.pageSize = 1;
  state.approveList = [];
  getList();
};
const cancelButton = async () => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  const params = {
    applyType: "valid_day_cancel",
    reason: state.reason,
    bpmId: state.item.id,
  };
  const res = await validApply(params);

  Taro.hideLoading();
  globalDialogRef.value.cancel();
  Taro.showToast({
    title: "提交成功",
    icon: "none",
    duration: 2000,
  });
  state.params.pageSize = 1;
  state.approveList = [];
  getList();
};
const approveCancle = async (item) => {
  console.log("eeeee");
  state.item = item;
  globalDialogRef.value.open();
};
//滚动触底事件
const onScrollToLower = () => {
  if (!scrollMyFlag.value && state.approveList.length < state.total) {
    state.pageNum++;

    getList();
  }
};
const statusColor = (i) => {
  if (!i.status) return "";
  const arr = state.statusType.filter(
    (item) => item.dictValue === i.applyType
  )[0].remark;
  const statusList = JSON.parse(arr).data;
  const list = statusList.filter((item) => item.dictValue === i.status);
  return list.length ? list[0].color : "";
};

const statusBgcolor = (i) => {
  if (!i.status) return;
  const arr = state.statusType.filter(
    (item) => item.dictValue === i.applyType
  )[0].remark;
  const statusList = JSON.parse(arr).data;
  const list = statusList.filter((item) => item.dictValue === i.status);
  return list.length ? list[0].bgcColor : "";
};

const statusText = (i) => {
  if (!i.status) return "";
  const arr = state.statusType.filter(
    (item) => item.dictValue === i.applyType
  )[0].remark;
  const statusList = JSON.parse(arr).data;
  const list = statusList.filter((item) => item.dictValue === i.status);
  return list.length ? list[0].dictLabel : "";
};

const gotoInst = (item) => {
  Taro.navigateTo({
    url: `/bpm-temp/src/pages/approve_detail/index?instanceId=${item.aeFlowId}`,
  });
};

const getValidTotalApi = async () => {
  const res = await validTotalApi();
  state.totalObj = res.data;
};

const getIcon = (item) => {
  if (item.applyType === "valid_day_cancel") {
    return destroyIcon;
  } else if (item.isCancel === "1") {
    return disabledIcon;
  } else if (item.status === "-1") {
    return rejetIcon;
  } else if (item.status === "-2") {
    return cancleIcon;
  }
};
useDidShow(() => {
  Taro.setNavigationBarTitle({ title: "区域外时间" });
  state.pageNum = 1;
  state.approveList = [];
  getList();
  getValidTotalApi();
});
onMounted(() => {
  getOutsideTypeApi();
});
</script>

<style lang="scss" scoped>
.outside {
  .top-search::-webkit-scrollbar {
    /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
    display: none;
  }

  .outside-top-search {
    display: flex;
    padding: 0 16px;
    overflow: hidden;
    overflow-x: scroll;
    align-items: center;
    background: #edeff5;
    padding-right: 40px;
    height: 51px;

    .top-search-item {
      height: 27px;
      padding: 0px 9px;
      border-radius: 16px;
      background: #e3e4e9;
      font-size: 14px;
      line-height: 27px;
      margin-right: 8px;
      display: inline-block;
      white-space: nowrap;
      color: #4e595e;
      border: 1px solid #e3e4e9;
      color: #2551f2;
      background: var(--brand-color-1, #e8f0ff);
      border: 1px dashed #92a8f8;
    }

    .line {
      margin: 0 4px;
      color: #eeee;
    }

    .top-search-right {
      position: absolute;
      right: 0px;
      font-size: 14px;
      line-height: 27px;
      text-align: center;
      width: 40px;
      background: linear-gradient(
        90deg,
        rgba(237, 239, 245, 0) 0%,
        #edeff5 17%,
        #edeff5 100%
      );
      height: 30px;

      img {
        width: 20px;
        height: 20px;
        vertical-align: middle;
      }
    }

    .top-search-value {
      max-width: 148px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .rotate {
      color: #2551f2;
      transform: rotate(180deg);
    }
  }

  .approveList {
    padding: 0px 0px;
    width: 100vw;
    height: 630px;
    font-family: PingFang SC, PingFang SC;

    .content {
      background-color: #ffffff;
      margin-bottom: 10px;
      border-radius: 8px;
      margin-left: 12px;
      margin-right: 12px;
      position: relative;

      .content-item {
        position: relative;
        padding: 12px;

        .item-top {
          font-size: 18px;
        }
        .status-icon {
          width: 70px;
          height: 70px;
        }
      }

      .item-time {
        font-size: 13px;
        color: #869199;
      }

      .item-type {
        font-size: 14px;
        color: #869199;
      }
    }

    .approve {
      color: #ff7d00;
      background: #fff3e8;
    }

    .agree {
      color: #00b578;
      background: #e6f8f2;
    }

    .reject {
      color: #f32f29;
      background: #feeaea;
    }

    .back {
      color: #4e595e;
      background: #f3f4f5;
    }

    .approvalStatus {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 12px;
      text-align: center;
      height: 20px;
      padding: 0 7px;
      line-height: 20px;
      border-radius: 2px 8px 0 10px;
    }

    .nut-divider.nut-divider-vertical {
      top: 0;
    }

    .add-pharmacy {
      position: absolute;
      right: 16px;
      top: 53%;
    }
  }

  .footerFiexd {
    margin-left: 80%;
    position: fixed;
    bottom: 80px;
    right: 20px;

    .drag {
      width: 52px;
      height: 52px;
      border-radius: 50%;
      background-color: #597dff;
      box-shadow: 0px 4px 4px 0px rgba(23, 49, 229, 0.2);
      display: flex;
      justify-content: center;
      align-items: center;

      .add {
        display: inline-block;
        width: 24px;
        height: 24px;
      }
    }
  }

  .nut-tab-pane {
    padding: 0;
  }

  .num-input {
    font-size: 16px;
    text-align: center;
    margin-top: 8px;

    margin-bottom: 16px;
    display: flex;
    align-items: center;

    .nut-input {
      background: #f3f4f5;
      padding: 7px 0;
    }
  }

  .nut-tabs .nut-tabs__titles .nut-tabs__titles-item {
    color: #969799;
  }

  .nut-tabs .nut-tabs__titles .nut-tabs__titles-item.active {
    color: #1d212b !important;
  }
}
</style>
