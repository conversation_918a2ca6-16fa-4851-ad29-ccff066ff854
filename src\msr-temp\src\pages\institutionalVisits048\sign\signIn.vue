<template>
  <view :class="{ signIn: true, overflowStyle: overflowStyle }">
    <SignTime
      signType="signIn"
      :visitUserName="initUserInfo.nickName"
      status="0"
      @click-right="signIn"
    />
    <view class="sign-select">
      <CellClick
        @selectClick="selectCusClick"
        :value="state.params.customerName"
        :required="true"
        label="客户"
        placeholder="请选择"
        :phRight="true"
        :disabled="state.isEditMode || query.info"
      />
      <nut-tag
        plain
        color="#2551f2"
        text-color="#2551f2"
        style="
          vertical-align: text-top;
          position: relative;
          left: calc(100% - 50px);
        "
        v-if="state.params.insLabel"
      >
        {{ state.params.insLabel }}
      </nut-tag>
      <nut-cell
        title="医疗机构"
        class="sign"
        :desc="state.params.institutionName"
      ></nut-cell>
      <nut-cell
        title="机构地址"
        class="sign"
        :desc="state.params.address"
      ></nut-cell>
      <nut-cell
        title="科室"
        class="sign"
        :desc="state.params.insDeptName"
      ></nut-cell>
      <CellClick
        @selectClick="selectTypeClick"
        :value="state.params.visitTypeName"
        label="拜访类型"
        placeholder="请选择"
        :phRight="true"
        :disabled="state.isEditMode"
      />
      <view class="photograph">
        <view style="padding: 16px 0">拍照</view>
        <view class="photo-list">
          <view
            class="photo"
            v-for="(photo, index) in photoList"
            :key="photo.ossId"
          >
            <img
              :src="photo.url"
              alt=""
              style="width: 80px; height: 80px"
              @click="lookImage(index)"
            />
            <view class="del" @tap="delPhoto(index)">X</view>
          </view>
          <view @tap="takePhoto()" class="photo">
            <img :src="uploaderImg" alt="" style="width: 80px; height: 80px" />
          </view>
        </view>
      </view>
      <CellClick
        @selectClick="selectPopup('objective')"
        :value="state.visitPurposeName"
        :required="true"
        label="拜访目的"
        placeholder="请选择"
        :phRight="true"
      />
      <view class="sculpture">
        <view class="selectUser">
          <view class="selectUser-title">协访人员</view>

          <view class="deteleText">点击头像可删除</view>
        </view>
        <view class="headSculpture">
          <view
            v-for="(item, index) in state.params.assistForm"
            :key="item.empCode"
            style="margin-right: 5px; display: flex; flex-wrap: wrap"
          >
            <view style="text-align: center; margin-right: 5px">
              <view class="picture" @click="deleteAssist(item)">
                <img
                  :src="item.assistAvatar ? item.assistAvatar : avatar"
                  alt=""
                />
              </view>
              <view style="color: #4e595e; font-size: 12px">{{
                item.empName
              }}</view>
            </view>
          </view>
          <view @click="openAssist">
            <img :src="Group5" alt="" />
          </view>
        </view>
      </view>
      <CellClick
        @selectClick="selectPopup('assistObjective')"
        :value="state.assistRequireName"
        label="协访需求"
        placeholder="请选择"
        :phRight="true"
      />

      <!-- 调查问卷入口，协访需求下方，单独一行 -->
      <view style="margin: 16px 0; border-bottom: 0.96px solid #edf0f3">
        <view>
          <span
            class="survey-link-underline"
            @click="handleSurveyClick('0JBX', '金蓓欣')"
          >
            {{ surveyNameMap["0JBX"] || "观念问卷" }}
            <span class="arrow">&gt;</span>
          </span>
        </view>
      </view>

      <view class="customerstext">
        <view class="title cellClick">
          <view style="font-size: 16px" class="title-pro title-required"
            >产品
          </view>
          <view>
          </view>
        </view>
        <view
          v-for="item in state.params.productForms"
          :key="item.productCode"
          class="product"
        >
          <view class="productNames">
            <view>{{ item.productName }}</view>
          </view>

          <view class="productContent">
            <view
              v-for="j in item.productInfoList"
              :key="j.infoId"
              class="info"
            >
              <!-- 有诊疗体验字段 -->
              <view class="feedback">
                <view class="approve">是否有诊疗体验</view>
                <nut-radio-group
                  v-model="j.hasTherapeuticExperience"
                  direction="horizontal"
                >
                  <nut-radio label="是">是</nut-radio>
                  <nut-radio label="否">否</nut-radio>
                </nut-radio-group>
              </view>
              <view
                v-if="j.hasTherapeuticExperience === '是'"
                class="therapeutic-experience"
              >
                <!-- 诊疗类型字段 -->
                <view class="experience-item">
                  <view class="experience-label" style="min-width: 80px;">诊疗类型</view>
                  <nut-radio-group
                    v-model="j.treatmentType"
                  >
                    <nut-radio label="院内门诊">院内门诊</nut-radio>
                    <nut-radio label="院内病房">院内病房</nut-radio>
                    <nut-radio label="院外诊所">院外诊所</nut-radio>
                  </nut-radio-group>
                </view>
                <view class="experience-item">
                  <view class="experience-label">是否急性发作患者</view>
                  <nut-radio-group
                    v-model="j.remark['是否急性发作患者']"
                    direction="horizontal"
                  >
                    <nut-radio label="是">是</nut-radio>
                    <nut-radio label="否">否</nut-radio>
                  </nut-radio-group>
                </view>
                <view style="font-weight: bold; margin: 12px 0 4px 0"
                  >患者类型选择：</view
                >
                <view class="experience-item">
                  <view class="experience-label"
                    >难治性痛风患者(痛风石、或经治疗年发作次数≥2次)</view
                  >
                  <nut-radio-group
                    v-model="
                      j.remark['难治性痛风患者(痛风石、或经治疗年发作次数≥2次)']
                    "
                    direction="horizontal"
                  >
                    <nut-radio label="是">是</nut-radio>
                    <nut-radio label="否">否</nut-radio>
                  </nut-radio-group>
                </view>
                <view class="experience-item">
                  <view class="experience-label">痛风伴CKD患者</view>
                  <nut-radio-group
                    v-model="j.remark['痛风伴CKD患者']"
                    direction="horizontal"
                  >
                    <nut-radio label="是">是</nut-radio>
                    <nut-radio label="否">否</nut-radio>
                  </nut-radio-group>
                </view>
                <view class="experience-item">
                  <view class="experience-label">痛风合并心脑血管疾病</view>
                  <nut-radio-group
                    v-model="j.remark['痛风合并心脑血管疾病']"
                    direction="horizontal"
                  >
                    <nut-radio label="是">是</nut-radio>
                    <nut-radio label="否">否</nut-radio>
                  </nut-radio-group>
                </view>
                <view class="experience-item">
                  <view class="experience-label"
                    >对传统治疗（秋水仙碱、非甾体抗炎药、激素）不耐受的痛风患者</view
                  >
                  <nut-radio-group
                    v-model="
                      j.remark[
                        '对传统治疗（秋水仙碱、非甾体抗炎药、激素）不耐受的痛风患者'
                      ]
                    "
                    direction="horizontal"
                  >
                    <nut-radio label="是">是</nut-radio>
                    <nut-radio label="否">否</nut-radio>
                  </nut-radio-group>
                </view>
                <view class="experience-item">
                  <view class="experience-label">其他</view>
                  <nut-radio-group
                    v-model="j.remark['其他']"
                    direction="horizontal"
                  >
                    <nut-radio label="是">是</nut-radio>
                    <nut-radio label="否">否</nut-radio>
                  </nut-radio-group>

                </view>
                <!-- 选择“其他”为“是”时，展示备注多行文本框 -->
                <view v-if="j.remark['其他'] === '是'" style="margin-top: 12px;">
                  <nut-textarea
                    v-model="j.remark['其他备注']"
                    placeholder="请补充描述其他类型"
                    limit-show 
                    max-length="200"
                  />
                </view>
              </view>
              <nut-divider style="color: #edf0f3;" color="#edf0f3;" />
             <view class="experience-label" style="color: #666; font-size: 16px; padding-left: 14px; margin-bottom: 6px; font-weight: bold;">推广信息</view>
             <!-- 推广信息表单 -->
              <view
                v-for="promotionItem in state.promotionInfoList"
                :key="promotionItem.id"
                class="promotion-item"
              >
                <view class="promotion-content">
                  <view class="promotion-text">{{ promotionItem.text }}</view>
                  <view class="feedback">
                    <view class="approve">认可度</view>
                    <nut-radio-group
                      v-model="j.promotionInfo[promotionItem.id]"
                      direction="horizontal"
                    >
                      <nut-radio
                        :label="option.label"
                        v-for="option in state.optionsRadio"
                        :key="option.label"
                      >
                        {{ option.text }}
                        <template #checkedIcon>
                          <img :src="radioIcon3" alt="" class="radioIcon" />
                        </template>
                      </nut-radio>
                    </nut-radio-group>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 拜访信息 -->
  </view>
  <FooterButton
    :isLoading="state.isLoading"
    :text="state.isEditMode ? '保存' : '保存'"
    @click-button="signIn"
  />
  <CusPopup ref="cusPopupRef" @select-cus="selectCus" />
  <VisitTypePopup
    @visit-type="visitTypeSelect"
    ref="visitTypePopupRef"
  ></VisitTypePopup>
  <nut-image-preview
    :show="state.showPreview"
    :images="state.imgData"
    :init-no="initNo"
    @close="state.showPreview = false"
    style="width: 100%; height: 100%"
  />
  <CalibrationDialog
    ref="calibrationDialogRef"
    @selectLocation="selectLocation"
  />
  <PopupChexked ref="temporaryPopupRef" @check-confirm="checkConfirm" />
  <SelectAssistPopup ref="assistPopupRef" @select-assist="selectAssist" />
</template>

<script setup>
import { onMounted, reactive, ref, onUnmounted } from "vue";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import SignTime from "./components/signTime";
import CellClick from "../../../pages/components/cellClick/index.vue";
import CusPopup from "../components/cusPopup";
import VisitTypePopup from "../components/visitTypePopup";
import PopupChexked from "@/msr-temp/src/pages/components/popupChexked/popupChexked";
import SelectAssistPopup from "../components/selectAssistPopup.vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import {
  validIsNotSignOut,
  visitDistance,
  sign,
  crmSinInM,
  addVisiInfo,
  assistMessage,
  filtering,
  imageUrl,
  institutionProducts,
  getProductInfoConfig,
} from "../../../api/institutionalVisitsApi";
import { appCode, assistUrl } from "../../../utils/content.js";

import deleteThemes from "../../../images/Delete-themes.png";
import addicon from "../../../images/addicon.png";
import radioIcon3 from "../../../images/radio-icon3.png";
import avatar from "../../../images/avatar.png";
import Group5 from "../../../images/Group5.png";

import "dayjs/locale/zh-cn";
import { apiAuth } from "../../../utils/feishuAuth";

import uploaderImg from "../../../images/uploader.png";
import { baseApi } from "../../../utils/content";
import CalibrationDialog from "../../components/calibrationDialog.vue";
import { surveyListApi } from "@/msr-temp/src/api/customerAPI.js";
import {
  addCustomerVisit,
  getVisitDetail,
  updateCustomerVisit,
} from "./../api.js";

const initNo = ref(0);
const TokenKey = Taro.getStorageSync("access_token");
const router = useRouter();
const query = router.params;
const cusPopupRef = ref(null);
const visitTypePopupRef = ref(null);
const temporaryPopupRef = ref(null);
const assistPopupRef = ref(null);
const initUserInfo = Taro.getStorageSync("initUserInfo").user;
const photoList = ref([]);
const calibrationDialogRef = ref(null);
const overflowStyle = ref(true);
const feishuNotice = ref({
  appLink: `${assistUrl}`,
  pcLink: `${assistUrl}`,
});
const selectObjectiveList = Taro.getStorageSync("selectObjective");
const assistPurpose = Taro.getStorageSync("assistPurpose");
const state = reactive({
  isLoading: false,
  isEditMode: false, // 是否为编辑模式
  visitId: null, // 拜访ID
  // 拜访类型字段
  status: "", // 0 定位失败 1定位成功
  params: {
    isPlan: "0", //根据拜访状态判断,
    signType: "0",
    visitTypeCode: "0",
    visitTypeName: "院内拜访",
    signInLat: "",
    signInLon: "",
    institutionCode: "",
    institutionName: "",
    customerName: "",
    customerCode: "",
    insDeptName: "",
    signInDistance: 0,
    signInAddress: "",
    insLat: null,
    insLon: null,
    visitPurposeCode: "one",
    assistRequirement: "",
    assistForm: [],
    communicateContent: "",
    productForms: [],
  },
  insLonNoFlag: false,
  showPreview: false,
  imgData: [],
  calibrationData: {},
  info: {},
  type: "",
  lastCommunicateContent: "",
  visitPurposeName: "",
  assistRequireName: "",
  optionsRadio: [
    {
      text: "认可",
      label: "1",
    },
    {
      text: "不认可",
      label: "0",
    },
    {
      text: "中立",
      label: "2",
    },
  ],
  // 推广信息配置
  promotionInfoList: [],
});
const selectCusClick = () => {
  if (state.id || state.isEditMode) return;
  cusPopupRef.value.open();
};

const selectCus = async (obj) => {
  state.params.institutionCode = obj.insCode;
  state.params.institutionName = obj.insName;
  state.params.customerName = obj.customerName;
  state.params.customerCode = obj.customerCode;
  state.params.insDeptName = obj.insDeptName;
  state.params.insLat = obj.latitude;
  state.params.insLon = obj.longitude;
  state.params.jurCode = obj.jurCode;
  state.params.address = obj.address;
  state.params.insLabel = obj.insLabel;
  state.calibrationData = obj;

  Taro.setStorage("customerDetail", {
    ...obj,
    insCode: state.params.institutionCode,
    insName: state.params.institutionName,
  });

  // 自动选择第一个产品
  await autoSelectFirstProduct();
};

const addInfo = (type) => {
  if (type === "add") {
    const info = {
      visitInstitutionId: state.info.id,
      lastCommunicateContent: state.info.lastCommunicateContent,
      insCode: state.info.institutionCode,
      communicateContent: state.info.planVisitContent,
    };

    Taro.navigateTo({
      url: `/pages/institutionalVisits/temporary?type=add&visitInstitutionId=${
        state.info.id
      }&info=${JSON.stringify(info)}`,
    });
  } else {
    Taro.navigateTo({
      url: `/pages/institutionalVisits/temporary?type=edit&visitInstitutionId=${state.info.id}`,
    });
  }
};

const chooseImage = () =>
  new Promise((res, rej) => {
    try {
      tt.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (data) => {
          console.log(data, "--data");
          const fileSystemManager = tt.getFileSystemManager();
          fileSystemManager.readFile({
            filePath: data.tempFilePaths?.[0],
            encoding: "base64",
            success(res_v1) {
              const imageType = "image/png";
              const base64ImageURL = `data:${imageType};base64,${res_v1?.data}`;
              res({ url: base64ImageURL, type: "FS" });
            },
            fail(res) {
              console.log(`readFile fail: ${JSON.stringify(res)}`);
            },
          });
        },
        fail: (err) => rej(err),
      });
    } catch (err) {
      console.log("!ttt");
      Taro.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (data) => {
          console.log(data, "---dddd");
          res({ url: data?.tempFilePaths?.[0], type: "H5" });
        },
        fail: (err) => rej(err),
      });
    }
  });
const takePhoto = async () => {
  try {
    const { url, type } = await chooseImage();
    Taro.showLoading({
      mask: true,
      title: "上传中",
    });
    addWatermark(url, type).then((files) => {
      Taro.uploadFile({
        url: process.env.TARO_APP_API + `${baseApi}oss-file/upload`,
        header: {
          clientid: process.env.TARO_APP_CLIENT_ID,
          Authorization: `Bearer ${TokenKey}`,
        },
        filePath: files,
        name: "file",
        fail: () => {
          Taro.hideLoading();
          console.log("上传失败");
        },
        success: (result) => {
          Taro.hideLoading();
          try {
            console.log("上传成功：", JSON.parse(result.data).data);
            photoList.value.push(JSON.parse(result.data).data);
            state.imgData = photoList.value.map((item) => {
              return {
                src: item.url,
              };
            });
          } catch (e) {
            console.log(e);
          }
        },
      });
    });
  } catch (err) {}
};

const lookImage = (i) => {
  initNo.value = i;
  state.showPreview = true;
};
const dayTime = () => {
  const now = new Date();

  // 格式化日期和时间
  const year = now.getFullYear(); // 年
  const month = now.getMonth() + 1; // 月，getMonth() 返回的月份是从 0 开始的
  const day = now.getDate(); // 日
  const hours = now.getHours(); // 时
  const minutes = now.getMinutes(); // 分
  const seconds = now.getSeconds(); // 秒

  // 将月、日、时、分、秒转换为两位数格式
  const formattedMonth = month < 10 ? `0${month}` : month;
  const formattedDay = day < 10 ? `0${day}` : day;
  const formattedHours = hours < 10 ? `0${hours}` : hours;
  const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;

  // 组合成 "年-月-日 时：分：秒" 格式的字符串
  return {
    Y: `${year}/${formattedMonth}/${formattedDay}`,
    H: `${formattedHours}:${formattedMinutes}`,
  };
};
const addWatermark = (imageSrc, type = "") => {
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      canvas.width = image.width;
      canvas.height = image.height;
      ctx.drawImage(image, 0, 0);
      ctx.font = type == "FS" ? "30px Arial" : "80px Arial";
      ctx.fillStyle = "white";
      ctx.fillText(dayTime().Y, canvas.width - (type == "FS" ? 200 : 500), 200);
      ctx.fillText(dayTime().H, canvas.width - (type == "FS" ? 200 : 500), 300);
      try {
        if (initUserInfo?.nickName && initUserInfo?.userName)
          ctx.fillText(
            initUserInfo.nickName + "-" + initUserInfo.userName,
            20,
            canvas.height - 300
          );
        if (state.params?.signInAddress)
          ctx.fillText(state.params.signInAddress, 20, canvas.height - 200);
      } catch (err) {}
      canvas.toBlob(
        function (newBlob) {
          resolve(URL.createObjectURL(newBlob));
        },
        "image/jpeg",
        0.5
      );
    };
    image.onerror = reject;
    image.src = imageSrc;
  });
};
const dataURLToBlob = (dataURL) => {
  // 将base64数据转换为二进制字符串
  var base64 = dataURL.split(",")[1];
  var mimeString = dataURL.split(",")[0].split(":")[1].split(";")[0];
  var binaryString = atob(base64);
  var len = binaryString.length;
  var bytes = new Uint8Array(len);
  for (var i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // 创建Blob对象
  return new Blob([bytes], { type: mimeString });
};

const delPhoto = (index) => {
  photoList.value.splice(index, 1);
  state.imgData = photoList.value.map((item) => {
    return {
      src: item.url,
    };
  });
};
const selectTypeClick = () => {
  if (state.id || state.isEditMode) return;
  visitTypePopupRef.value.open();
};
const visitTypeSelect = (name, code) => {
  state.params.visitTypeName = name;
  state.params.visitTypeCode = code;
};

let timerFooterId = null;
const signIn = async () => {
  if (timerFooterId) {
    clearTimeout(timerFooterId);
    timerFooterId = null;
  }
  // 客户必填校验
  if (!state.params.customerName) {
    Taro.showToast({
      title: "请选择客户",
      icon: "none",
    });
    return;
  }
  if (!state.params.visitPurposeCode) {
    Taro.showToast({
      title: "请选择拜访目的",
      icon: "none",
    });
    return;
  }

  const resBlur = await blurTextarea();
  if (!resBlur) return;

  if (!state.params.productForms.length) {
    Taro.showToast({
      title: "请选择至少一个产品传递观念",
      icon: "none",
    });
    return;
  }

  let otherRemarkMissing = false;
  let acuteRequiredMissing = false;
  let promotionInfoMissing = false;

  state.params.productForms.forEach((item) => {
    item.productInfoList.forEach((product) => {
      // 推广信息认可度必填
      if (product.promotionInfo) {
        state.promotionInfoList.forEach((promotionItem) => {
          if (!product.promotionInfo[promotionItem.id]) {
            promotionInfoMissing = true;
          }
        });
      } else {
        promotionInfoMissing = true;
      }

      // "是否急性发作患者"必填
      if (
        product.hasTherapeuticExperience === "是" &&
        (!product.remark ||
          (product.remark["是否急性发作患者"] !== "是" &&
            product.remark["是否急性发作患者"] !== "否"))
      ) {
        acuteRequiredMissing = true;
      }
      // "其他"为“是”时，“其他备注”必填
      console.log(product.remark, 'product.remark')
      if (
        product.remark &&
        product.remark["其他"] === "是" &&
        (!product.remark["其他备注"] || !product.remark["其他备注"].trim())
      ) {
        otherRemarkMissing = true;
      }
    });
  });

  if (promotionInfoMissing) {
    Taro.showToast({
      title: "请选择推广信息的认可度",
      icon: "none",
    });
    return;
  }
  if (acuteRequiredMissing) {
    Taro.showToast({
      title: "请选择是否急性发作患者",
      icon: "none",
    });
    return;
  }
  if (otherRemarkMissing) {
    Taro.showToast({
      title: "请补充描述其他类型",
      icon: "none",
    });
    return;
  }

  timerFooterId = setTimeout(async () => {
    Taro.showLoading({
      mask: true,
      title: "加载中",
    });
    if (
      state.crmSinInMInfo.remark === "1" &&
      Number(state.params.signInDistance) >
        Number(state.crmSinInMInfo.configValue)
    ) {
      Taro.hideLoading();
      return Taro.showModal({
        content: state.crmSinInMInfo.configName,
        confirmText: "确定",
        duration: 2000,
        showCancel: false,
      });
    }
    const parmas = {
      visitUserHrCode: initUserInfo.userName,
      visitLevel: "1",
    };

    // 构造 VisitContentBo 结构
    const signInPhotoList = photoList.value.map((item) => {
      return {
        attUrl: item.originalUrl,
        ossId: item.ossId,
      };
    });
    const {
      productForms,
      communicateContent,
      assistForm,
      assistRequirement,
      visitPurposeCode,
      extendInfo,
      ...signInParams
    } = state.params;
    const visitContentBo = {
      visitInfoBo: {
        visitPurposeCode,
        communicateContent,
        assistRequirement,
        assistForm,
        productForms,
        extendInfo: extendInfo || "",
      },
      visitSignBo: {
        ...signInParams,
        signInPhotoList,
      },
      crmVisitProductExtension: {
        visitProductName: state.params.productForms?.[0]?.productName,
        extension: JSON.stringify(productForms)
      } 
    };
    try {
      let res;
      if (state.isEditMode) {
        // 编辑模式：调用修改接口
        const updateParams = {
          visitInstitutionId: state.visitId,
          visitPurposeCode,
          communicateContent,
          assistRequirement,
          assistForm,
          productForms,
          extendInfo: extendInfo || "",
          signInPhotoList,
          crmVisitProductExtension: {
            visitProductName: state.params.productForms?.[0]?.productName,
            extension: JSON.stringify(productForms)
          } 
        };
        res = await updateCustomerVisit(updateParams);
      } else {
        // 新增模式：调用添加接口
        res = await addCustomerVisit(visitContentBo);
      }

      Taro.hideLoading();
      if (res.code === 200) {
        Taro.showToast({
          title: state.isEditMode ? "更新成功" : "保存成功",
          icon: "none",
          duration: 2000,
        });
        Taro.navigateBack();
      } else {
        Taro.showToast({
          title: res?.msg || (state.isEditMode ? "更新失败" : "保存失败"),
          icon: "none",
        });
      }
    } catch (e) {
      Taro.hideLoading();
      Taro.showToast({
        title: "接口异常",
        icon: "none",
      });
    }
  }, 300);
};

const getDistance = (type) => {
  if (state.params.insLon && state.params.insLon != "0.0") {
    const params = {
      origins: `${state.params.signInLon},${state.params.signInLat}`,
      destination: `${state.params.insLon},${state.params.insLat}`,
    };

    visitDistance(params)
      .then((res) => {
        if (res.code === 200 && res?.data.length != 0) {
          state.params.signInDistance = res?.data?.[0];
          state.status = "1";
          if (type) {
            Taro.showToast({
              title: "刷新成功",
              icon: "none",
              duration: 2000,
            });
          }
        } else {
          state.status = "1";
          Taro.showToast({
            title: "获取机构位置与当前位置距离失败",
            icon: "none",
            duration: 2000,
          });
        }
      })
      .catch((res) => {
        state.status = "1";
        Taro.showToast({
          title: "获取机构位置与当前位置距离失败",
          icon: "none",
          duration: 2000,
        });
      });
  } else {
    state.status = "1";
    state.params.signInDistance = 0;
    state.insLonNoFlag = true;
  }
};
const getConvert = (longitude, latitude) =>
  new Promise((res) => {
    Taro.request({
      url: `https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${longitude},${latitude}&key=0446b950e065d1e0b98a9e97f08dfbff&coordsys=gps`,
      success: (result_before) => {
        const [lon, lat] = result_before.data.locations.split(",");
        res({ lon, lat });
      },
    });
  });

let timer = null;

const getCrmSinInM = async () => {
  const res = await crmSinInM();
  state.crmSinInMInfo = res.rows[0] || {};
};

// 获取推广信息配置
const getPromotionInfoConfig = async () => {
  try {
    const res = await getProductInfoConfig();
    if (res?.msg) {
      // 解析配置值，期望格式为 JSON 字符串
      const configData = JSON.parse(res.msg);
      state.promotionInfoList = configData || [];
      console.log("推广信息配置:", state.promotionInfoList);
    }
  } catch (error) {
    console.error("获取推广信息配置失败:", error);
    // 设置默认配置
    state.promotionInfoList = [
    ];
  }
};
const blurTextarea = async () => {
  const res = await filtering({ txt: state.params.communicateContent });
  if (res.data.length) {
    const content = `本次沟通内容包含如下敏感词：${res.data.join(
      "、"
    )}，请重新输入`;
    Taro.showModal({
      content: content,
      confirmText: "确定",
      duration: 2000,
      showCancel: false,
    });
    return false;
  } else {
    return true;
  }
};

const calibration = () => {
  calibrationDialogRef.value.open(state.calibrationData);
};
const selectPopup = (type) => {
  state.type = type;
  if (type === "objective") {
    // 重新获取字典数据，确保数据是最新的
    const purposeList = Taro.getStorageSync("selectObjective") || [];
    temporaryPopupRef.value.open(purposeList, "");
  }
  if (type === "assistObjective") {
    // 重新获取字典数据，确保数据是最新的
    const assistList = Taro.getStorageSync("assistPurpose") || [];
    temporaryPopupRef.value.open(assistList, "");
  }
};

const checkConfirm = (list) => {
  console.log("多选的code", list);
  if (state.type === "objective") {
    const purposeList = Taro.getStorageSync("selectObjective") || [];
    const arr = purposeList.filter((item) => list.includes(item.dictValue));
    state.visitPurposeName = arr.map((item) => item.dictLabel).join(",");
    state.params.visitPurposeCode = list.join(",");
  }
  if (state.type === "assistObjective") {
    const assistList = Taro.getStorageSync("assistPurpose") || [];
    const arr = assistList.filter((item) => list.includes(item.dictValue));
    state.assistRequireName = arr.map((item) => item.dictLabel).join(",");
    state.params.assistRequirement = list.join(",");
  }
};

const selectAssist = (list) => {
  if (!state.params.assistForm.length) {
    state.params.assistForm = list;
  } else {
    const newList = list.filter(
      (item) =>
        !state.params.assistForm.some(
          (existingItem) => existingItem.id === item.id
        )
    );
    state.params.assistForm = [...state.params.assistForm, ...newList];
  }

  state.params.assistForm = state.params.assistForm.slice(0, 10);
};

const deleteAssist = (v) => {
  Taro.showModal({
    content: `是否删除协访人${v.empName}`,
    success: async function (res) {
      if (res.confirm) {
        state.params.assistForm = state.params.assistForm.filter(
          (item) => item.id !== v.id
        );
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};
// 自动获取产品列表并选择第一项
const autoSelectFirstProduct = async () => {
  if (!state.params.institutionCode) {
    return;
  }

  try {
    const initHrInfo = Taro.getStorageSync("initHrInfo");
    const params = {
      insCode: state.params.institutionCode,
      jurCodeList: initHrInfo?.jurCodeList || [],
      selfJurCodeList: initHrInfo?.selfJurCodeList || [],
      subJurCodeList: initHrInfo?.subJurCodeList || [],
      productName: "",
    };

    const res = await institutionProducts(params);
    if (res?.data && res.data.length > 0) {
      const firstProduct = res.data[0];
      if (firstProduct.productInfoList && firstProduct.productInfoList.length > 0) {
        // 自动选择第一个产品的第一项
        const selectedProduct = {
          ...firstProduct,
          productInfoList: [
            {
              ...firstProduct.productInfoList[0],
              cusProdRespCode: "",
              hasTherapeuticExperience: "否",
              treatmentType: "",
              promotionInfo: {},
              remark: {
                是否急性发作患者: "否",
                "难治性痛风患者(痛风石、或经治疗年发作次数≥2次)": "否",
                痛风伴CKD患者: "否",
                痛风合并心脑血管疾病: "否",
                "对传统治疗（秋水仙碱、非甾体抗炎药、激素）不耐受的痛风患者": "否",
                其他: "否",
                其他备注: "",
              },
            }
          ]
        };

        state.params.productForms = [selectedProduct];
        console.log("自动选择的产品:", selectedProduct);
      }
    }
  } catch (error) {
    console.error("自动获取产品失败:", error);
  }
};

const selectProducts = () => {
  if (state.params.productForms.length) {
    Taro.setStorage({ key: "setPro", data: state.params.productForms });
  }
  console.log(state.info, "--state.info");
  Taro.navigateTo({
    url: `/pages/institutionalVisits048/components/selectProducts?insCode=${state.params.institutionCode}`,
  });
};
const deleteProduct = (code, id) => {
  state.params.productForms = state.params.productForms.map((productForm) => {
    if (productForm.productCode === code) {
      productForm.productInfoList = productForm.productInfoList.filter(
        (info) => info.infoId !== id
      );
    }
    return productForm;
  });

  state.params.productForms = state.params.productForms.filter(
    (productForm) => {
      if (productForm.productCode === code) {
        return productForm.productInfoList.length > 0;
      }
      return true;
    }
  );
  console.log("state.params.productForms", state.params.productForms);
};
const openAssist = () => {
  if (state.params.assistForm.length === 10) {
    Taro.showToast({
      title: "协访人最多只能添加10个",
      icon: "none",
    });
    return;
  }
  assistPopupRef.value.open();
};

const selectLocation = (location) => {
  console.log("定位校准", location);
  state.insLonNoFlag = false;
  state.params.insLat = location.location.lat.toString();
  state.params.insLon = location.location.lng.toString();
  getDistance();
};

const surveyNameMap = ref({});

async function fetchSurveyName(productCode) {
  if (surveyNameMap.value[productCode]) return surveyNameMap.value[productCode];
  const res = await surveyListApi({ productCode, status: "1" });
  const survey = (res.rows || []).find((d) => d.productCode === productCode);
  if (survey) {
    surveyNameMap.value[productCode] = survey.name;
    return survey.name;
  }
  return "";
}

async function handleSurveyClick(productCode, productName) {
  if (!state.params.customerCode) {
    Taro.showToast({
      icon: "none",
      title: "请先选择客户",
    });
    return;
  }
  // 查询问卷列表
  const res = await surveyListApi({ productCode, status: "1" });
  const survey = (res.rows || []).find((d) => d.productCode === productCode);
  if (survey) {
    surveyNameMap.value[productCode] = survey.name;

    await Taro.setStorageSync("_survey", survey);
    await Taro.navigateTo({
      url: `/pages/area/customer/survey?surveyId=${
        survey.id
      }&productCode=${productCode}&productName=${encodeURIComponent(
        productName
      )}&type=048&customerCode=${
        state.params.customerCode
      }&customerName=${encodeURIComponent(state.params.customerName)}&insCode=${
        state.params.institutionCode
      }&insName=${encodeURIComponent(
        state.params.institutionName
      )}&deptName=${encodeURIComponent(
        state.params.insDeptName || state.params.deptName
      )}`,
    });
  } else {
    Taro.showToast({ title: "暂无问卷", icon: "none" });
  }
}

// 处理图片URL获取的辅助函数
const processPhotoItem = async (arr) => {
  if (!arr || !arr.length) return [];
  try {
    const photoResults = await Promise.all(
      arr.map(async (ossId) => {
        const res = await imageUrl(ossId);
        if (res?.data?.rows && res.data.rows.length > 0) {
          return {
            url: res.data.rows[0].url,
            originalUrl: res.data.rows[0].url,
            ossId: res.data.rows[0].ossId || ossId,
          };
        }
        return null;
      })
    );
    return photoResults.filter((item) => item !== null);
  } catch (error) {
    console.error("获取附件详情失败:", error);
    return [];
  }
};

// 获取拜访详情
const getVisitDetailData = async (visitId) => {
  try {
    Taro.showLoading({
      mask: true,
      title: "加载中",
    });
    // 自动选择第一个产品
    await autoSelectFirstProduct();
    const res = await getVisitDetail({
      visitInstitutionId: visitId,
    });
    Taro.hideLoading();

    if (res.code === 200 && res.data) {
      const detail = res.data;
      console.log("拜访详情数据:", detail);

      // 回显基本信息
      state.params.institutionCode = detail.institutionCode;
      state.params.institutionName = detail.institutionName;
      state.params.customerName = detail.customerName;
      state.params.customerCode = detail.customerCode;
      state.params.insDeptName = detail.insDeptName;
      state.params.insLat = detail.insLat;
      state.params.insLon = detail.insLon;
      state.params.jurCode = detail.jurCode;
      state.params.address = detail.insAddress;
      state.params.insLabel = detail.insLabel;
      state.params.visitTypeCode = detail.visitTypeCode;
      state.params.visitTypeName =
        detail.visitTypeCode === "0" ? "院内拜访" : "院外拜访";
      state.params.visitPurposeCode = detail.visitPurposeCode;
      state.params.communicateContent =
        detail.communicateContent || detail.customerCommunicateContent;
      state.params.assistRequirement = detail.assistRequirement;
      state.params.assistForm = detail.assistUserVOs || [];
      // state.params.productForms = detail.productForms || [];
      state.params.productForms = detail.crmVisitProductExtension?.[0]?.extension ? JSON.parse(detail.crmVisitProductExtension?.[0]?.extension) : [];
      state.lastCommunicateContent = detail.lastCommunicateContent;
      if(!detail.crmVisitProductExtension?.length) {
        // 自动选择第一个产品
        await autoSelectFirstProduct();
      }
      Taro.setStorage("customerDetail", {
        ...state.params,
        insCode: state.params.institutionCode,
        insName: state.params.institutionName,
      });

      // 回显签到信息
      if (detail.signInTime) {
        state.params.signInTime = detail.signInTime;
        state.params.signInAddress = detail.signInAddress;
        state.params.signInLat = detail.signInLat;
        state.params.signInLon = detail.signInLon;
        state.params.signInDistance = detail.signInDistance;
      }

      // 回显照片
      if (detail.signInPhotoList && detail.signInPhotoList.length > 0) {
        // 处理 signInPhotoList，调用附件详情接口获取真实URL
        const photoResults = await processPhotoItem(detail.signInPhotoList);
        photoList.value = photoResults;
        state.imgData = photoList.value.map((item) => ({
          src: item.url,
        }));
      }

      // 如果有签退照片，也处理一下（虽然当前页面主要是签到）
      if (detail.signOutPhotoList && detail.signOutPhotoList.length > 0) {
        console.log("签退照片列表:", detail.signOutPhotoList);
        // 这里可以根据需要处理签退照片
      }

      // 设置拜访目的和协访需求的显示名称
      console.log("拜访目的编码:", detail.visitPurposeCode);
      console.log("协访需求编码:", detail.assistRequirement);

      if (detail.visitPurposeCode) {
        // 重新获取字典数据，确保数据是最新的
        const purposeList = Taro.getStorageSync("selectObjective") || [];
        console.log("拜访目的字典:", purposeList);

        if (purposeList.length > 0) {
          // 处理多个拜访目的的情况（逗号分隔）
          const purposeCodes = detail.visitPurposeCode.split(",");
          const purposeNames = purposeCodes.map((code) => {
            const purpose = purposeList.find(
              (item) => item.dictValue === code.trim()
            );
            return purpose ? purpose.dictLabel : code;
          });
          state.visitPurposeName = purposeNames.join(",");
        } else {
          // 如果字典数据为空，直接使用编码作为显示名称
          state.visitPurposeName = detail.visitPurposeCode;
        }
      }

      if (detail.assistRequirement) {
        // 重新获取字典数据，确保数据是最新的
        const assistList = Taro.getStorageSync("assistPurpose") || [];
        console.log("协访需求字典:", assistList);

        if (assistList.length > 0) {
          // 处理多个协访需求的情况（逗号分隔）
          const assistCodes = detail.assistRequirement.split(",");
          const assistNames = assistCodes.map((code) => {
            const assist = assistList.find(
              (item) => item.dictValue === code.trim()
            );
            return assist ? assist.dictLabel : code;
          });
          state.assistRequireName = assistNames.join(",");
        } else {
          // 如果字典数据为空，直接使用编码作为显示名称
          state.assistRequireName = detail.assistRequirement;
        }
      }

      // 设置校准数据
      state.calibrationData = {
        insCode: detail.institutionCode,
        insName: detail.institutionName,
        customerName: detail.customerName,
        customerCode: detail.customerCode,
        insDeptName: detail.insDeptName,
        latitude: detail.insLat,
        longitude: detail.insLon,
        jurCode: detail.jurCode,
        address: detail.insAddress,
        insLabel: detail.insLabel,
      };

      // 预加载问卷名
      for (const item of state.params.productForms) {
        fetchSurveyName(item.productCode);
      }
    } else {
      Taro.showToast({
        title: res?.msg || "获取详情失败",
        icon: "none",
      });
    }
  } catch (error) {
    Taro.hideLoading();
    console.error("获取拜访详情失败:", error);
    Taro.showToast({
      title: "获取详情失败",
      icon: "none",
    });
  }
};

onMounted(async () => {
  Taro.setNavigationBarTitle({ title: "拜访签到" });
  apiAuth();
  getCrmSinInM();

  // 获取推广信息配置
  await getPromotionInfoConfig();

  // 预加载问卷名称
  await fetchSurveyName("0JBX");

  // 判断是否为编辑模式
  if (query.mode === "edit" && query.id) {
    state.isEditMode = true;
    state.visitId = query.id;
    Taro.setNavigationBarTitle({ title: "编辑拜访" });

    // 获取拜访详情
    await getVisitDetailData(query.id);
  } else if (query.info) {
    // 从 area 列表带过来的客户信息
    try {
      const info = JSON.parse(decodeURIComponent(query.info));
      state.params.institutionCode = info.insCode;
      state.params.institutionName = info.insName;
      state.params.customerName = info.customerName;
      state.params.customerCode = info.customerCode;
      state.params.insDeptName = info.insDeptName;
      state.params.insLat = info.latitude;
      state.params.insLon = info.longitude;
      state.params.jurCode = info.jurCode;
      state.params.address = info.address;
      state.params.insLabel = info.insLabel;
      state.calibrationData = info;

      Taro.setStorage("customerDetail", {
        ...state.params,
        insCode: state.params.institutionCode,
        insName: state.params.institutionName,
      });

      // 自动选择第一个产品
      await autoSelectFirstProduct();

      // state.isEditMode = true;
    } catch (e) {
      // fallback to customerQuery
      // const customerQuery = Taro.getStorageSync("customerQuery");
      // state.params.institutionCode = customerQuery.insCode;
      // state.params.institutionName = customerQuery.insName;
      // state.params.customerName = customerQuery.customerName;
      // state.params.customerCode = customerQuery.customerCode;
      // state.params.insDeptName = customerQuery.insDeptName;
      // state.params.insLat = customerQuery.latitude;
      // state.params.insLon = customerQuery.longitude;
      // state.params.jurCode = customerQuery.jurCode;
      // state.params.address = customerQuery.address;
      // state.params.insLabel = customerQuery.insLabel;
      // state.calibrationData = customerQuery;
      // state.isEditMode = true;
    }
    Taro.removeStorageSync("customerQuery");
  } else if (query.source) {
    // 原有的客户查询逻辑
    const customerQuery = Taro.getStorageSync("customerQuery");
    state.params.institutionCode = customerQuery.insCode;
    state.params.institutionName = customerQuery.insName;
    state.params.customerName = customerQuery.customerName;
    state.params.customerCode = customerQuery.customerCode;
    state.params.insDeptName = customerQuery.insDeptName;
    state.params.insLat = customerQuery.latitude;
    state.params.insLon = customerQuery.longitude;
    state.params.jurCode = customerQuery.jurCode;
    state.params.address = customerQuery.address;
    state.params.insLabel = customerQuery.insLabel;
    state.calibrationData = customerQuery;
    Taro.removeStorageSync("customerQuery");

    // 自动选择第一个产品
    await autoSelectFirstProduct();
  }

  // 预加载问卷名
  for (const item of state.params.productForms) {
    fetchSurveyName(item.productCode);
  }
});

useDidShow(() => {
  state.imgData = photoList.value.map((item) => {
    return {
      src: item.url,
    };
  });
  const selectePro = Taro.getStorageSync("selectePro");
  if (selectePro.length) {
    state.params.productForms = selectePro;

    Taro.removeStorageSync("selectePro");
  }
  state.params.productForms = state.params.productForms.map((product) => ({
    ...product,
    productInfoList: product.productInfoList.map((info) => ({
      ...info,
      hasTherapeuticExperience: info.hasTherapeuticExperience || "否",
      treatmentType: info.treatmentType || "",
      promotionInfo: info.promotionInfo || {},
      remark: {
        ...(info.remark && typeof info.remark === "object" ? info.remark : {}),
        是否急性发作患者:
          info.remark &&
          typeof info.remark === "object" &&
          typeof info.remark["是否急性发作患者"] !== "undefined"
            ? info.remark["是否急性发作患者"]
            : "否",
        "难治性痛风患者(痛风石、或经治疗年发作次数≥2次)":
          info.remark &&
          typeof info.remark === "object" &&
          typeof info.remark[
            "难治性痛风患者(痛风石、或经治疗年发作次数≥2次)"
          ] !== "undefined"
            ? info.remark["难治性痛风患者(痛风石、或经治疗年发作次数≥2次)"]
            : "否",
        痛风伴CKD患者:
          info.remark &&
          typeof info.remark === "object" &&
          typeof info.remark["痛风伴CKD患者"] !== "undefined"
            ? info.remark["痛风伴CKD患者"]
            : "否",
        痛风合并心脑血管疾病:
          info.remark &&
          typeof info.remark === "object" &&
          typeof info.remark["痛风合并心脑血管疾病"] !== "undefined"
            ? info.remark["痛风合并心脑血管疾病"]
            : "否",
        "对传统治疗（秋水仙碱、非甾体抗炎药、激素）不耐受的痛风患者":
          info.remark &&
          typeof info.remark === "object" &&
          typeof info.remark[
            "对传统治疗（秋水仙碱、非甾体抗炎药、激素）不耐受的痛风患者"
          ] !== "undefined"
            ? info.remark[
                "对传统治疗（秋水仙碱、非甾体抗炎药、激素）不耐受的痛风患者"
              ]
            : "否",
        其他:
          info.remark &&
          typeof info.remark === "object" &&
          typeof info.remark["其他"] !== "undefined"
            ? info.remark["其他"]
            : "否",
        其他备注:
          info.remark &&
          typeof info.remark === "object" &&
          typeof info.remark["其他备注"] !== "undefined"
            ? info.remark["其他备注"]
            : "",
      },
    })),
  }));
});

onUnmounted(() => {
  Taro.removeStorageSync("photos");
});
</script>

<style lang="scss">
.overflowStyle {
  overflow: hidden;
}

.sign .nut-cell__value {
  font-size: 16px;
}

.signIn {
  color: #1d212b;
  padding-top: 16px;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC-Regular;

  .sign-select {
    margin: 12px;
    padding: 16px;
    padding-top: 2px;
    margin-top: 8px;
    background: #fff;
    border-radius: 8px;

    .subtitle {
      .nut-cell__title-desc {
        padding: 8px 0;
      }
    }

    .sculpture {
      margin-bottom: 10px;

      .selectUser {
        display: flex;
        font-size: 12px;
        height: 48px;
        align-items: center;

        .selectUser-title {
          font-size: 16px;
          color: #333;
        }

        .deteleText {
          color: #999;
          font-size: 10px;
          margin-left: 5px;
        }
      }

      .headSculpture {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        font-size: 16px;

        img {
          display: inline-block;
          width: 48px;
          height: 48px;
          margin-top: 5px;
        }

        // .picture {}

        .text {
          width: 46px;
          text-align: center;
        }
      }
    }

    .customerstext {
      background-color: #ffffff;
      margin-top: 8px;
      font-family: PingFang SC;

      .title {
        display: flex;
        justify-content: space-between;
        height: 50px;
        align-items: center;
      }

      .title-pro {
        // &::before {
        //   content: "*";
        //   color: red;
        //   margin-right: 5px;
        //   vertical-align: middle;
        // }
      }

      .productText {
        color: #999;
      }

      .product {
        background-color: #f8fafe;
        margin-top: 8px;

        padding-bottom: 0;

        .productNames {
          display: flex;
          justify-content: space-between;
          border-bottom: 0.96px solid #edf0f3;
          color: #2551f2;
          font-size: 16px;
          font-weight: 500;
          padding: 12px;
        }

        .deleteIcon {
          display: inline-block;
          width: 18px;
          height: 18px;
        }

        .productContent {
          color: #75798d;

          .info {
            font-size: 14px;
          }
        }

        .feedback {
          display: flex;
          align-items: center;
          padding-left: 12px;
          align-items: flex-end;

          .radioIcon {
            width: 20px;
            height: 20px;
            display: inline-block;
            // margin-top: 2px;
          }

          .approve {
            min-width: 55px;

            &::before {
              content: "*";
              color: red;
              margin-right: 5px;
              vertical-align: middle;
            }
          }

        .approve-optional {
          min-width: 55px;
        }
        }
      }

      .nut-radio-group {
        display: flex;
        justify-content: space-between;
        padding: 0 10px;
        flex-wrap: wrap;
        justify-content: left;
      }

      .nut-radio {
        background: none;
        border: none;
        padding: 12px 0 0 2px;
      }
      .nut-radio__label {
        font-size: 14px;
      }
    }

    .cellClick .arrowIcon {
      margin-right: 0;
      margin-top: 2px;
    }

    .photograph {
      //display: flex;
      //justify-content: space-between;
      .photo-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .photo {
          width: 80px;
          height: 80px;
          position: relative;
          overflow: hidden;

          .del {
            position: absolute;
            font-size: 12px;
            right: -12px;
            top: -10px;
            width: 24px;
            height: 24px;
            background: black;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            color: #fff;
            padding-top: 7px;
            padding-right: 7px;
          }

          img {
            width: 100%;
          }
        }
      }
    }
  }

  .nut-button--plain.nut-button--primary {
    border-color: #e5e6eb;
  }

  .nut-button .nut-button__text {
    margin: 0;
  }

  .nut-radio-group--horizontal .nut-radio {
    margin-bottom: 0;
    margin-right: 0px;
  }
}

.therapeutic-experience {
  padding: 0 12px 12px;

  .experience-item {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 4px;

    .experience-label {
      flex: 1;
      font-size: 14px;
      color: #666;
    }

    .experience-unit {
      margin-left: 8px;
    }
  }
}

.promotion-item {
  margin-bottom: 16px;

  .promotion-content {
    padding: 0 12px;

    .promotion-text {
      font-size: 14px;
      color: #333;
    }
  }
}

.survey-link {
  display: inline-flex;
  align-items: center;
  color: #2551f2;
  font-size: 14px;
  cursor: pointer;
  margin-left: 12px;

  .arrow {
    margin-left: 2px;
    font-size: 16px;
  }
}

.survey-link-underline {
  color: #2551f2;
  text-decoration: underline;
  cursor: pointer;
  font-size: 15px;
  display: inline-block;
  margin-bottom: 4px;
}
</style>
