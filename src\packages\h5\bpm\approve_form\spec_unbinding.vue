<template>
  <view class="bg-white px-[16px]">

    <view class="text-[14px] text-[#2551F2]" v-if="formData.tips">注：<br /> {{ formData.tips }}</view>
    <view class="pt-[16px]">
      <view class="bg-white flex gap-[10px]">
        <view>
          <img :src="hospitalIcon" class="min-w-[48px] h-[48px]" />
        </view>
        <view>
          <view class="text-[#1D212B] text-[18px] leading-[21px] pb-[4px]">{{
            props.formData.dsName
          }}</view>
          <view class="text-[#869199] text-[14px]">{{
            props.formData.dsMdmCode
          }}</view>
        </view>
      </view>
      <view class="bg-white rounded-[8px] mx-[8px] mb-[16px]">
        <view>
          <view v-for="(spec, index) in props.formData.insDsList">
            <view class="flex items-top mb-[8px]" style="border-bottom: 1px dashed#E5E6EB;padding: 12px 0;">
              <view class="text-[#869199] text-[14px] font-400 min-w-[84px]">产品</view>
              <view class="text-[#1D212B] text-[14px] font-400 text-left">{{
                spec.productName
              }}</view>
            </view>
            <view class="flex items-top mb-[8px]" style="padding: 12px 0;">
              <view class="text-[#869199] text-[14px] font-400 min-w-[84px]">品规名称</view>
              <view class="text-[#1D212B] text-[14px] font-400 text-left">{{
                spec.specName
              }}</view>
            </view>
            <view class="flex items-top mb-[8px]" style="padding-bottom: 12px ;"
              :style="{ borderBottom: props.formData.insDsList.length - 1 === index ? 'none' : '1px solid #E5E6EB' }">
              <view class=" text-[#869199] text-[14px] font-400 min-w-[84px]">操作</view>
              <view class="text-[#1D212B] text-[14px] font-400 text-left"
                v-if="props.formType === 'gs_ins_ds_spec_shelves_apply'">{{
                  spec.shelves
                }}</view>
              <view v-if="props.formType === 'gs_ins_ds_spec_unbinding_apply'"
                class="text-[#1D212B] text-[14px] font-400 text-left">
                解绑
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref, computed } from "vue";
import hospitalIcon from "@/bpm-temp/src/assets/images/hospital-icon.png";

const router = useRouter();
const query = router.params;

const props = defineProps({
  formData: {
    type: Object,
    default: {},
  },
  formType: {
    type: String,
    default: '',

  }
});
const options = ref([]);
const state = reactive({
  info: {},
  specList: [],
});


onMounted(() => {
  props.formData = props.formData || {};
  console.log("props.formData", props.formData)
});
</script>

<style lang="scss"></style>
