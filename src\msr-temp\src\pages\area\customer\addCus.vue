<template>
  <view class="addCus">
    <view>
      <view class="section">
        <view class="cusTitle">基本信息</view>
        <nut-form
          class="form"
          :label-width="200"
          :model-value="state.params"
          ref="ruleForm"
        >
          <FormItem
            label="申请人"
            propValue="applicant"
            required
            :readonly="state.infoDisabled"
            v-model="state.params.applicant"
            :rules="state.rules.applicant"
          />
          <!-- <FormItem
            ref="insRef"
            label="机构名称"
            propValue="insName"
            labelWidth="80"
            required
            v-model="state.params.insName"
            @search="searchIns"
            @current:radio="setInsCode"
            placeholder="请选择"
            needSearch
            :popupOption="state.options.insName"
            :rules="state.rules.insName"
          /> -->
          <view v-if="state.sysJuCustAddFlag === '1'">
            <nut-form-item
              label="部门"
              prop="deptName"
              :label-width="100"
              required
              :rules="[{ required: true, message: '必填' }]"
            >
              <nut-input
                clearable
                input-align="right"
                :border="false"
                readonly
                @click="selectClick('postName')"
              >
                <template #right>
                  <view
                    @click="selectClick('postName')"
                    style="font-size: 16px; text-align: right"
                    ><text v-if="state.sysJuCustAddInfo.deptName">{{
                      state.sysJuCustAddInfo.deptName
                    }}</text
                    ><text
                      v-if="!state.sysJuCustAddInfo.deptName"
                      style="color: #c9cdd0"
                      >新增客户，审批通过后自动认领该客户到选择辖区下</text
                    ></view
                  >
                  <img :src="arrow" class="arrowIcon" />
                </template>
              </nut-input>
            </nut-form-item>
            <FormItem
              label="岗位"
              propValue="postName "
              readonly
              placeholder=" "
              v-model="state.sysJuCustAddInfo.postName"
              :rules="state.rules.postName"
            />
          </view>
          <view v-else>
            <nut-form-item label="岗位" prop="postName" :label-width="100">
              <view style="font-size: 16px; text-align: right">{{
                state.info.postName
              }}</view>
            </nut-form-item>
            <nut-form-item label="部门" prop="ancestors" :label-width="100">
              <view style="font-size: 16px">{{ state.info.ancestors }}</view>
            </nut-form-item>
          </view>
          <!-- <FormItem
            label="岗位"
            propValue="postName"
            required
            :readonly="state.infoDisabled"
            v-model="state.params.postName"
            :rules="state.rules.postName"
          />
          <FormItem
            label="部门"
            propValue="ancestors"
            required
            labelWidth="50"
            type="ellipsis"
            :readonly="state.infoDisabled"
            v-model="state.params.ancestors"
          /> -->
          <FormItem
            label="客户名称"
            propValue="customerName"
            required
            v-model="state.params.customerName"
            :rules="state.rules.customerName"
          />
          <!-- <FormItem
            ref="insRef"
            label="机构名称"
            propValue="insName"
            labelWidth="80"
            loadingBrfore
            required
            v-model="state.params.insName"
            @search="searchIns"
            @current:radio="setInsCode"
            :popupRadioDisabled="state.popupRadioDisabled"
            :popupRadioDisabledCallBack="popupRadioDisabledCallBack"
            placeholder="请选择"
            needSearch
            :popupOption="state.options.insName"
            :rules="state.rules.insName"
          /> -->

          <nut-form-item
            label="机构名称"
            prop="insName"
            required
            :label-width="100"
            :rules="[{ required: true, message: '请选择机构名称' }]"
          >
            <nut-input
              v-model="state.params.insName"
              clearable
              input-align="right"
              readonly
              :border="false"
              placeholder="请选择"
              @click="selectInsName"
              v-if="!state.params.insName"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
            <view
              v-else
              @click="selectInsName"
              style="font-size: 16px; text-align: right"
              >{{ state.params.insName || `>` }}</view
            >
          </nut-form-item>

          <FormItem
            label="机构编码"
            propValue="insMdmCode"
            readonly
            placeholder=" "
            v-model="state.params.insMdmCode"
          />
          <FormItem
            label="主执业点"
            propValue="masterFlag"
            v-model="state.params.masterFlag"
            placeholder="请选择"
            required
            needPopup
            :rules="state.rules.masterFlag"
            :popupOption="state.options.masterFlag"
          />
          <FormItem
            label="标准科室"
            propValue="insDeptName"
            required
            v-model="state.params.insDeptName"
            :rules="state.rules.insDeptName"
            placeholder="请选择"
            needPopup
            :popupOption="state.options.insDeptName"
          />
          <!-- <FormItem
            label="科室标签"
            propValue="insDeptTag"
            v-model="state.params.insDeptTag"
          /> -->
          <!-- placeholder="请选择"
            needPopup
            :popupOption="state.options.insDeptTag" -->
          <FormItem
            label="职称"
            propValue="professionTechTitle"
            required
            v-model="state.params.professionTechTitle"
            :rules="state.rules.professionTechTitle"
            placeholder="请选择"
            needPopup
            :popupOption="state.options.professionTechTitle"
          />
          <FormItem
            label="行政级别"
            propValue="administrationLevel"
            required
            :rules="state.rules.administrationLevel"
            v-model="state.params.administrationLevel"
            placeholder="请选择"
            needPopup
            :popupOption="state.options.administrationLevel"
          />
          <!-- <FormItem
            label="省份"
            propValue="district"
            v-model="state.params.district"
            placeholder="请选择"
            needPopup
            type="city"
          /> -->
          <FormItem
            label="性别"
            propValue="sex"
            v-model="state.params.sex"
            placeholder="请选择"
            needPopup
            :popupOption="state.options.sex"
          />
          <nut-form-item
            label="产品"
            prop="productName"
            :label-width="100"
            required
            :rules="[{ required: true, message: '请选择产品' }]"
            v-if="state.sysJuCustAddFlag === '1'"
          >
            <nut-input
              v-model="state.params.productName"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              style="opacity: 0; height: 0; width: 0; overflow: hidden"
              @click="selectProducts"
              v-if="!state.params.productName"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
            <view
              v-else
              @click="selectProducts"
              style="font-size: 16px; text-align: right"
              >{{ state.params.productName || `>` }}</view
            >
          </nut-form-item>
          <FormItem
            label="备注"
            type="textarea"
            propValue="remark"
            v-model="state.params.remark"
          />
        </nut-form>
      </view>
      <FooterButton
        v-if="query.type !== 'masterFlag'"
        text="提交申请"
        @click-button="clickRight"
        :isLoading="state.subLoading"
      />
      <FooterButtonTwo
        v-else
        rightText="确定"
        @click-left="clickLeft"
        @click-right="clickRight"
        :isLoading="state.subLoading"
        leftText="取消"
      />
    </view>
    <SelectIns ref="selectInsRef" @ins-confirm="insConfirm" />
    <UserPopup ref="userPopupRef" @select-user="selectUser" />
    <AddProduct ref="addProductRef" @select-pro="selectPro" />
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import FooterButtonTwo from "../../../pages/components/footerButtonTwo/footerButtonTwo";

import { reactive, ref, onMounted } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { crmInstitutionListApi } from "../../../api/area.js";
import AddProduct from "../components/addProduct.vue";
import { initiateCust, sysJuCustAddApi } from "../../../api/area.js";
import { getPostCode } from "../../../utils/area.js";
import FormItem from "../../../pages/components/formItem/index.vue";
import UserPopup from "../../../pages/area/components/userPopup.vue";
import arrow from "../../../images/arrow.png";
import { appCode, tenantId, getProcessIdApi, h5Url, pcUrl } from "../../../utils/content";
import SelectIns from "./selectIns";
const feishuNotice = ref({
  appLink: `${h5Url}`,
  pcLink: `${pcUrl}`,
});
const initHrInfo = Taro.getStorageSync("initHrInfo");
const orgList = initHrInfo.orgList.filter((item) => item.postType === "1")[0];
const selectInsRef = ref(null);
const router = useRouter();
const query = router.params;
const ruleForm = ref(null);
const insRef = ref(null);
const userPopupRef = ref(null);
const addProductRef = ref(null);
const state = reactive({
  popupRadioDisabled: false,
  rules: {
    administrationLevel: [{ required: true, message: "必填" }],
    masterFlag: [{ required: true, message: "必填" }],
    applicant: [{ required: true, message: "必填" }],
    deptName: [{ required: true, message: "必填" }],
    customerName: [{ required: true, message: "必填" }],
    insName: [{ required: true, message: "必填" }],
    insDeptName: [{ required: true, message: "必填" }],
    professionTechTitle: [{ required: true, message: "必填" }],
    insDeptTag: [{ required: true, message: "必填" }],
    deptName: [{ required: true, message: "必填" }],
  },
  options: {
    insLevel: [],
    insDeptName: [],
    professionTechTitle: [],
    administrationLevel: [],
    insDeptTag: [],
    insName: [],
    sex: [
      { dictLabel: "男", dictValue: "男" },
      { dictLabel: "女", dictValue: "女" },
    ],
    masterFlag: [
      { dictLabel: "是", dictValue: "是" },
      { dictLabel: "否", dictValue: "否" },
    ],
  },

  info: {
    applicant: initHrInfo.empName,
    applicantCode: initHrInfo.empCode,
    postCode: orgList.code,
    postName: orgList.name,
    deptCode: orgList.deptCode,
    deptName: orgList.value,
    ancestors: orgList.ancestors,
    postIdList: initHrInfo.postIdList,
  },
  sysJuCustAddInfo: {
    applicant: initHrInfo.empName,
    applicantCode: initHrInfo.empCode,
    postCode: "",
    postName: "",
    deptCode: "",
    deptName: "",
    ancestors: "",
    jurCode: "",
    postIdList: initHrInfo.postIdList,
  },
  params: {
    type: "insert",
    applicantCode: "",
    insDeptTag: "",
    insName: "",
    district: "",
    postName: "",
    postCode: "",
    customerName: "",
    insName: "",
    insMdmCode: "",
    masterFlag: "",
    insDeptName: "",
    professionTechTitle: "",
    administrationLevel: "",
    sex: "",
    remark: "",
    insCode: "",
    productName: "",
    products: [],
  },
  masterValue: query.type === "add" ? "是" : "否",
  type: "",
  subLoading: false,
  sysJuCustAddFlag: "0",
  processId: "",
});

const clickRight = () => {
  ruleForm.value.validate().then(async ({ valid, errors }) => {
    if (valid) {
      state.subLoading = true;
      const params = {
        applyContent: {
          ...state.params,
        },
        customerCode: state.params.customerCode || "",
        enableWorkflow: true,
        customerName: state.params.customerName,
        insCode: state.params.insCode,
        insName: state.params.insName,
        insMdmCode: state.params.insMdmCode,
        appCode: appCode(),
        tenantId: tenantId,
        applyType:
          query.type === "add" ? "2" : query.type === "masterFlag" ? "3" : "",
        ...(state.sysJuCustAddFlag === "1"
          ? state.sysJuCustAddInfo
          : state.info),
        channel: "2",
        processId: state.processId,
        ...feishuNotice.value,
      };
      const res = await initiateCust(params);
      if (res.msg)
        Taro.showToast({
          title: res.msg,
          icon: "none",
          duration: 2000,
        });
      if (res.code === 200) {
        Taro.reLaunch({
          url: `/pages/customer_manage/index?currentTab=1`,
        });
      }
      state.subLoading = false;
      console.log("success", formData);
    } else {
      console.log("error submit!!", errors);
    }
  });
};

const clickLeft = () => {
  Taro.navigateBack({
    delta: 1,
  });
};

const dictInit = () => {
  state.options.insLevel = Taro.getStorageSync("insLevel");
  state.options.insDeptName = Taro.getStorageSync("deptNameList");
  state.options.professionTechTitle = Taro.getStorageSync("professionList");
  state.options.administrationLevel = Taro.getStorageSync("administrationList");
  state.options.insDeptTag = Taro.getStorageSync("departmentLabelList");
  // initIns();
};
const initBase = () => {
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  const orgList =
    initHrInfo?.orgList?.filter((item) => item.postType === "1")[0] || [];
  state.params.applicant = initHrInfo?.empName;
  // state.params.postName = orgList?.name;
  // state.params.deptName = orgList?.value;
  state.params.applicantCode = initHrInfo?.empCode;
  // state.params.postCode = orgList?.code;
  // state.params.deptCode = orgList?.deptCode;
  state.params.ancestors = orgList?.ancestors;
  state.params.postIdList = initHrInfo?.postIdList || [];
};
const selectClick = () => {
  userPopupRef.value.open(state.params.postCode);
};

const selectProducts = () => {
  if (!state.params.deptName || !state.params.insName) {
    return Taro.showToast({
      title: "请先选择部门或机构",
      icon: "none",
      duration: 2000,
    });
  }
  const params = {
    type: "ins",
    jurCode: state.sysJuCustAddInfo.jurCode,
    insCode: state.params.insCode,
  };

  const checked =
    (state.params.products && state.params.products.map((item) => item.name)) ||
    [];
  addProductRef.value.open("all", params, checked);
};
const selectUser = (item) => {
  state.sysJuCustAddInfo.ancestors = item.ancestors;
  state.sysJuCustAddInfo.deptName = item.deptName;
  state.params.deptName = item.deptName;
  state.sysJuCustAddInfo.deptCode = item.deptCode;
  state.sysJuCustAddInfo.postCode = item.postCode;
  state.sysJuCustAddInfo.postName = item.postName;
  state.sysJuCustAddInfo.jurCode = item.jurCode;
  state.popupRadioDisabled = true;
  state.params.insName = "";
  state.params.insMdmCode = "";
  state.params.insCode = "";
};
const selectInsName = () => {
  if (!state.params.deptName && state.sysJuCustAddFlag === "1")
    return Taro.showToast({
      title: "请先选择部门",
      icon: "none",
      duration: 2000,
    });

  selectInsRef.value.open();
};

const insConfirm = (ins) => {
  state.params.insCode = ins.insCode;
  state.params.insMdmCode = ins.insMdmCode;
  state.params.insName = ins.insName;
};

const getSysJuCustAddApi = async () => {
  const res = await sysJuCustAddApi();
  state.sysJuCustAddFlag = JSON.parse(res.msg).settingStatus;
  state.params = {
    ...state.params,
    ...JSON.parse(res.msg),
    ...(state.sysJuCustAddFlag !== "1" &&
      JSON.parse(decodeURIComponent(query.ins))),
  };
  console.log("ddddd", state.params);
};
const selectPro = (arr) => {
  state.params.products = arr;
  state.params.productName = arr.map((item) => item.name).join(",");
};
const getKey = async () => {
  state.processId = await getProcessIdApi("customer_apply");
};
useDidShow(() => {
  dictInit();
  if (query.type === "add") {
    Taro.setNavigationBarTitle({ title: "新增客户申请" });
    state.params = {
      customerName: decodeURIComponent(query.customerName),
    };
  } else if (query.type === "update") {
    Taro.setNavigationBarTitle({ title: "变更客户申请" });
    state.params = {
      ...JSON.parse(decodeURIComponent(query.item)),
      ...JSON.parse(decodeURIComponent(query.ins)),
    };
  } else {
    Taro.setNavigationBarTitle({ title: "添加执业点" });
    state.params = {
      ...JSON.parse(decodeURIComponent(query.item)),
      ...JSON.parse(decodeURIComponent(query.ins)),
      masterFlag: "否",
    };
  }
  initBase();
});

onMounted(() => {
  getSysJuCustAddApi();
  getKey();
});
</script>
<style lang="scss" scoped>
.cusTitle {
  height: 48px;
  line-height: 48px;
  font-size: 14px;
  padding: 0 16px;
  color: #869199;
}
.arrowIcon {
  width: 16px;
  height: 16px;
  margin-top: 2px;
}
.input-text::placeholder {
  // color: red !important; /* 设置placeholder的文本颜色 */
  // text-align: right;
  // padding-right: 10px;
  // font-size: 14px; /* 设置placeholder的字体大小 */
}
.nut-cell__title {
  // font-size: 16px !important;
}
.nut-cell-group__wrap {
  margin-top: 0;
  padding: 0 20px !important;
  box-shadow: none;
}
.addCus {
  padding-bottom: 90px;
  background: #fff;
  .section {
    font-size: 14px;

    .label {
      color: #1d212b;
      &::before {
        content: "*";
        color: red;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }
  .nut-cell__title {
    color: #1d212b;
  }
  .nut-input-left-box {
    margin-right: 10px;
  }
  .nut-input {
    padding: 0;
    border-bottom: none;
  }
}
.input-text::placeholder {
  color: #c9cdd0;
  word-break: break-all;
}
</style>
