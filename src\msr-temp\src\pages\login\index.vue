<template>
  <view class="login">
    <view>
      <img :src="logo" alt="" class="logo" />
    </view>
    <view class="form" v-if="state.account">
      <nut-form>
        <nut-form-item>
          <nut-input
            v-model="state.username"
            class="nut-input-text"
            placeholder="请输入账号"
            type="text"
          />
        </nut-form-item>
        <nut-form-item>
          <nut-input
            v-model="state.password"
            class="nut-input-text"
            placeholder="请输入密码"
            type="text"
          />
        </nut-form-item>
      </nut-form>
      <nut-button
        block
        :style="{
          'margin-top': '20%',
          'background-color': state.loginFlag ? 'none' : ' #2551F2',
          color: state.loginFlag ? '#333' : '#fff',
        }"
        @click="onSubmit"
        shape="square"
        :disabled="state.loginFlag"
        >登陆</nut-button
      >
      <view class="btnLogin" style="margin-top: 20px; padding: 0">
        <nut-button block shape="square" plain color="#2551F2" @click="fsLogin">
          <template #icon>
            <img :src="feishu" alt="" class="fsIcon" />
          </template>
          飞书用户一键登录
        </nut-button>
      </view>
    </view>
    <view v-if="!state.account">
      <view class="btnLogin">
        <nut-button block shape="square" color="#2551F2" @click="fsLogin">
          <template #icon>
            <img :src="feishu" alt="" class="fsIcon" />
          </template>
          飞书用户一键登录
        </nut-button>
      </view>
      <view class="account" @tap="selectAccount">账号登录</view>
    </view>
  </view>
</template>
<script setup>
import Taro, { useRouter } from "@tarojs/taro";
import { reactive, onMounted, ref } from "vue";
import logo from "@/images/login_bgc_2.png";
import feishu from "@/images/feishu.png";
import { login, getUserInfo, hrDetail } from "@/api/login.js";
import {
  getSelectObjectiveList,
  getVisitTypeList,
  getAssistPurpose,
} from "@/utils/content.js";
import {
  getDeptNameList,
  getProfessionList,
  geAdministrationList,
  getCusLevelList,
  getCusPotenLevel,
  getDepartmentLabelList,
} from "@/utils/customer";
import {
  getInsLevel,
  getInsprofessionType,
  getEconomicType,
  getInsGrade,
} from "@/utils/ins";
import {
  getPharmacyType,
  getAreaType,
  getPharmacyNature,
  getPaymentType,
  getBusinessScope,
  getApproveType,
  getDevelopType,
} from "@/utils/pharmacy";
import { getDistrict, getApplyFormType } from "@/utils/area.js";

const router = useRouter();
const state = reactive({
  username: "GS11020", //'H2330', // 'GS8342',
  password: "admin123",
  code: "",
  img: "",
  uuid: "",
  account: false,
  loginFlag: false,
});
import { appCode, tenantId, fsUrl } from "@/utils/content";
const clientId = process.env.TARO_APP_CLIENT_ID;
const captchaEnabled = ref(true);
const fsLogin = () => {
  window.location.href = fsUrl;
};
const onSubmit = async () => {
  state.loginFlag = true;

  try {
    const parmas = {
      username: state.username,
      password: state.password,
      clientId,
      grantType: "password",
      uuid: state.uuid,
      code: state.code,
      tenantId: tenantId,
    };

    const res = await login(parmas);
    if (res.code === 200) {
      Taro.setStorageSync("access_token", res.data.access_token);
      const userData = await getUserInfo();
      Taro.setStorage({ key: "initUserInfo", data: userData.data });
      const hrData = await hrDetail({
        appCode: appCode(),
        tenantId: tenantId,
      });
      Taro.setStorage({ key: "initHrInfo", data: hrData.data });
      Taro.redirectTo({ url: "/pages/index/index" });
      getSelectObjectiveList();
      getVisitTypeList();
      getDeptNameList();
      getProfessionList();
      geAdministrationList();
      getCusLevelList();
      getAssistPurpose();
      getDistrict();
      getCusPotenLevel();
      getDepartmentLabelList();
      getInsLevel();
      getInsprofessionType();
      getEconomicType();
      getPharmacyType();
      getAreaType();
      getPharmacyNature();
      getPaymentType();
      getBusinessScope();
      getApproveType();
      getDevelopType();
      getInsGrade();
      getApplyFormType();
    }
  } catch (error) {
    state.loginFlag = false;
    console.error(error, "异常");
  }
};

const selectAccount = () => {
  state.account = true;
};

onMounted(() => {
  Taro.clearStorageSync();

});
</script>
<style lang="scss">
.login {
  position: relative;

  height: 100vh;
  background: linear-gradient(
    180deg,
    #e0f7fd 0%,
    #ffffff 25%,
    #f8faff 79%,
    #f0e6ff 100%
  );

  .logo {
    margin-top: 30%;
    width: 266px;
    height: 40px;
    margin-left: 27%;
  }

  .btnLogin {
    margin-top: 60%;
    padding: 0 24px;

    .fsIcon {
      display: inline-block;
      width: 20px;
      height: 20px;
    }

    // .nut-button--default {
    //   color: #fff;
    //   background: #2551f2;
    // }
  }

  .account {
    color: #333;

    /* 14/Regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    text-align: center;
    margin-top: 21px;
  }

  .form {
    margin-top: 20%;
    padding: 0 41px;

    .nut-cell {
      background: none;
      border-bottom: 1px solid #dedede;
    }

    .verification-code {
      height: 25px;
    }
  }
}
</style>
