<template>
  <view class="customerPopup">
    <GlobalPopup ref="popupRef" @close="close">
      <TopSearch
        searchFlag
        @top-search="search"
        placeholder="请输入客户姓名或科室搜索"
      />
      <scroll-view
        style="height: 500px"
        :scroll-y="true"
        :scroll-top="0"
        @scrolltolower="scrollToLower"
      >
        <nut-radio-group v-model="state.radioChecked" text-position="left">
          <GlobalNutRadio
            v-for="item in state.cusOption"
            :value="item.customerCode"
            :key="item.customerCode"
          >
            <template #name>
              <view>
                <text>{{ item.customerName }}({{ item.insDeptName }})</text>
              </view>
            </template>
          </GlobalNutRadio>
        </nut-radio-group>
      </scroll-view>
      <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import Taro, { useRouter } from "@tarojs/taro";
import GlobalPopup from "../../../pages/components/globalPopup/globalPopup.vue";
import TopSearch from "../../../pages/components/topSearch/topSearch.vue";
import GlobalNutRadio from "../../../pages/components/globalNutRadio/globalNutRadio.vue";
import { custByins } from "../../../api/area";
const emit = defineEmits(["select-cus"]);
const state = reactive({
  radioChecked: "",
  userName: "",
  cusOption: [],
  params: {
    search: "",
    insCode: "",
    pageSize: 10,
    pageNum: 1,
    customerName: "",
  },
  total: 0,
  flag: false,
});
const popupRef = ref(null);

const search = (name) => {
  if (state.flag) return;
  state.radioChecked = "";
  state.params.customerName = name;
  state.params.pageNum = 1;
  state.cusOption = [];
  state.total = 0;
  getCusOption();
};

const getCusOption = async () => {
  state.flag = true;
  const res = await custByins(state.params);
  if (!state.cusOption.length) {
    state.cusOption = res.data.rows;
  } else {
    state.cusOption = [...state.cusOption, ...res.data.rows];
  }
  state.total = res.data.total;
  state.flag = false;
};

const scrollToLower = () => {
  console.log("eeeee");
  if (!state.flag && state.cusOption.length < state.total) {
    state.params.pageNum++;
    getCusOption();
  }
};
const confirm = () => {
  if (!state.radioChecked) return;
  const arr = state.cusOption.filter(
    (item) => item.customerCode === state.radioChecked
  );
  emit("select-cus", arr[0]);
  popupRef.value.close();
  state.radioChecked = "";
  state.cusOption = [];
};
const open = (insCode, code, jurCode) => {
  state.radioChecked = code;
  state.params.insCode = insCode;
  state.params.jurCode = jurCode;
  popupRef.value.open();
  state.params.pageNum = 1;
  state.total = 0;
  state.params.customerName = "";
  state.cusOption = [];

  getCusOption();
};

defineExpose({
  open,
});
</script>
<style lang="scss">
.customerPopup {
  background: #f4f5f7;
  .nut-radio-group {
    padding: 0 16px;
  }
  .nut-radio {
    background: #fff;
  }

  .footer-comfirm {
    height: 60px;
    padding: 16px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    padding-right: 0;
  }
}
</style>
