<template>
  <view class="approveList">
    <ListFilter :list="state.list" @activeItem="activeItem" />
    <scroll-view
      style="height: 500px; position: relative"
      :scroll-y="true"
      :scroll-top="state.scrollTop"
      @scrolltolower="onScrollToLower"
    >
      <view
        style="
          position: absolute;
          top: 21%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!state.insList.length && !scrollMyFlag"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>
      <!-- 地区经理视角 -->
      <view v-else>
        <view v-for="item in state.insList" :key="item.id" class="content">
          <view class="content-item">
            <view @click="gotoInst(item)">
              <view class="item-top">
                <text>{{ item.insName }}</text>
              </view>

              <view class="item-time">
                <view> 单号：{{ item.id }}</view>
                <view> {{ item.createTime }}</view>
              </view>
              <nut-divider
                dashed
                :style="{ color: '#e5e6eb', margin: '12px 0' }"
              />
              <view class="item-time" style="font-size: 14px">
                <view
                  ><view>申请人：{{ item.applicant }}</view>
                  <view> 申请类型：新增药店</view></view
                >
              </view>
              <view
                class="approvalStatus"
                :class="statusStyleClass(item.status)"
              >
                <text style="margin-left: 2px">{{
                  statusText(item.status)
                }}</text>
              </view>
              <view
                style="color: red; font-size: 12px"
                v-if="item.status === '-1' && item.rejectType === '1'"
                >【业务审批拒绝】拒绝理由：{{ item.remark }}</view
              >
              <view
                style="color: red; font-size: 12px"
                v-if="item.status === '-1' && item.rejectType === '2'"
                >【主数据审批拒绝】拒绝理由：{{ item.remark }}</view
              >
            </view>
            <nut-button
              type="info"
              color="#F3F4F5"
              style="
                color: #4e595e;
                position: absolute;
                right: 16px;
                bottom: 16px;
              "
              size="small"
              @click="approveCancle(item)"
              v-if="
                initHrInfo.empCode === item.applicantCode &&
                item.status === '1' &&
                item.channel === '1'
              "
              >撤销</nut-button
            >
          </view>
        </view>
      </view>

      <view
        v-if="state.insList.length > 0 && state.insList.length == state.total"
        style="color: #869199; font-size: 16px; text-align: center"
        >已全部加载完毕</view
      >
    </scroll-view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import {
  defineEmits,
  defineProps,
  ref,
  reactive,
  defineExpose,
  computed,
} from "vue";
import arrow from "../../../images/arrow.png";

import arrowRight from "../../../images/arrow-right.png";
import address from "../../../images/location_a.png";
import hospital from "../../../images/iconhospital.png";
import { applyList, cancel } from "../../../api/pharmacy.js";
import { Clock } from "@nutui/icons-vue-taro";
import ListFilter from "../../../pages/components/listFilter/index.vue";
import noData from "../../../images/no-data.png";
const emit = defineEmits(["change-filter"]);
const scrollMyFlag = ref(false);
const initHrInfo = Taro.getStorageSync("initHrInfo");
const props = defineProps({
  tab: {
    type: String,
    default: "",
  },
});

const state = reactive({
  scrollTop: 0,
  search: [],
  params: {
    pageNum: 1,
    pageSize: 10,
    startDate: "",
    endDate: "",
    search: "",
    insName: "",
    applyType: "4",
  },
  total: 0,
  pcdvalue: "",
  insList: [],
  list: [
    { text: "待审批", key: "1", active: false },
    { text: "已审批", key: "2", active: false },
    { text: "我发起", key: "3", active: false },
  ],
  statusMap: [
    { code: "1", text: "审批中", color: "approve" },
    { code: "2", text: "主数据清洗中", color: "approve" },
    {
      code: "3",
      text: "主数据审核完成",
      color: "agree",
    },
    { code: "-1", text: "已拒绝", color: "reject" },
    { code: "-2", text: "已撤销", color: "back" },
  ],
});
const activeItem = (key) => {
  if (scrollMyFlag.value) return;

  state.list.forEach((item) => {
    if (item.key == key) {
      item.active = !item.active;
    }
  });
  state.search = state.list
    .filter((item) => item.active)
    .map((item) => item.key);
  state.params.pageNum = 1;
  state.insList = [];
  getinsList();
};

const getinsList = () => {
  scrollMyFlag.value = true;
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  // if (initHrInfo.identity !== "resp" && !state.search.length) {
  //   state.search.push("4");
  // }
  state.params.search = state.search.join(",") || "";
  const params = {
    ...state.params,
    startDate: state.params.startDate
      ? state.params.startDate + " " + "00:00:00"
      : "",
    endDate: state.params.endDate
      ? state.params.endDate + " " + "23:59:59"
      : "",
  };
  applyList(params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.data.total;
        if (!state.insList.length) {
          state.insList = res.data.rows || [];
          state.insList = state.insList.map((item) => {
            return {
              ...item,
              applyNodeInfo: JSON.parse(item.applyNodeInfo),
              applyContent: JSON.parse(item.applyContent),
            };
          });
          console.log(";;;;;", state.insList);
        } else {
          state.insList = [...state.insList, ...res.data.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = false;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      scrollMyFlag.value = false;
      Taro.hideLoading();
    });
};

const topSearch = (search) => {
  state.params.insName = search;
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getinsList();
};

const approveFilters = () => {
  const info = {
    startDate: state.params.startDate,
    endDate: state.params.endDate,
  };
  Taro.navigateTo({
    url: `/pages/pharmacy/approve/approveFiltes?info=${JSON.stringify(info)}`,
  });
};
const onScrollToLower = () => {
  if (!scrollMyFlag.value && state.insList.length < state.total) {
    state.params.pageNum++;
    getinsList();
  }
};

const statusText = (status) => {
  console.log(status);
  return state.statusMap.filter((item) => item.code === status)[0].text;
};
const statusStyleClass = (status) => {
  return state.statusMap.filter((item) => item.code === status)[0].color;
};
const gotoInst = (item) => {
  if (item.status === "2" || item.status === "3" || item.rejectType === "2")
    return;
  if (item.channel === "2") {
    Taro.navigateTo({
      url: `/bpm-temp/src/pages/approve_detail/index?instanceId=${item.aeFlowId}`,
    });
  } else {
    window.location.href = `${process.env.TARO_APP_APPROVE_URL}/ae/automation/inbox?usingInstanceId=${item.aeFlowId}&hideAction=false&hideForm=false&hideProcess=false&hideTitle=false`;
  }
};
const initInsList = () => {
  const approveFilters = Taro.getStorageSync("approveFilters");
  if (approveFilters) {
    state.params.startDate = approveFilters.startDate;
    state.params.endDate = approveFilters.endDate;
    Taro.removeStorageSync("approveFilters");
  }
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getinsList();
  emit("change-filter", state.params.startDate);
};

const approveCancle = async (item) => {
  Taro.showLoading({
    mask: true,
    title: "撤回中",
  });
  const res = await cancel({ bpmId: item.id });
  if (res.code == 200) {
    Taro.showToast({
      title: "撤回成功",
      icon: "none",
      duration: 2000,
    });
    Taro.hideLoading();
    state.params.pageNum = 1;
    state.insList = [];
    getinsList();
  } else {
    Taro.hideLoading();
    Taro.showToast({
      title: res.msg,
      icon: "error",
      duration: 2000,
    });
  }
};
defineExpose({
  topSearch,
  approveFilters,
  initInsList,
});
</script>
<style lang="scss">
.approveList {
  padding: 0px 0px;
  width: 100vw;
  // height: 630px;
  font-family: PingFang SC, PingFang SC;

  .content {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    margin-left: 12px;
    margin-right: 12px;
    position: relative;
    .content-item {
      position: relative;
      padding: 16px;
      .item-top {
        font-size: 18px;
      }
    }
    .item-time {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #869199;
    }
    .item-type {
      font-size: 14px;
      color: #869199;
    }
  }

  .approve {
    color: #ff7d00;
    background: #fff3e8;
  }
  .agree {
    color: #00b578;
    background: #e6f8f2;
  }
  .reject {
    color: #f32f29;
    background: #feeaea;
  }
  .back {
    color: #4e595e;
    background: #f3f4f5;
  }

  .approvalStatus {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 12px;
    text-align: center;
    height: 20px;
    padding: 0 7px;
    line-height: 20px;
    border-radius: 2px 8px 0 10px;
  }

  .nut-divider.nut-divider-vertical {
    top: 0;
  }
  .add-pharmacy {
    position: absolute;
    right: 16px;
    top: 53%;
  }
}
</style>
