<template>
  <view class="bg-white px-[16px]">
    <view>
      <view class="text-[#1D212B] text-[14px] py-[10px]">基本信息</view>
      <view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >变更类型</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.type
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >辖区负责人</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.jurMgrName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >部门</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.ancestors
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >岗位</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.postName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >机构名称</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insName
          }}</view>
        </view>

        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >客户名称</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.customerName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >认领产品</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.products
          }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { defineProps } from "vue";
const props = defineProps(["formData"]);
</script>
<style lang="scss"></style>
