<template>
  <view class="userPopup">
    <GlobalPopup ref="popupRef">
      <TopSearch @top-search="search" placeholder="请输入姓名" />
      <view v-if="!state.flag && !state.cusOption.length" class="result-loading"
        >搜索暂无数据</view
      >
      <nut-radio-group
        @change="onChange"
        v-model="state.radioChecked"
        text-position="left"
      >
        <GlobalNutRadio
          v-for="item in state.cusOption"
          :value="item.postCode"
          :key="item.postCode"
        >
          <template #name>
            <view>
              <text
                >{{ item.postName }} ｜ {{ item.deptName }} ｜
                {{ item.user.map((item) => item.value).join(",") }}</text
              >
            </view>
          </template>
        </GlobalNutRadio>
      </nut-radio-group>
      <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import Taro, { useRouter } from "@tarojs/taro";
import GlobalPopup from "@/pages/components/globalPopup/globalPopup.vue";
import TopSearch from "@/pages/components/topSearch/topSearch.vue";
import GlobalNutRadio from "@/pages/components/globalNutRadio/globalNutRadio.vue";
import { subDetail } from "@/api/area.js";

const initHrInfo = Taro.getStorageSync("initHrInfo");
const emit = defineEmits(["select-user"]);
const state = reactive({
  radioChecked: "",
  cusOption: [],
  params: {
    postIdList: initHrInfo.postIdList,
    search: "",
  },
  flag: false,
  timer: null,
});
const popupRef = ref(null);
const debounce = (fn, wait) => {
  if (state.timer !== null) {
    clearTimeout(state.timer);
  }
  state.timer = setTimeout(fn, wait);
};
const onChange = (v) => {};
const search = (name) => {
  state.radioChecked = "";
  state.params.search = name;
  getCusOption();
};

const getCusOption = async () => {
  state.flag = true;
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  state.cusOption = [];
  const res = await subDetail(state.params);
  Taro.hideLoading();
  // Taro.setStorageSync("bu", "");
  state.flag = false;
  state.cusOption = res.data;
};
const confirm = () => {
  if (!state.radioChecked) return;

  const arr = state.cusOption.filter(
    (item) => item.postCode === state.radioChecked
  );
  emit("select-user", arr[0]);
  popupRef.value.close();
  state.radioChecked = "";
  Taro.hideLoading();
};
const open = (code) => {
  state.radioChecked = code;
  popupRef.value.open();
  state.params.search = "";
  getCusOption();
};

defineExpose({
  open,
});
</script>
<style lang="scss">
.userPopup {
  background: #f4f5f7;
  .result-loading {
    color: #869199;
    font-size: 12px;
    text-align: center;
    margin: 16px 0;
  }
  .nut-radio-group {
    max-height: 400px;
    overflow: hidden;
    overflow-y: scroll;
    padding: 0 16px;
  }
  .nut-radio {
    overflow: hidden;
    overflow-y: scroll;
    background: #fff;
  }

  .footer-comfirm {
    height: 60px;
    padding: 16px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    padding-right: 0;
  }
}
</style>
