import {
  visitPurpose,
  visitType,
  getCurrentTime,
  assistPurpose,
} from "@/api/institutionalVisitsApi";
import Taro from "@tarojs/taro";
import Rectangle505 from "@/images/Rectangle505.png";
import Rectangle506 from "@/images/Rectangle506.png";
import Rectangle507 from "@/images/Rectangle507.png";
import Rectangle508 from "@/images/Rectangle508.png";
import Rectangle509 from "@/images/Rectangle509.png";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

const scoresList = [
  {
    scoreItemName: "访前计划",
    scoreItemCode: "0",
    score: 0,
  },
  {
    scoreItemName: "工作态度",
    scoreItemCode: "1",
    score: 0,
  },
  {
    scoreItemName: "陈述利益",
    scoreItemCode: "2",
    score: 0,
  },
  {
    scoreItemName: "专业知识",
    scoreItemCode: "3",
    score: 0,
  },
  {
    scoreItemName: "促成共识",
    scoreItemCode: "4",
    score: 0,
  },
  {
    scoreItemName: "客情关系",
    scoreItemCode: "5",
    score: 0,
  },
];
export const dictDataList = scoresList;
export function dictDataValueList(code) {
  const arr = scoresList.filter((item) => item.code === code);
  return arr[0].name;
}
export function getWeekDay(date) {
  if (!(date instanceof Date)) {
    console.error("请传入正确的日期对象");
    return "";
  }
  const weekDays = ["日", "一", "二", "三", "四", "五", "六"];
  return `星期${weekDays[new Date().getDay()]}`;
}

export const serialize = (data) => {
  const list = [];
  Object.keys(data).forEach((ele) => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join("&");
};

export async function getSelectObjectiveList() {
  const { rows } = await visitPurpose();
  Taro.setStorage({
    key: "selectObjective",
    data: rows,
  });
}
export async function getAssistPurpose() {
  const { rows } = await assistPurpose();
  Taro.setStorage({
    key: "assistPurpose",
    data: rows,
  });
}

export async function getVisitTypeList() {
  const { rows } = await visitType();
  Taro.setStorage({
    key: "visitTypeList",
    data: rows,
  });
}
export function visitTypeName(code) {
  const visitTypeList = Taro.getStorageSync("visitTypeList");
  if (code && visitTypeList.length > 0) {
    const names = visitTypeList.filter((item) => item.value === code);
    return names[0].text;
  }
}

export function currentTime() {
  getCurrentTime().then((res) => {
    return res.data;
  });
}

export function getVisitStatusName(code) {
  if (code === "0") {
    return "待拜访";
  }
  if (code === "1") {
    return "已拜访";
  }
  if (code === "2") {
    return "拜访中";
  }
  if (code === "3") {
    return "已取消";
  }
  if (code === "4") {
    return "已过期";
  }
}

export function getBackgroundColor(code) {
  if (code === "0") return "background-color-0";

  if (code === "1") return "background-color-1";

  if (code === "2") return "background-color-2";

  if (code === "4") return "background-color-4";
}

export function getColor(code) {
  if (code === "0") return "color-0";

  if (code === "1") return "color-1";

  if (code === "2") return "color-2";

  if (code === "4") return "color-4";
}
export function getMinutesHours(d) {
  if (!d) return "";
  const dateTime = dayjs(d);
  const hourMinute = dateTime.format("MM-DD HH:mm");
  return hourMinute;
}

export function getCustmerStatusName(code) {
  switch (code) {
    case "0":
      return "待拜访";
    case "1":
      return "已拜访";
    case "2":
      return "取消拜访";
    default:
      return "";
  }
}
export function isBefore(visitTime) {
  const newDate = currentTime();
  const date1 = dayjs(visitTime).format("YYYY-MM-DD");
  const date2 = dayjs(newDate).format("YYYY-MM-DD");
  if (date1 === date2) {
    return true;
  } else {
    return false;
  }
}

export function visitPurposeName(visitPurposeCode) {
  const selectObjectiveList = Taro.getStorageSync("selectObjective");
  if (visitPurposeCode && selectObjectiveList.length > 0) {
    const nameList = selectObjectiveList
      .filter((item) => visitPurposeCode.includes(item.dictValue))
      .map((item) => {
        return item.dictLabel;
      });
    return nameList.join(",");
  }
}
export function assistPurposeName(assistPurposeCode) {
  const assistPurposeList = Taro.getStorageSync("assistPurpose");
  if (assistPurposeCode && assistPurposeList.length > 0) {
    const nameList = assistPurposeList
      .filter((item) => assistPurposeCode.includes(item.dictValue))
      .map((item) => {
        return item.dictLabel;
      });
    return nameList.join(",");
  }
}

export function getIsConfirmInvite(code) {
  switch (code) {
    case "0":
      return "拒绝";
    case "1":
      return "接受";
    default:
      return "";
  }
}

export const customerStatusList = {
  0: { text: "待拜访", color: "not-started" },
  1: { text: "已拜访", color: "started" },
  2: { text: "取消拜访", color: "expire" },
};
export function filterArr(arr, code, key) {
  const existingCodes = new Set(arr.map((item) => item[key]));
  const uniqueCodeArr = code.filter((item) => !existingCodes.has(item[key]));
  return uniqueCodeArr;
}
export function getTags(code) {
  if (code === "0") return Rectangle505;

  if (code === "1") return Rectangle506;

  if (code === "2") return Rectangle508;

  if (code === "4") return Rectangle507;
}

export function formatDate(date, format) {
  if (!(date instanceof Date)) {
    console.error("请传入正确的日期对象");
    return "";
  }
  const map = {
    "Y+": date.getFullYear(), // 年
    "M+": date.getMonth() + 1, // 月份
    "D+": date.getDate(), // 日
    "h+": date.getHours(), // 小时
    "m+": date.getMinutes(), // 分
    "s+": date.getSeconds(), // 秒
  };

  for (const key in map) {
    const reg = new RegExp(`(${key})`);
    if (reg.test(format)) {
      const str = map[key].toString();
      format = format.replace(RegExp.$1, str.padStart(RegExp.$1.length, "0"));
    }
  }

  return format;
}

export function getCusProdRespCode(code) {
  switch (code) {
    case "0":
      return "不认可";
    case "1":
      return "认可";
    case "2":
      return "中立";
    default:
      return "";
  }
}
export function getAssisStatusName(code) {
  switch (code) {
    case "0":
      return "待协访";
    case "1":
      return "协防中";
    case "2":
      return "已完成";
    case "3":
      return "待评价";
    case "4":
      return "已拒绝";
    case "5":
      return "已评价";
    case "6":
      return "已失效";
    case "7":
      return "已取消";
    default:
      return "";
  }
}
export function getAssisTags(code) {
  if (!code) {
    return Rectangle505;
  }
  if (code === "1") return Rectangle505;

  if (code === "2") return Rectangle508;

  if (code === "3") return Rectangle506;

  if (code === "4") return Rectangle507;
  if (code === "5") return Rectangle509;
}

export function getAssisBackgroundColor(code) {
  if (code === "1") return "background-color-0";

  if (code === "2") return "background-color-2";

  if (code === "3") return "background-color-1";

  if (code === "4") return "background-color-4";
  if (code === "5") return "background-color-5";
}

export function getAssisColor(code) {
  switch (code) {
    case "0":
      return "color-0";
    case "1":
      return "color-2";
    case "5":
      return "color-1";
    case "4":
      return "color-3";
    default:
      return "color-4";
  }
}
export function getBase64(img) {
  console.log("imgh", img);
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.addEventListener("load", () => {
      resolve(reader.result);
    });
    reader.readAsDataURL(img);
  });
}

export function base64ToFile(base64, fileName) {
  const data = base64.split(",");
  const type = data[0].match(/:(.*?);/)[1];
  const suffix = type.split("/")[1];
  const bstr = window.atob(data[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) u8arr[n] = bstr.charCodeAt(n);

  const file = new File([u8arr], `${fileName}.${suffix}`, {
    type,
  });
  return file;
}

function getImageFileFromUrl(url, imageName) {
  return new Promise((resolve, reject) => {
    var blob = null;
    var xhr = new XMLHttpRequest();
    xhr.open("GET", url);
    xhr.setRequestHeader("Accept", "image/png");
    xhr.responseType = "blob";
    // 加载时处理
    xhr.onload = () => {
      // 获取返回结果
      blob = xhr.response;
      let imgFile = new File([blob], imageName, { type: "image/png" });
      // 返回结果
      resolve(imgFile);
    };
    xhr.onerror = (e) => {
      reject(e);
    };
    // 发送
    xhr.send();
  });
}

export function location() {
  let lat = "";
  let lon = "";
  if ("geolocation" in navigator) {
    navigator.geolocation.getCurrentPosition(
      function (position) {
        const latitude = position.coords.latitude; // 纬度
        const longitude = position.coords.longitude; // 经度
        lat = latitude;
        lon = longitude;
      },
      function (error) {
        Taro.showToast({
          title: "定位失败",
          icon: "none",
          duration: 2000,
        });
      },
      {
        // enableHighAccuracy: true,
        // timeout: 1000,
        // maximumAge: 0,
      }
    );
  } else {
    Taro.showToast({
      title: "请开启定位权限",
      icon: "none",
      duration: 2000,
    });
  }
  // Taro.getLocation({
  //   type: "gcj02",
  //   success: function (res) {
  //     console.log("resssssss", res);
  //     lat = res.latitude;
  //     lon = res.longitude;
  //   },
  //   fail: function (res) {
  //     Taro.showToast({
  //       title: "定位失败",
  //       icon: "none",
  //       duration: 2000,
  //     });
  //   },
  // });
  return lat, lon;
}

export const titleStyle = (title) => {
  var obj = {};
  switch (title) {
    case "副主任医师": {
      obj.background = "background: #E8F0FF;";
      obj.fontColor = "color: #2551F2;";
      break;
    }
    case "住院医师": {
      obj.background = "background: #E6F8F2;";
      obj.fontColor = "color: #00B578;";
      break;
    }
    case "主治医师": {
      obj.background = "background: #FFF7EA;";
      obj.fontColor = "color: #FFB42F;";
      break;
    }
    default: {
      obj.background = "background: #E8F0FF;";
      obj.fontColor = "color: #2551F2;";
      break;
    }
  }
  return obj;
};


export const tenantId = process.env.TARO_APP_TENANT_ID;
export const baseApi = process.env.TARO_APP_BASE_API;

export const isAis =  window.location.hostname.includes("athena-ais");

export const appCode = () => isAis ? process.env.TARO_APP048_CODE : process.env.TARO_APP_CODE;

export const appId = isAis ? process.env.TARO_APP_APP048_ID : process.env.TARO_APP_APP_ID

export const fsUrl = isAis ? process.env.TARO_APP_FS048_URL : process.env.TARO_APP_FS_URL

export const h5Url = isAis ? process.env.TARO_APP_H5048_URL : process.env.TARO_APP_H5_URL

export const pcUrl = isAis ? process.env.TARO_APP_PC048_URL : process.env.TARO_APP_PC_URL

export const assistUrl = isAis ? process.env.TARO_APP_H5048_ASSIST_URL : process.env.TARO_APP_H5_ASSIST_URL