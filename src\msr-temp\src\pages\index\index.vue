<template>
  <view class="crmHomePage">
    <view class="header"> </view>
    <view class="head">
      <view v-if="initUserInfo.avatar"
        ><img :src="avatar" style="width: 48px; height: 48px; max-width: 50px"
      /></view>
      <view class="nameAvatar" v-else>{{
        initUserInfo.nickName.slice(-2)
      }}</view>
      <view class="nameBox">
        <view class="name"
          >{{ initUserInfo.nickName }}({{ initUserInfo.userName }})</view
        >
        <view class="position">{{ postNameList() }}</view>
      </view>
      <div class="text-guiji" @click="pathToTrajectory()"></div>
    </view>

    <!-- 主要功能 -->
    <view class="mainIcons">
      <div
        v-for="(item, index) in mainIconList.filter((item) => item.flag)"
        :key="item.text"
        @click="goto(item)"
        class="mainIconItem"
        :style="{ backgroundColor: item.backgroundColor }"
      >
        <view style="display: flex; align-items: center; width: 100%">
          <view class="mainIconItem_icon">
            <IconFont :name="item.icon" style=""></IconFont>
          </view>
          <view class="mainIconItem_text">{{ item.text }}</view>
        </view>
        <view style="margin-right: 12px">
          <IconFont
            name="rect-right"
            style="vertical-align: inherit"
            color="#C6CAD1"
            size="14px"
          ></IconFont
        ></view>
      </div>
    </view>
    <!--  院外关联渠道  -->
    <view
      class="mainIcons"
      style="padding-top: 0"
      v-if="props.appcode === 'pcb'"
    >
      <div
        class="mainIconItem"
        :style="{ backgroundColor: '#EEF3FF' }"
        @click="handleNavigateExternal()"
      >
        <view style="display: flex; align-items: center; width: 100%">
          <view class="mainIconItem_icon">
            <IconFont :name="area" style=""></IconFont>
          </view>
          <view class="mainIconItem_text">院外关联渠道</view>
        </view>
        <view style="margin-right: 12px">
          <IconFont
            name="rect-right"
            style="vertical-align: inherit"
            color="#C6CAD1"
            size="14px"
          ></IconFont
        ></view>
      </div>
    </view>
    <!-- 关联药店 -->
    <view
      class="associationEntrance"
      v-if="
        currentIconList.some((item) => item.text == '关联药店' && item.flag)
      "
      @click="
        goto({
          icon: association,
          text: '关联药店',
          to: '/pages/association/index',
          flag: true,
        })
      "
    >
      <IconFont :name="association" style="height: 16px"></IconFont>
      <IconFont name="rect-right" color="#C6CAD1" size="14px"></IconFont>
    </view>
    <!-- 数据管理 -->
    <view class="subIcons">
      <view class="logo">
        <IconFont :name="data_title" style="height: 36px"></IconFont>
      </view>
      <div>
        <image
          :src="LineStroke"
          style="width: 5px; height: 38px; margin: 0 4px 0 0"
        ></image>
      </div>
      <view class="subIconsItemList">
        <div
          v-for="(item, index) in dataManageList.filter((item) => item.flag)"
          :key="item.text"
          @click="goto(item)"
          class="subIconItem"
        >
          <view class="subIconItem_icon">
            <IconFont
              :name="item.icon"
              style="height: 20px; width: 20px"
            ></IconFont>
          </view>
          <view class="subIconItem_text">{{ item.text }}</view>
        </div>
      </view>
    </view>
    <!-- 日程活动 -->
    <view class="subIcons">
      <view class="logo">
        <IconFont :name="event_title" style="height: 36px"></IconFont>
      </view>
      <div>
        <image
          :src="LineStroke"
          style="width: 5px; height: 38px; margin: 0 4px 0 0"
        ></image>
      </div>
      <view class="subIconsItemList">
        <div
          v-for="(item, index) in eventManageList.filter((item) => item.flag)"
          :key="item.text"
          @click="goto(item)"
          class="subIconItem"
        >
          <view class="subIconItem_icon">
            <IconFont
              :name="item.icon"
              style="height: 20px; width: 20px"
            ></IconFont>
          </view>
          <view class="subIconItem_text">{{ item.text }}</view>
        </div>
      </view>
    </view>
    <!-- 其它工具 -->
    <view
      class="subIcons"
      v-if="othersManageList.filter((item) => item.flag).length"
    >
      <view class="logo">
        <IconFont :name="others_title" style="height: 36px"></IconFont>
      </view>
      <div>
        <image
          :src="LineStroke"
          style="width: 5px; height: 38px; margin: 0 4px 0 0"
        ></image>
      </div>
      <view class="subIconsItemList">
        <div
          v-for="(item, index) in othersManageList.filter((item) => item.flag)"
          :key="item.text"
          @click="goto(item)"
          class="subIconItem"
        >
          <view class="subIconItem_icon">
            <IconFont
              :name="item.icon"
              style="height: 20px; width: 20px"
            ></IconFont>
          </view>
          <view class="subIconItem_text">{{ item.text }}</view>
        </div>
      </view>
    </view>

    <!-- 以下为旧版首页 -->
    <!-- <view class="icons">
      <nut-grid direction="horizontal" :column-num="iconList.length > 2 ? 2 : iconList.length" :border="false"
        :gutter="16">
        <nut-grid-item v-for="(item, index) in iconList.filter((item) => item.flag).filter(item => empNoFilter(item))"
          :key="item.text" :text="item.text" @click="goto(item)" :style="{
            // borderLeft: index % 3 === 0 ? none : '1px solid #f5f5f5',
            backgroundColor: '#EEF3FF',
            borderRadius: '8px',
          }">
          <view style="display: flex; flex-direction: row">
            <IconFont :name="item.icon"></IconFont>
          </view>
        </nut-grid-item>
      </nut-grid>
    </view> -->
  </view>
</template>

<script setup>
import Taro, { useDidShow } from "@tarojs/taro";
import { IconFont } from "@nutui/icons-vue-taro";
import { reactive, onMounted, ref } from "vue";
import inst_cust from "../../images/inst_cust_v2.png";
import approval from "../../images/approval.png";
import inst_visit from "../../images/inst_visit_v2.png";
import inst_assist from "../../images/inst_assist_v2.png";
import area from "../../images/area.png";
import wenjuan from "../../images/wenjuan-icon.png";
import shangji from "../../images/shangji.png";
import insSign from "../../images/ins-sign.png";
import association from "../../images/association.png";
import target from "../../images/target.png";
import pharmacy from "../../images/pharmacy.png";
import ins from "../../images/ins_icon.png";
import client from "../../images/client.png";
import evets from "../../images/evets.png";
import speaker from "../../images/speaker.png";
import speaker_line from "../../images/speaker_line.png";
import calendar_index_icon from "../../images/calendar_index_icon.png";
import arrow from "../../images/arrow.png";
import data_title from "../../images/data_title.png";
import LineStroke from "../../images/LineStroke.png";
import event_title from "../../images/event_title.png";
import others_title from "../../images/others_title.png";

import rizhanbao from "../../images/rizhanbao.png";
import jigouzhunru from "../../images/hcd-icon.png";
import jisuanqi from "../../images/jisuanqi.png";
import outside from "../../images/outside.png";
import httpRequest from "@/servers/http";
import { sysUserInsApi, sysUserSpeakerApi } from "../../api/speaker";
import { checkDept } from "../institutionalVisits048/api.js";
import { appCode, baseApi, appId } from "../../utils/content";

const props = defineProps({
  appcode: {
    type: String,
    default: "",
  },
});

const pathToTrajectoryShow = ["gen", "ade"];
const initUserInfo = Taro.getStorageSync("initUserInfo").user;
const initHrInfo = Taro.getStorageSync("initHrInfo");

const urlState = ref({
  url:
    process.env.NODE_ENV === "production"
      ? "https://sales-networkreport.nbims.com.cn"
      : "https://sales-networkreport-test.nbims.com.cn",
  api: "https://sales-estimate.nbims.com.cn",
  BIUrl:
    process.env.NODE_ENV === "production"
      ? "https://gscdn.genscigroup.com/AppAccessReportingWeb/PRO/latest/#/home"
      : "http://10.230.77.17/AppAccessReportingWeb/FAT/latest/#/home?code=2",
  questionUrl:
    process.env.NODE_ENV === "production"
      ? "https://gscdn.genscigroup.com/AppPortalH5Web/PRO/latest/index.html#/?code=star_new"
      : "http://10.230.77.18/AppPortalBuilderWeb/FAT/latest/index.html#/product?code=star_new",
  eventsUrl:
    process.env.NODE_ENV === "production"
      ? "https://cm-events.dgtmeta.com/"
      : "https://cm-events.dgtmeta.com/",
});

const state = reactive({
  activekeys: "1",
  stats: {},
  userSpeakerObj: {},
  userInsObj: {},
});
let mainIconList = [
  {
    icon: area,
    text: "我的辖区",
    // to: "/pages/area/index",
    flag: true,
    backgroundColor: "#EEF3FF",
  },
  {
    icon: target,
    text: "推广有效性",
    // to: "/pages/target/index",
    flag: initHrInfo.identity !== "resp",
    backgroundColor: "#E9F6FF",
  },
  {
    icon: approval,
    text: "全部审批",
    // to: "",
    flag: true,
    backgroundColor: "#FFF9EB",
  },
  {
    icon: speaker,
    text: "讲者协议",
    // to: "",
    flag: true,
    backgroundColor: "#FFF6FE",
  },
];
let dataManageList = [
  // TODO:机构、客户、讲者，跳转到哪待确认
  {
    icon: ins,
    text: "机构",
    // to: "/pages/ins_manage/index",
    flag: true,
  },
  {
    icon: client,
    text: "客户",
    // to: "/pages/customer_manage/index",
    flag: true,
  },
  {
    icon: pharmacy,
    text: "药店",
    // to: "/pages/pharmacy/index",
    flag: true,
  },
  {
    icon: speaker_line,
    text: "讲者",
    // to: "/pages/speaker/index",
    flag: true,
  },
];
let eventManageList = [
  // TODO:金果日历
  {
    icon: calendar_index_icon,
    text: "金果日历",
    // to: "",
    flag: true,
  },
  {
    icon: inst_visit,
    text: "客户拜访",
    // to: "/pages/institutionalVisits/index",
    flag: true,
  },
  {
    icon: inst_visit,
    text: "客户拜访",
    // to: "/pages/institutionalVisits/index",
    flag: true,
  },
  {
    icon: inst_assist,
    text: "客户协访",
    // to: "/pages/collaborativevisit/index",
    flag: true,
  },
  {
    icon: insSign,
    text: "机构签到",
    // to: "pages/insSign/index",
    flag: true,
  },
  // {
  //   icon: outside,
  //   text: "区域外时间",
  //   flag: true,
  // },
];
let othersManageList = [
  // 日战报、商机管理、巡星填报
  {
    icon: rizhanbao,
    text: "日战报",
    // to: "/pages/collaborativevisit/index",
    flag: true,
  },
  {
    icon: shangji,
    text: "商机管理",
    // to: "https://bjscklkstx.feishuapp.cn/ae/apps/package_5eaa76__c/aadgxgbjjdajy",
    flag: true,
  },
  {
    icon: wenjuan,
    text: "巡星填报",
    // to: "/pages/collaborativevisit/index",
    flag: true,
  },
  {
    icon: evets,
    text: "活动管理",
    flag: true,
  },
  {
    icon: jisuanqi,
    text: "利润测算",
    flag: props.appcode === "gen",
  },
];

const iconList = ref([
  [
    {
      icon: area,
      text: "我的辖区",
      to: "/pages/area/index",
      flag: true,
    },

    {
      icon: pharmacy,
      text: "药店",
      to: "/pages/pharmacy/index",
      flag: true,
    },
    {
      icon: association,
      text: "关联药店",
      to: "/pages/association/index",
      flag: true,
    },
    {
      icon: target,
      text: "推广有效性",
      to: "/pages/target/index",
      flag: true,
    },
    {
      icon: rizhanbao,
      text: "日战报",
      url: `https://sales-networkreport-test.nbims.com.cn/category/reproduction/reproductionWeekReportUpload/dataBoard?token=eyJhbGciOiJIUzUxMiJ9.eyJlbXBObyI6IkdTNDgyOSIsImV4cCI6MTcwMDg4MTM5OX0.Yes8nYeCY42RcAVeSCYXLWvTSWm4Cw4HAS5bkxbqSgz3O978t98i5bjyI9HZwhf6BIdAXtuSEhUD8UHe5SqA0Q`,
      flag: true,
    },
    {
      icon: insSign,
      text: "机构签到",
      to: "pages/insSign/index",
      flag: true,
    },
    {
      icon: inst_visit,
      text: "客户拜访",
      to: "/pages/institutionalVisits/index",
      flag: true,
    },
    {
      icon: inst_visit,
      text: "客户拜访",
      to: "/pages/institutionalVisits048/index",
      flag: true,
    },
    {
      icon: inst_assist,
      text: "客户协访",
      to: "/pages/collaborativevisit/index",
      flag: true,
    },
    {
      icon: wenjuan,
      text: "巡星填报",
      to: "/pages/collaborativevisit/index",
      flag: true,
    },
    {
      icon: shangji,
      text: "商机管理",
      to: "https://bjscklkstx.feishuapp.cn/ae/apps/package_5eaa76__c/aadgxgbjjdajy",
      flag: true,
      empNoList: ["GS5738", "GS9256", "GS5996", "GS12545"],
    },
    {
      icon: evets,
      text: "活动管理",
      to: "",
      flag: true,
    },
  ],
]);
const iconList2 = ref([
  {
    icon: area,
    text: "我的辖区",
    to: "/pages/area/index",
    flag: true,
  },
  {
    icon: client,
    text: "客户",
    to: "/pages/customer_manage/index",
    flag: true,
  },
  {
    icon: ins,
    text: "机构",
    to: "/pages/ins_manage/index",
    flag: false,
  },
  {
    icon: client,
    text: "客户",
    to: "/pages/customer_manage/index",
    flag: true,
  },

  {
    icon: insSign,
    text: "机构签到",
    to: "pages/insSignSkn/index",
    flag: true,
  },
  {
    icon: inst_visit,
    text: "客户拜访",
    to: "/pages/institutionalVisits/index",
    flag: true,
  },

  {
    icon: wenjuan,
    text: "巡星填报",
    to: "/pages/collaborativevisit/index",
    flag: true,
  },

  {
    icon: evets,
    text: "活动管理",
    to: "",
    flag: true,
  },
]);
const iconList3 = ref([
  {
    icon: area,
    text: "我的辖区",
    to: "/pages/area/index",
    flag: true,
  },
  {
    icon: ins,
    text: "机构",
    to: "/pages/ins_manage/index",
    flag: true,
  },
  {
    icon: client,
    text: "客户",
    to: "/pages/customer_manage/index",
    flag: true,
  },
  {
    icon: pharmacy,
    text: "药店",
    to: "/pages/pharmacy/index",
    flag: true,
  },
  {
    icon: association,
    text: "关联药店",
    to: "/pages/association/index",
    flag: true,
  },
  {
    icon: target,
    text: "推广有效性",
    to: "/pages/target/index",
    flag: initHrInfo.identity !== "resp",
  },
  {
    icon: rizhanbao,
    text: "日战报",
    url: `https://sales-networkreport-test.nbims.com.cn/category/reproduction/reproductionWeekReportUpload/dataBoard?token=eyJhbGciOiJIUzUxMiJ9.eyJlbXBObyI6IkdTNDgyOSIsImV4cCI6MTcwMDg4MTM5OX0.Yes8nYeCY42RcAVeSCYXLWvTSWm4Cw4HAS5bkxbqSgz3O978t98i5bjyI9HZwhf6BIdAXtuSEhUD8UHe5SqA0Q`,
    flag: true,
  },
  {
    icon: insSign,
    text: "机构签到",
    to: "pages/insSign/index",
    flag: false,
  },
  {
    icon: inst_visit,
    text: "客户拜访",
    to: "/pages/institutionalVisits/index",
    flag: true,
  },
  {
    icon: inst_assist,
    text: "客户协访",
    to: "/pages/collaborativevisit/index",
    flag: true,
  },
  {
    icon: wenjuan,
    text: "巡星填报",
    to: "/pages/collaborativevisit/index",
    flag: true,
  },
  {
    icon: shangji,
    text: "商机管理",
    to: "https://bjscklkstx.feishuapp.cn/ae/apps/package_5eaa76__c/aadgxgbjjdajy",
    flag: true,
    empNoList: ["GS13521", "GS5415", "GS5738", "GS9955", "GS13521"],
  },
  {
    icon: evets,
    text: "活动管理",
    to: "",
    flag: false,
  },
  {
    icon: jisuanqi,
    text: "利润测算",
    flag: props.appcode === "gen",
  },
]);

const iconList5_ade = ref([
  {
    icon: area,
    text: "我的辖区",
    to: "/pages/area/index",
    flag: true,
  },
  {
    icon: ins,
    text: "机构",
    to: "/pages/ins_manage/index",
    flag: true,
  },
  {
    icon: client,
    text: "客户",
    to: "/pages/customer_manage/index",
    flag: true,
  },
  {
    icon: pharmacy,
    text: "药店",
    to: "/pages/pharmacy/index",
    flag: true,
  },
  {
    icon: association,
    text: "关联药店",
    to: "/pages/association/index",
    flag: true,
  },
  {
    icon: rizhanbao,
    text: "日战报",
    url: `https://sales-networkreport-test.nbims.com.cn/category/reproduction/reproductionWeekReportUpload/dataBoard?token=eyJhbGciOiJIUzUxMiJ9.eyJlbXBObyI6IkdTNDgyOSIsImV4cCI6MTcwMDg4MTM5OX0.Yes8nYeCY42RcAVeSCYXLWvTSWm4Cw4HAS5bkxbqSgz3O978t98i5bjyI9HZwhf6BIdAXtuSEhUD8UHe5SqA0Q`,
    flag: true,
  },
  {
    icon: insSign,
    text: "机构签到",
    to: "pages/insSign/index",
    flag: true,
  },
  // {
  //   icon: inst_visit,
  //   text: "客户拜访",
  //   to: "/pages/institutionalVisits/index",
  //   flag: true,
  // },
  // {
  //   icon: inst_visit,
  //   text: "客户拜访",
  //   to: "/pages/institutionalVisits048/index",
  //   flag: true,
  // },
  {
    icon: inst_assist,
    text: "客户协访",
    to: "/pages/collaborativevisit/index",
    flag: true,
  },
  {
    icon: evets,
    text: "活动管理",
    to: "",
    flag: false,
  },
]);

const iconList4 = ref([
  {
    icon: area,
    text: "我的辖区",
    to: "/pages/area/index",
    flag: true,
  },
  {
    icon: ins,
    text: "机构",
    to: "/pages/ins_manage/index",
    flag: true,
  },
  {
    icon: client,
    text: "客户",
    to: "/pages/customer_manage/index",
    flag: true,
  },
  {
    icon: pharmacy,
    text: "药店",
    to: "/pages/pharmacy/index",
    flag: true,
  },
  {
    icon: association,
    text: "关联药店",
    to: "/pages/association/index",
    flag: true,
  },

  {
    icon: insSign,
    text: "机构签到",
    to: "pages/insSign/index",
    flag: true,
  },
  {
    icon: inst_visit,
    text: "客户拜访",
    to: "/pages/institutionalVisits/index",
    flag: true,
  },
  {
    icon: inst_assist,
    text: "客户协访",
    to: "/pages/collaborativevisit/index",
    flag: true,
  },
  {
    icon: wenjuan,
    text: "巡星填报",
    to: "/pages/collaborativevisit/index",
    flag: true,
  },
  {
    icon: outside,
    text: "区域外时间",
    to: "/pages/outside/index",
    flag: true,
  },
]);

const iconList_pcb = ref([
  {
    icon: area,
    text: "我的辖区",
    to: "/pages/area/index",
    flag: true,
  },
  {
    icon: area,
    text: "院外关联渠道",
    to: "/pages/area2/index",
    flag: true,
  },
  {
    icon: ins,
    text: "机构",
    to: "/pages/ins_manage/index",
    flag: true,
  },
  {
    icon: client,
    text: "客户",
    to: "/pages/customer_manage/index",
    flag: true,
  },
  {
    icon: pharmacy,
    text: "药店",
    to: "/pages/pharmacy/index",
    flag: true,
  },
  {
    icon: association,
    text: "关联药店",
    to: "/pages/association/index",
    flag: true,
  },
  {
    icon: insSign,
    text: "机构签到",
    to: "pages/insSign/index",
    flag: true,
  },
  {
    icon: inst_visit,
    text: "客户拜访",
    to: "/pages/institutionalVisits/index",
    flag: true,
  },
  {
    icon: inst_assist,
    text: "客户协访",
    to: "/pages/collaborativevisit/index",
    flag: true,
  },
]);

const token = ref("");
const BIToken = ref("");

const goto = (item) => {
  if (item.text === "日战报") {
    if (!token.value) {
      Taro.showToast({
        title: "token转换失败",
      });
      return false;
    }
    if (props.appcode === "ade") {
      window.location.href = `${urlState.value.url}/category/neuroendocrine/neuroendocrineDayReportUpload/dataBoard?token=${token.value}`;
    } else if (props.appcode === "gen") {
      window.location.href = `${urlState.value.url}/category/reproduction/reproductionWeekReportUpload/dataBoard?token=${token.value}`;
    } else {
      window.location.href = `${urlState.value.url}/category/reproduction/reproductionWeekReportUpload/dataBoard?token=${token.value}`;
    }
  } else if (item.text === "机构准入") {
    if (!token.value) {
      Taro.showToast({
        title: "token转换失败",
      });
      return false;
    }
    window.location.href = `${urlState.value.BIUrl}?token=${BIToken.value}`;
  } else if (item.text === "巡星填报") {
    window.location.href = `${urlState.value.questionUrl}`;
  } else if (item.text === "商机管理") {
    window.location.href = `https://bjscklkstx.feishuapp.cn/ae/apps/package_5eaa76__c/aadgxgbjjdajy`;
  } else if (item.text === "利润测算") {
    window.location.href = `https://bjscklkstx.feishuapp.cn/ae/apps/v3/package_9accf9__c/mobile/aadh3eslx3yns?lk_meta=%7B%22page-meta%22%3A%7B%22showBottomNavBar%22%3A%22false%22%2C%22showNavBar%22%3A%22false%22%7D%7D`;
  } else if (item.text === "活动管理") {
    window.location.href = `${urlState.value.eventsUrl}`;
  } else if (item.text === "讲者") {
    handleNavigateSpeaker();
  } else {
    Taro.navigateTo({
      url: item.to,
    });
  }
};

const postNameList = () => {
  if (initHrInfo && initHrInfo.orgList && initHrInfo.orgList.length) {
    const list = initHrInfo.orgList.filter((item) => item.postType === "1");
    return list.map((item) => item.name).join(" | ");
  } else {
    return "";
  }
};

onMounted(() => {
  filterList();
  getBIToken();
  getToken();
  getSysUserSpeakerApi();
  getSysUserIns();
  if (props.appcode === "ade") {
    const deptCode = initHrInfo?.orgList?.[0]?.deptCode;
    if (deptCode) {
      checkDept(deptCode)
        .then((res) => {
          const isDeptEnabled = res.data;
          // 将结果存储到 sessionStorage
          sessionStorage.setItem(`048`, isDeptEnabled ? "1" : "0");
          addCustomerVisitItem(isDeptEnabled);
        })
        .catch(() => {
          // 出错时默认使用旧版本，并存储为 false
          sessionStorage.setItem(`048`, "0");
          addCustomerVisitItem(false);
        });
    }
  }
});

// 添加客户拜访项目的辅助函数
const addCustomerVisitItem = (isDeptEnabled) => {
  if (isDeptEnabled) {
    eventManageList.unshift({
      icon: inst_visit,
      text: "客户拜访",
      to: "/pages/institutionalVisits048/index",
      flag: true,
    });
    iconList5_ade.value.unshift({
      icon: inst_visit,
      text: "客户拜访",
      to: "/pages/institutionalVisits048/index",
      flag: true,
    });
  } else {
    eventManageList.unshift({
      icon: inst_visit,
      text: "客户拜访",
      to: "/pages/institutionalVisits/index",
      flag: true,
    });
    iconList5_ade.value.unshift({
      icon: inst_visit,
      text: "客户拜访",
      to: "/pages/institutionalVisits/index",
      flag: true,
    });
  }
};

const getToken = async () => {
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  const res = await httpRequest.post(
    `${urlState.value.api}/public/account/api/crm/public/code2token`,
    {
      empNo: initHrInfo.empCode,
    }
  );
  token.value = res?.data?.token;
};

const getBIToken = async () => {
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  const res = await httpRequest.post(
    `https://login-data.genscigroup.com/crm/generateToken`,
    {
      empNo: initHrInfo.empCode,
    }
  );
  BIToken.value = res?.data;
};
let currentIconList = [];
const filterList = () => {
  const iconListMap = {
    gen: iconList3.value,
    ade: iconList5_ade.value,
    skn: iconList2.value,
    pns: iconList4.value,
    ped: iconList5_ade.value,
    pcb: iconList_pcb.value,
  };
  // 根据appcode过滤出对应的iconList
  currentIconList = iconListMap[props.appcode] || iconList.value;
  // 筛选mainIconList等模块中，存在于currentIconList的icon,并用currentIconList中对应的item替换原本的to和flag属性
  mainIconList = mainIconList
    .map((item) => {
      const match = currentIconList.find(
        (current) => current.text === item.text
      );
      return match ? { ...item, to: match.to, flag: match.flag } : null;
    })
    .filter(Boolean);

  dataManageList = dataManageList
    .map((item) => {
      const match = currentIconList.find(
        (current) => current.text === item.text
      );
      return match ? { ...item, to: match.to, flag: match.flag } : null;
    })
    .filter(Boolean);

  eventManageList = eventManageList
    .map((item) => {
      const match = currentIconList.find(
        (current) => current.text === item.text
      );
      return match ? { ...item, to: match.to, flag: match.flag } : null;
    })
    .filter(Boolean);

  othersManageList = othersManageList
    .map((item) => {
      const match = currentIconList.find(
        (current) => current.text === item.text
      );
      return match ? { ...item, to: match.to, flag: match.flag } : null;
    })
    .filter(Boolean);

  // 以下为旧版首页逻辑
  // if (props.appcode === "gen") {
  //   iconList.value = iconList3.value;
  // }
  // if (props.appcode === "ade") {
  //   iconList.value = iconList5_ade.value;
  // }
  // if (props.appcode === "skn") {
  //   iconList.value = iconList2.value;
  // }
  // if (props.appcode === "pns") {
  //   iconList.value = iconList4.value;
  // }
};

const pathToTrajectory = () => {
  Taro.navigateTo({
    url: "/pages/trajectory/index",
  });
};

const handleNavigateSpeaker = () => {
  const access_token = Taro.getStorageSync("access_token");
  const url =
    process.env.NODE_ENV === "production"
      ? `https://athena-mds-mp.dgtmeta.com`
      : `https://athena-mds-mp.t.dgtmeta.com`;
  window.location.href = `${url}/pages/speaker/index?source=crm&token=${access_token}&appid=${appId}&originUrl=${window.location.origin}&code=${baseApi}&sourceBuName=${appCode}`;
};

const handleNavigateExternal = () => {
  const access_token = Taro.getStorageSync("access_token");
  const url =
    process.env.NODE_ENV === "production"
      ? `https://athena-mds-mp.dgtmeta.com`
      : `https://athena-mds-mp.t.dgtmeta.com`;
  window.location.href = `${url}/pages/external_affiliation_channels/index?source=crm&token=${access_token}&appid=${appId}&originUrl=${window.location.origin}&code=${baseApi}&sourceBuName=${appCode}`;
};

const getLRCSAuth = () => {
  // othersManageList.push({ icon: jisuanqi, text: "利润测算", flag: true })
  // iconList3.value.push({ icon: jisuanqi, text: "利润测算", flag: true })
  // iconList5_ade.value.push({ icon: jisuanqi, text: "利润测算", flag: true })
  // iconList2.value.push({ icon: jisuanqi, text: "利润测算", flag: true })
  // iconList4.value.push({ icon: jisuanqi, text: "利润测算", flag: true })
};

const getSysUserIns = async () => {
  const res = await sysUserInsApi();
  state.userInsObj = JSON.parse(res.msg);
  if (
    state.userInsObj.status === "1" ||
    state.userInsObj.code.includes(initUserInfo.userName)
  ) {
    othersManageList.push({ icon: jigouzhunru, text: "机构准入", flag: true });
    iconList3.value.push({ icon: jigouzhunru, text: "机构准入", flag: true });
    iconList5_ade.value.push({
      icon: jigouzhunru,
      text: "机构准入",
      flag: true,
    });
    iconList2.value.push({ icon: jigouzhunru, text: "机构准入", flag: true });
    iconList4.value.push({ icon: jigouzhunru, text: "机构准入", flag: true });
  } else {
    if (initHrInfo.identity !== "resp") {
      othersManageList.push({
        icon: jigouzhunru,
        text: "机构准入",
        flag: true,
      });
      iconList3.value.push({ icon: jigouzhunru, text: "机构准入", flag: true });
      iconList5_ade.value.push({
        icon: jigouzhunru,
        text: "机构准入",
        flag: true,
      });
      iconList2.value.push({ icon: jigouzhunru, text: "机构准入", flag: true });
      iconList4.value.push({ icon: jigouzhunru, text: "机构准入", flag: true });
    }
  }
};
const getSysUserSpeakerApi = async () => {
  const res = await sysUserSpeakerApi();
  state.userSpeakerObj = JSON.parse(res.msg);
  if (
    state.userSpeakerObj.status === "1" ||
    state.userSpeakerObj.code.includes(initUserInfo.userName)
  ) {
    dataManageList.push({
      icon: speaker_line,
      text: "讲者",
      flag: true,
    });
    iconList3.value.push({
      icon: speaker_line,
      text: "讲者",
      flag: true,
    });
    iconList5_ade.value.push({
      icon: speaker_line,
      text: "讲者",
      flag: true,
    });
    iconList2.value.push({
      icon: speaker_line,
      text: "讲者",
      flag: true,
    });
    iconList4.value.push({
      icon: speaker_line,
      text: "讲者",
      flag: true,
    });
  }
};
</script>

<style lang="scss">
.crmHomePage {
  background-color: #fff;
  height: 100vh;

  .header {
    width: 100vw;
    height: 6.5rem;
    background-image: url("../../images/bg_4.png");
    background-size: cover;
    // background-position: -108px -108px;
    background-repeat: no-repeat;
    // background-size: 128%;
  }

  .head {
    margin: -5.1rem 0 16px 0;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    padding: 16px 16px;
    align-items: center;
    justify-content: space-between;

    .text-guiji {
      height: 48px;
      width: 48px;
      flex: 0 0 48px;
      background-image: url("../../images/guiji_icon.png");
      background-size: cover;
    }

    .nameAvatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #92a8f8;
      color: #fff;
      line-height: 50px;
      text-align: center;
      min-width: 50px;
    }

    .nameBox {
      margin: 0 8px;
      // flex: 0 1 220px;
      overflow: hidden;

      .name {
        height: 34px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #1d212b;
        line-height: 24px;
        font-style: normal;
        text-transform: none;
        // word-break: break-all;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }

  .mainIcons {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 16px 16px 0 16px;

    .mainIconItem {
      width: 48%;
      height: 64px;
      border-radius: 8px;
      display: flex;
      // flex-direction: row;
      justify-content: space-between; // 改为左对齐
      // padding-left: 26%; // 添加左内边距来居中
      align-items: center;
      margin-bottom: 16px;

      // 当是最后一个元素且列表长度为奇数时撑满
      &:last-child:nth-child(odd) {
        width: 100%;
        // 且内容居左，如同一行两个时一样对齐
        justify-content: flex-start;
        .mainIconItem_icon {
          width: 40px;
          height: 40px;
          margin-left: 5%;
          display: flex; // 添加 flex 布局
          justify-content: center; // 水平居中
          align-items: center; // 垂直居中
        }
      }

      .mainIconItem_icon {
        width: 40px;
        height: 40px;
        margin-left: 10%;
        display: flex; // 添加 flex 布局
        justify-content: center; // 水平居中
        align-items: center; // 垂直居中
      }

      .mainIconItem_text {
        font-size: 14px;
        color: #1d212b;
        margin-left: 12px;
      }
    }
  }

  .associationEntrance {
    height: 50px;
    box-sizing: border-box;
    margin: 0 16px 0 16px;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    background-color: #f4f5f7;
    // 水平居中
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .subIcons {
    margin: 0 16px 0 16px;
    padding: 16px 0 16px 4px;
    border-radius: 8px;
    background-color: #fff;
    margin-bottom: 16px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;

    // 除最后一个以外，添加下边框
    &:not(:last-child) {
      border-bottom: 1px solid #f4f5f7;
    }

    .logo {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 16px;
    }

    .subIconsItemList {
      display: flex;
      flex-wrap: nowrap;
      flex-direction: row;
      width: 100%;

      .subIconItem {
        width: 25%;
        height: 46px;
        // 居中
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;

        // 除最后一个以外，添加右边框
        &:not(:last-child) {
          border-right: 1px solid #f4f5f7;
        }

        .subIconItem_icon {
          margin-top: -1px;
          margin-bottom: 6px;
          display: flex; // 添加 flex 布局
          justify-content: center; // 水平居中
          align-items: center; // 垂直居中
        }

        .subIconItem_text {
          font-size: 11px;
          color: #869199;
        }
      }
    }
  }

  .position {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #869199;
    line-height: 18px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    width: 180px;
  }

  .icons {
    background-color: #fff;
    margin: 0px 16px 16px 16px;
    padding-bottom: 16px;
    padding-top: 12px;
    border-radius: 8px;

    .statistics {
      width: 100%;
      height: 80px;
      border-radius: 8px;
      background: #f4f5f7;
      display: flex;
      align-items: center;
      justify-content: space-around;
    }
  }

  .nut-grid-item__content--surround {
    border-top-width: 0px;
  }

  .nut-grid-item__content {
    padding: 0px 8px;
  }

  .nut-grid-item__content--border {
    border-right-width: 0px;
    border-bottom-width: 0px;
  }

  .gapLine {
    width: 0px;
    height: 40px;
    border: 1px solid #f4f5f7;
  }

  .nut-grid-item__text {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    font-style: normal;
    text-align: center;
  }

  .nut-icon__img {
    width: 62px;
    height: 62px;
  }

  .nut-divider.nut-divider-vertical {
    margin: 0;
  }

  .nut-tab-pane {
    padding: 14px;
  }

  .nut-tabs {
    border-radius: 8px;
  }
}
</style>
