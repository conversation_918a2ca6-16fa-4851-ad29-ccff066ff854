<template>
  <div class="bg-white px-[16px]">

    <div class="text-[14px] text-[#2551F2]" style="padding: 8px 0;" v-if="formData.tips">注：<br /> {{ formData.tips }}
    </div>
    <div class="pt-[16px]">
      <div class="bg-white flex gap-[10px]">
        <div>
          <img :src="hospitalIcon" class="min-w-[48px] h-[48px]" />
        </div>
        <div>
          <div class="text-[#1D212B] text-[18px] leading-[21px] pb-[4px]">{{
            props.formData.dsName
          }}</div>
          <div class="text-[#869199] text-[14px]">{{
            props.formData.dsMdmCode
          }}</div>
        </div>
      </div>
      <div class="bg-white rounded-[8px] mx-[8px] mb-[16px]">
        <div>
          <div v-for="(spec, index) in props.formData.insDsList">
            <div class="flex items-top mb-[8px]" style="border-bottom: 1px dashed#E5E6EB;padding: 12px 0;">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">产品</div>
              <div class="text-[#1D212B] text-[14px] font-400 text-left">{{
                spec.productName
              }}</div>
            </div>
            <div class="flex items-top mb-[8px]" style="padding: 12px 0;">
              <div class="text-[#869199] text-[14px] font-400 w-[124px]">品规名称</div>
              <div class="text-[#1D212B] text-[14px] font-400 text-left">{{
                spec.specName
              }}</div>
            </div>
            <div class="flex items-top mb-[8px]" style="padding-bottom: 12px ;"
              :style="{ borderBottom: props.formData.insDsList.length - 1 === index ? 'none' : '1px solid #E5E6EB' }">
              <div class=" text-[#869199] text-[14px] font-400 w-[124px]">操作</div>
              <div class="text-[#1D212B] text-[14px] font-400 text-left"
                v-if="props.formType === 'gs_ins_ds_spec_shelves_apply'">{{
                  spec.shelves
                }}</div>
              <div v-if="props.formType === 'gs_ins_ds_spec_unbinding_apply'"
                class="text-[#1D212B] text-[14px] font-400 text-left">
                解绑
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import hospitalIcon from "@/bpm/src/assets/images/hospital-icon.png";

const router = useRouter();
const query = router.params;

const props = defineProps({
  formData: {
    type: Object,
    default: {},
  },
  formType: {
    type: String,
    default: '',

  }
});
const options = ref([]);
const state = reactive({
  info: {},
  specList: [],
});


onMounted(() => {
  props.formData = props.formData || {};
  console.log("props.formData", props.formData)
});
</script>

<style lang="scss"></style>
