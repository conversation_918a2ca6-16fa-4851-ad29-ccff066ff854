<template>
  <view>
    <view class="photograph">
      <view style="padding: 16px 0; min-width: 63px; color: #869199">拍照</view>
      <view class="photo-list">
        <view
          class="photo"
          v-for="(photo, index) in [...state.detailImageList, ...photoList]"
          :key="photo.ossId"
        >
          <img
            :src="photo.url"
            alt=""
            style="width: 80px; height: 80px"
            @click="lookImage(index)"
          />
          <view
            class="del"
            @tap="delPhoto(photo.url)"
            v-if="photo.type !== 'in'"
            >X</view
          >
        </view>
        <view @tap="takePhoto()" class="photo" v-if="props.iconFlag">
          <img :src="uploaderImg" alt="" style="width: 80px; height: 80px" />
        </view>
      </view>
    </view>
    <nut-image-preview
      :show="state.showPreview"
      :images="state.imgData"
      :init-no="initNo"
      @close="state.showPreview = false"
      style="width: 100%; height: 100%"
    />
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  computed,
  watch,
  onMounted,
} from "vue";
import uploaderImg from "../../../images/uploader.png";
import { imageUrl } from "../../../api/institutionalVisitsApi";
import { apiAuth } from "../../../utils/feishuAuth";
import { baseApi } from "../../../utils/content";
const initUserInfo = Taro.getStorageSync("initUserInfo").user;
const props = defineProps({
  iconFlag: {
    type: Boolean,
    default: true,
  },

  status: {
    type: String,
    default: "",
  },
  params: {
    type: Object,
    default: {},
  },
  type: {
    type: String,
    default: "",
  },
});

const photoList = ref([]);
const initNo = ref(0);
const emit = defineEmits(["update-fileList"]);
const TokenKey = Taro.getStorageSync("access_token");
const state = reactive({
  detailImageList: [],
  imgData: [],
});
const list = computed(() => []);
const lookImage = (i) => {
  console.log("查看", i);
  initNo.value = i;
  state.showPreview = true;
};

const delPhoto = (u) => {
  console.log(u);
  photoList.value = photoList.value.filter((item) => item.url !== u);
  console.log(photoList.value);
  state.imgData = [...state.detailImageList, ...photoList.value].map((item) => {
    return {
      src: item.url,
    };
  });
};
const chooseImage = () =>
  new Promise((res, rej) => {
    try {
      tt.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (data) => {
          console.log(data, "--data");
          const fileSystemManager = tt.getFileSystemManager();
          fileSystemManager.readFile({
            filePath: data.tempFilePaths?.[0],
            encoding: "base64",
            success(res_v1) {
              const imageType = "image/png";
              const base64ImageURL = `data:${imageType};base64,${res_v1?.data}`;
              res({ url: base64ImageURL, type: "FS" });
            },
            fail(res) {
              console.log(`readFile fail: ${JSON.stringify(res)}`);
            },
          });
        },
        fail: (err) => rej(err),
      });
    } catch (err) {
      Taro.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (data) => {
          console.log(data, "---dddd");
          res({ url: data?.tempFilePaths?.[0], type: "H5" });
        },
        fail: (err) => rej(err),
      });
    }
  });
const takePhoto = async () => {
  try {
    const { url, type } = await chooseImage();
    Taro.showLoading({
      mask: true,
      title: "上传中",
    });
    addWatermark(url, type).then((files) => {
      Taro.uploadFile({
        url: process.env.TARO_APP_API + `${baseApi}oss-file/upload`,
        header: {
          clientid: process.env.TARO_APP_CLIENT_ID,
          Authorization: `Bearer ${TokenKey}`,
        },
        filePath: files,
        name: "file",
        fail: () => {
          Taro.hideLoading();
          console.log("上传失败");
        },
        success: (result) => {
          Taro.hideLoading();
          try {
            photoList.value.push(JSON.parse(result.data).data);
            state.imgData = [...state.detailImageList, ...photoList.value].map(
              (item) => {
                return {
                  src: item.url,
                };
              }
            );
          } catch (e) {
            console.log(e);
          }
        },
      });
    });
  } catch (err) {}
};
const addWatermark = (imageSrc, type = "") => {
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      canvas.width = image.width;
      canvas.height = image.height;
      ctx.drawImage(image, 0, 0);
      ctx.font = type == "FS" ? "30px Arial" : "80px Arial";
      ctx.fillStyle = "white";
      ctx.fillText(dayTime().Y, canvas.width - (type == "FS" ? 200 : 500), 200);
      ctx.fillText(dayTime().H, canvas.width - (type == "FS" ? 200 : 500), 300);
      try {
        if (initUserInfo?.nickName && initUserInfo?.userName)
          ctx.fillText(
            initUserInfo.nickName + "-" + initUserInfo.userName,
            20,
            canvas.height - 300
          );
        if (props.params?.signInAddress && props.type === "signIn") {
          ctx.fillText(props.params.signInAddress, 20, canvas.height - 200);
        } else {
          ctx.fillText(props.params.signOutAddress, 20, canvas.height - 200);
        }
      } catch (err) {}
      canvas.toBlob(
        function (newBlob) {
          resolve(URL.createObjectURL(newBlob));
        },
        "image/jpeg",
        0.5
      );
    };
    image.onerror = reject;
    image.src = imageSrc;
  });
};

const dayTime = () => {
  const now = new Date();

  // 格式化日期和时间
  const year = now.getFullYear(); // 年
  const month = now.getMonth() + 1; // 月，getMonth() 返回的月份是从 0 开始的
  const day = now.getDate(); // 日
  const hours = now.getHours(); // 时
  const minutes = now.getMinutes(); // 分
  const seconds = now.getSeconds(); // 秒

  // 将月、日、时、分、秒转换为两位数格式
  const formattedMonth = month < 10 ? `0${month}` : month;
  const formattedDay = day < 10 ? `0${day}` : day;
  const formattedHours = hours < 10 ? `0${hours}` : hours;
  const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;

  // 组合成 "年-月-日 时：分：秒" 格式的字符串
  return {
    Y: `${year}/${formattedMonth}/${formattedDay}`,
    H: `${formattedHours}:${formattedMinutes}`,
  };
};
const setPhotoList = () => {
  photoList.value = [];
  state.detailImageList = [];
  initNo.value = 0;
  state.imgData = [];
};

const viewList = (list1, list2) => {
  console.log("33333", list1, list2);
  [...list1, ...list2].forEach(async (item) => {
    const res = await imageUrl(item);
    res.data.rows[0].type = "in";
    state.detailImageList.push(res.data.rows[0]);
    state.imgData.push({ src: res.data.rows[0].url });
  });
};
defineExpose({
  photoList,
  setPhotoList,
  viewList,
});
onMounted(() => {
  apiAuth();
});
</script>
<style lang="scss" scoped>
.photograph {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 8px;
  .photo-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    .photo {
      width: 80px;
      height: 80px;
      position: relative;
      overflow: hidden;
      .del {
        position: absolute;
        font-size: 12px;
        right: -12px;
        top: -10px;
        width: 24px;
        height: 24px;
        background: black;
        border-radius: 50%;
        text-align: center;
        line-height: 24px;
        color: #fff;
        padding-top: 7px;
        padding-right: 7px;
      }
      img {
        width: 100%;
      }
    }
  }
}
</style>
