<template>
  <view class="speakerDetail bg-[#EDEFF5]">
  
    <view class="bg-white p-[16px] flex gap-[10px]">
      <view>
        <img :src="hospitalIcon" class="min-w-[48px] h-[48px]" />
      </view>
      <view>
        <view class="text-[#1D212B] text-[18px] leading-[21px] pb-[4px]">{{
          props.formData.insName
        }}</view>
        <view class="text-[#869199] text-[14px]">{{
          props.formData.insMdmCode
        }}</view>
      </view>
    </view>
    <view class="bg-white px-[16px] pb-[16px]">
      <view class="text-[#4E595E] text-[14px] pb-[12px]"
        >已关联({{ props.formData.insDsList?.length || "0" }})</view
      >
      <view
        v-for="item in props.formData.insDsList || []"
        class="px-[16px] py-[8px] bg-[#F2F6FF] rounded-[4px] mb-[12px]"
        style="border: 1px solid #92a8f8"
      >
        <view class="text-[#2551F2] text-[16px] pb-[4px]">{{
          item.dsName
        }}</view>
        <view class="text-[#869199] text-[12px]"
          >药店编码：{{ item.dsMdmCode }}</view
        >
      </view>
    </view>
  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref, computed } from "vue";
import hospitalIcon from "@/bpm-temp/src/assets/images/hospital-icon.png";
const router = useRouter();
const query = router.params;
const props = defineProps({
  formData: {
    type: Object,
    default: {},
  },
});


</script>

<style lang="scss">

</style>
