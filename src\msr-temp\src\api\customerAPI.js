// 肿瘤机构和客户API
import httpRequest from "@/servers/http";

//标准科室
export const deptNameList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=dept_name`);
};
//科室标签
export const departmentLabelList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=department_label`);
};

export const professionList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=profession_tech_title`);
};

export const administrationList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=administration_lvl`);
};

export const cusLevelList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=cus_idea_level`);
};
export const cusPotenLevel = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=cus_poten_level`);
};

export const district = (params) => {
  return httpRequest.get(`/mdm/hcd/district`, params);
};

export const applyCustomerListApi = (params) => {
  return httpRequest.post(`/msr/customer/apply-customer/list`, params);
};

// 客户管理-搜索接口
export const crmCustomerApplyListApi = (params) => {
  return httpRequest.post(`/msr/customer/crm-customer-apply-list`, params);
};

export const customerDetail = (id) => {
  return httpRequest.get(`/msr/customer/apply-customer/${id}`);
};

export const surveyListApi = (params) => {
  return httpRequest.get(`/ade/survey/manage/query`, params);
};

// 提交问卷（明细）
export const addSurveyApi = (data) => {
  return httpRequest.post(`/ade/survey/details/add`, data);
};

// 提交问卷（明细）
export const addSurveyApi048 = (data) => {
  return httpRequest.post(`/ade/api/details/add`, data);
};

// 分页问卷明细查询（参数  问卷id：surveyId,创建人：createBy ）
export const surveyDetailApi = (params) => {
  return httpRequest.get(`/ade/survey/details/query`, params);
};

export const surveyMock = () => {
  return {
    "total": 3,
    "rows": [
      {
        "createBy": "1796436792244559874",
        "createTime": "2025-03-24 16:54:06",
        "updateBy": "1796436792244559874",
        "updateTime": "2025-03-24 16:54:06",
        "tenantId": "000010",
        "id": "1904094358738993154",
        "name": "JSZ",
        "describe": null,
        "targetType": "doctor",
        "productCode": "1JSZ",
        "productName": "金赛增",
        "scoreLevel": "[{\"label\": \"3232\", \"maxScore\": 2, \"minScore\": 0}]",
        "questions": "[{\"title\": \"11\", \"options\": [{\"score\": 2, \"content\": \"1111\"}]}]",
        "status": "1",
        "delFlag": "0",
        "updateEmpName": "000010"
      },
      {
        "createBy": "1796436792244559874",
        "createTime": "2025-03-24 15:25:21",
        "updateBy": "1796436792244559874",
        "updateTime": "2025-03-24 15:25:21",
        "tenantId": "000010",
        "id": "1904072023113891842",
        "name": "1234",
        "describe": null,
        "targetType": "doctor",
        "productCode": "YP0JMZ0SDJ01",
        "productName": "赛达久",
        "scoreLevel": "[{\"label\": \"123123\", \"maxScore\": 2, \"minScore\": 0}]",
        "questions": "[{\"title\": \"23232\", \"options\": [{\"score\": 1, \"content\": \"111\"}]}]",
        "status": "1",
        "delFlag": "0",
        "updateEmpName": "000010"
      },
      {
        "createBy": "1796436792244559874",
        "createTime": "2025-03-24 15:19:47",
        "updateBy": "1796436792244559874",
        "updateTime": "2025-03-24 15:19:47",
        "tenantId": "000010",
        "id": "1904070623768248322",
        "name": "111",
        "describe": null,
        "targetType": "doctor",
        "productCode": "YPGXJS0JSJ01",
        "productName": null,
        "scoreLevel": "[{\"label\": \"AA\", \"maxScore\": 123, \"minScore\": 0}]",
        "questions": "[{\"title\": \"12\", \"options\": [{\"score\": 3, \"content\": \"12\"}]}]",
        "status": "1",
        "delFlag": "0",
        "updateEmpName": "000010"
      }
    ],
    "code": 200,
    "msg": "查询成功"
  }
}
