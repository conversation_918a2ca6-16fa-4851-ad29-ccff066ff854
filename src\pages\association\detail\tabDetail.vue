<template>
  <view class="association-tabDetail">
    <view
      class="content"
      v-for="(item, index) in state.info"
      :key="item.dsCode"
    >
      <view class="content-top">
        <view class="top-tag">{{ index + 1 }}</view>
        <view class="title">{{ item.dsName }} </view>
      </view>
      <view class="content-info">
        <DetailItem label="药店类型" :value="item.drugstoreVoList[0].dsType" />
        <DetailItem
          label="上级药店"
          :value="item.drugstoreVoList[0].superiorDsName"
        />
        <DetailItem
          label="上级药店编码"
          :value="item.drugstoreVoList[0].superiorDsCode"
        />
        <nut-divider dashed :style="{ color: '#e5e6eb', margin: '0' }" />
        <view class="pro-list" v-for="j in item.drugstoreVoList" :key="j.id">
          <DetailItem label="品规名称" :value="j.specName" />
          <DetailItem label="是否上架" :value="j.shelves" />
          <DetailItem label="上级经销商" :value="j.parentDealer" />
          <DetailItem label="销售代表" :value="j.respEmpName" />
          <DetailItem label="地区经理" :value="j.areaMgr" />
          <!-- <DetailItem label="大区经理" :value="j.distMgr" /> -->
          <DetailItem label="大区销售经理" :value="j.distMgr" />
          <DetailItem label="是否解绑" :value="j.binding" />
          <DetailItem
            label="是否商务开发"
            :value="j.businessContact === '1' ? '是' : '否'"
          />
          <view class="item">
            <view class="detail-item-title">
              <view class="label">开发状态</view>
              <view class="value">
                <text>{{ getDevelopTypeLabel(j.salesStatus) }}</text>
                <text
                  style="color: #2551f2; margin-left: 4px"
                  v-if="getBusiness(j)"
                  @click="clickItem(j)"
                  >更改</text
                ></view
              >
            </view>
          </view>
          <nut-divider dashed :style="{ color: '#e5e6eb', margin: '0' }" />
        </view>
        <DetailItem
          :isImageUrl="true"
          label="照片(营业执照，经营许可证，门头照)"
          :value="item.urlList ? item.urlList : []"
        />
      </view>
    </view>
    <PopupRadio ref="popupRadioRef" @radio-confirm="radioConfirm" />
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { reactive, ref } from "vue";
import { getDevelopTypeLabel } from "@/utils/pharmacy.js";
import { updateSalesStatus } from "@/api/pharmacy.js";
import PopupRadio from "@/pages/components/popupRadio/popupRadio";
import DetailItem from "@/pages/components/detailItem/index.vue";
const emit = defineEmits("change-type");
const initHrInfo = Taro.getStorageSync("initHrInfo");
const popupRadioRef = ref(null);
const state = reactive({
  info: [],
  id: null,
});

const setInfo = (info) => {
  state.info = info;
};
const clickItem = (item) => {
  state.id = item.id;
  const options = Taro.getStorageSync("developType");
  popupRadioRef.value.open(options, item.salesStatus);
};

const radioConfirm = async (val) => {
  const res = await updateSalesStatus(state.id, val);
  if (res.code == 200) {
    Taro.showToast({
      title: "修改成功",
      icon: "none",
    });
  }
  emit("change-type");
};

const getBusiness = (j) => {
  if (j.businessContact === "1" && initHrInfo.identity === "business") {
    return true;
  }
  if (j.businessContact === "0" && initHrInfo.identity === "resp") {
    return true;
  }
};
defineExpose({
  setInfo,
});
</script>
<style lang="scss">
.association-tabDetail::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}
.association-tabDetail {
  font-size: 14px;
  color: #1d212b;
  overflow: hidden;
  overflow-y: scroll;
  padding: 12px;
  .popupRadio .footerButton {
    background: #fff;
    font-size: 20px;
  }
  .popupRadio .nut-popup {
    padding-bottom: 0 !important;
  }
  .item {
    .detail-item-title {
      display: flex;
      align-items: center;
      padding: 6px 0;
      .label {
        width: 30%;
        color: #869199;
      }
      .value {
        color: #1d212b;
        width: 70%;
        display: block;
      }
    }
  }
  .content {
    border-radius: 8px;
    background: #fff;
    margin-bottom: 8px;
    .content-top {
      display: flex;
      align-items: baseline;
      font-size: 18px;

      padding: 16px;
      border-bottom: 1px solid #e5e6eb;

      .top-tag {
        margin-right: 8px;
        min-width: 21px;
        height: 24px;
        background: linear-gradient(144deg, #597dff 0%, #2551f2 100%);
        border-radius: 2px 2px 2px 2px;
        line-height: 24px;
        text-align: center;
        color: #fff;
      }
      .title {
      }
    }
    .content-info {
      padding: 0 16px 16px 16px;
      .pro-list {
      }
    }
  }
}
</style>
