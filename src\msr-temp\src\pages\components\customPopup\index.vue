<template class="customPopup">
    <nut-popup :visible="showPopupVisible" :position="props.position" :closeable="props.closeable" :close-icon-position="closeIconPosition" round :style="style" @close="onClose" safe-area-inset-bottom @click-close-icon= clickCloseIcon @click-overlay="clickOverlay">
      <template #default>
        <!-- 弹出层的内容 -->
        <slot />
      </template>
    </nut-popup>
  </template>
  
  <script setup>
  import { defineEmits, defineProps, ref, onMounted } from 'vue'
  
  const props = defineProps({
    showPopupVisible: {
      type: Boolean,
      default: false,
    },
    position: {
      type: String,
      default: 'bottom',
    },
    style: {
      type: String,
      default: 'false',
    },
    closeIconPosition: {
      type: String,
      default: 'top-right',
    },
  })
  
  const emit = defineEmits(['updateShowPopupVisible'])
  
  const popupVisible = ref(props.showPopupVisible)

  const onClose = () => {
    popupVisible.value = false
    // eslint-disable-next-line vue/custom-event-name-casing
    emit('updateShowPopupVisible', popupVisible.value)
  }
const clickCloseIcon = () => {
  emit('updateShowPopupVisible', popupVisible.value)
}
const clickOverlay = () => {
  emit('updateShowPopupVisible', popupVisible.value)
}

  onMounted(() => {
  console.log("props.showPopupVisible",props.showPopupVisible)
  })
  </script>
  
  <style lang="scss">
  .customPopup {
  
  }
 
  </style>
  