import HTTPREQUEST from "@/servers/http";
export const applyFormTypeApi = () => {
  return HTTPREQUEST.get(`/plt/dict/data/list?dictType=apply_form_type`);
};

export const outsideTypeApi = () => {
  return HTTPREQUEST.get(`/plt/dict/data/list?dictType=outside_type`);
};
export const validApply = (params) => {
  return HTTPREQUEST.post(`/msr/valid/apply`, params);
};
export const validApplyList = (params) => {
  return HTTPREQUEST.get(`/msr/valid/apply`, params);
};
export const validTotalApi = () => {
  return HTTPREQUEST.get(`/msr/valid/total`);
};

export const sysValidDayApi = () => {
  return HTTPREQUEST.get(`/plt/config/configKey/sys.valid.day`);
};
