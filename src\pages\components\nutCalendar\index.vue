<template>
  <view>
    <nut-calendar
      :default-value="state.currentTime"
      v-model:visible="state.show"
      is-auto-back-fill
      type="range"
      :start-date="'2024-01-01'"
      :end-date="state.endDate"
      @choose="choose"
    >
    </nut-calendar>
  </view>
</template>
<script setup>
import { reactive } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
const emit = defineEmits(["chage-time"]);
const state = reactive({
  show: false,

  endDate: dayjs().format("YYYY-MM-DD"),
  topTime: null,
  currentTime: ["", ""],
});

const open = (time1, time2) => {
  state.show = true;
  if (time1) {
    state.currentTime = [time1, time2];
  }
};
const choose = (list) => {
  state.topTime = [list[0][3], list[1][3]];
  emit("chage-time", list[0][3], list[1][3]);
};
defineExpose({
  open,
});
</script>
<style lang="scss">
.nut-calendar
  .nut-calendar__content
  .nut-calendar__panel
  .nut-calendar__days
  .nut-calendar__day--choose::after {
  background-color: #92a8f8;
}
.nut-calendar
  .nut-calendar__content
  .nut-calendar__panel
  .nut-calendar__days
  .nut-calendar__day--choose {
  color: #2551f2;
}
.nut-calendar
  .nut-calendar__content
  .nut-calendar__panel
  .nut-calendar__days
  .nut-calendar__day--active {
  border-radius: 4px 0 0 4px;
}
</style>
