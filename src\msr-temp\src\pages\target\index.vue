<template>
  <view class="target">
    <view class="target-top">
      <view class="top-item">
        <view class="top-text"
          >{{ state.year }}年推广有效性<text style="font-size: 11px">(万)</text>
        </view>
        <view class="top-num" @click="numDetail('year')">{{
          convertToTenThousand(state.targeTotal.sum || "")
        }}</view>
      </view>
      <view class="top-item" style="margin: 0 8px">
        <view
          class="top-text"
          style="display: flex; align-items: center; justify-content: center"
        >
          <img :src="caretLeft" class="top-icons" @click="prevQuarter" />
          <view
            >Q{{ state.currentQuarter }}推广有效性<text style="font-size: 11px"
              >(元)</text
            ></view
          >
          <img :src="caretRight" class="top-icons" @click="nextQuarter" />
        </view>
        <view
          class="top-num"
          style="color: #2551f2"
          @click="numDetail('qur')"
          >{{ getQuarterTarget() }}</view
        >
      </view>
      <view class="top-item">
        <view
          class="top-text"
          style="display: flex; align-items: center; justify-content: center"
        >
          <img :src="caretLeft" class="top-icons" @click="prevMonth" />
          <view
            ><text  style="font-size: 13px">{{ state.month }}月推广有效性</text><text style="font-size: 11px"
              >(元)</text
            ></view
          >
          <img :src="caretRight" class="top-icons" @click="nextMonth" />
        </view>
        <view
          class="top-num"
          style="color: #ffb637"
          @click="numDetail('month')"
          >{{ getMonthTarget() }}</view
        >
      </view>
    </view>
    <view class="target-search">
      <view @click="selectYears" style="display: flex; align-items: center">
        <view class="year">{{ state.targeTotal.year }}</view>
        <IconFont name="triangle-down" color="#2551F2" size="12px"></IconFont>
      </view>
      <SearchBar
        @seacrh-top="topSearch"
        placeholder="请输入机构名称、负责人姓名和工号"
        :filters="false"
      />
    </view>
    <scroll-view
      style="height: 500px; padding-bottom: 20px"
      :scroll-y="true"
      :scroll-top="state.scrollTop"
      @scrolltolower="onScrollToLower"
    >
      <view
        style="
          position: absolute;
          top: 35%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!state.list.length && scrollMyFlag"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>
      <view class="target-content">
        <view
          class="target-item"
          v-for="(item, index) in state.list"
          :key="item.id"
        >
          <view class="item-top">
            <view class="item-title">
              <view
                style="
                  color: #1d212b;
                  font-size: 18px;
                  font-weight: 500;
                  white-space: nowrap; /* 防止文本换行 */
                  overflow: hidden; /* 隐藏超出的内容 */
                  text-overflow: ellipsis;
                "
                >{{ item.crmInsName }}</view
              >
              <view
                style="color: #869199; font-size: 16px"
                @click="open(index)"
              >
                <view v-if="!item.flag" style="min-width: 16px">
                  <img :src="arrowDown" class="cIncons" />
                </view>
                <view v-else style="min-width: 15px">
                  <img :src="up" class="cIncons" />
                </view>
              </view>
            </view>
            <view class="sub-name">
              <view class="kpi" style="display: flex; align-items: center">
                <!-- <view class="kpi-icon" style="">KPI</view> -->

                <img :src="kpiFill" style="width: 20px; height: 20px" />
                <view
                  style="font-size: 16px; color: #2551f2; margin-left: 4px"
                  >{{ formatNumber(item.sum || "") }}</view
                >
              </view>
              <view style="color: #4e595e; font-size: 14px"
                >{{ item.nickName
                }}<tetx v-if="item.deptName">({{ item.deptName }})</tetx></view
              >
            </view>
          </view>
          <nut-divider
            dashed
            :style="{ color: '#e5e6eb', margin: '0' }"
            v-if="item.flag"
          />
          <view class="item-report" v-if="item.flag">
            <view
              class="item-table-title"
              style="background: #f3f4f5; border-radius: 8px 8px 0 0"
            >
              <view class="label">月份</view>
              <view class="value" style="color: #869199">推广有效性额(元)</view>
            </view>
            <view class="item-table">
              <view class="item-table-item">
                <view class="label">1月</view>
                <view class="value" @click="detail(item, 1)">{{
                  formatNumber(item.janTarget || "")
                }}</view>
              </view>

              <view class="item-table-item">
                <view class="label">2月</view>
                <view class="value" @click="detail(item, 2)">{{
                  formatNumber(item.febTarget || "")
                }}</view>
              </view>
              <view class="item-table-item">
                <view class="label">3月</view>
                <view class="value" @click="detail(item, 3)">{{
                  formatNumber(item.marTarget || "")
                }}</view>
              </view>
              <view class="item-table-item">
                <view class="label">4月</view>
                <view class="value" @click="detail(item, 4)">{{
                  formatNumber(item.aprTarget || "")
                }}</view>
              </view>
              <view class="item-table-item">
                <view class="label">5月</view>
                <view class="value" @click="detail(item, 5)">{{
                  formatNumber(item.mayTarget || "")
                }}</view>
              </view>
              <view class="item-table-item">
                <view class="label">6月</view>
                <view class="value" @click="detail(item, 6)">{{
                  formatNumber(item.junTarget || "")
                }}</view>
              </view>
              <view class="item-table-item">
                <view class="label">7月</view>
                <view class="value" @click="detail(item, 7)">{{
                  formatNumber(item.julTarget || "")
                }}</view>
              </view>
              <view class="item-table-item">
                <view class="label">8月</view>
                <view class="value" @click="detail(item, 8)">{{
                  formatNumber(item.augTarget || "")
                }}</view>
              </view>
              <view class="item-table-item">
                <view class="label">9月</view>
                <view class="value" @click="detail(item, 9)">{{
                  formatNumber(item.sepTarget || "")
                }}</view>
              </view>
              <view class="item-table-item">
                <view class="label">10月</view>
                <view class="value" @click="detail(item, 10)">{{
                  formatNumber(item.octTarget || "")
                }}</view>
              </view>
              <view class="item-table-item">
                <view class="label">11月</view>
                <view class="value" @click="detail(item, 11)">{{
                  formatNumber(item.novTarget || "")
                }}</view>
              </view>
              <view class="item-table-item" style="border-bottom: none">
                <view class="label">12月</view>
                <view class="value" @click="detail(item, 12)">{{
                  formatNumber(item.decTarget || "")
                }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view
        v-if="state.list.length > 0 && state.list.length == state.total"
        style="
          color: #869199;
          font-size: 16px;
          text-align: center;
          margin-top: 8px;
        "
        >已全部加载完毕</view
      >
    </scroll-view>
    <Years
      :list="state.targeTotal.yearList"
      ref="yearsRef"
      @confirm="yearsConfirm"
    />
  </view>
  <nut-popup
    v-model:visible="show"
    round
    :style="{ width: '311px', height: '120px' }"
  >
    <view style="padding: 16px 16px 0 16px">
      <view class="target-title-popup"
        >{{ state.tetx1 }}{{ state.tetx2 }}推广有效性（元）</view
      >
      <view class="target-num-popup">
        {{ state.text3 || 0 }}
      </view>
    </view>
    <nut-divider :style="{ color: '#E5E6EB', margin: 0 }" />
    <view
      style="text-align: center; font-size: 16px; color: #2551f2; margin: 8px"
      @click="show = false"
      >确定</view
    >
  </nut-popup>
</template>
<script setup>
import Taro, { useRouter, useDidShow, useDidHide } from "@tarojs/taro";
import { reactive, onMounted, ref } from "vue";

import caretLeft from "@/images/caret-left.png";
import caretRight from "@/images/caret-right.png";
import { IconFont } from "@nutui/icons-vue-taro";
import SearchBar from "@/pages/components/searchBar";
import arrowDown from "@/images/arrow-down.png";
import up from "@/images/up_2.png";
import iconWrapper from "@/images/icon-wrapper.png";
import {
  formatNumber,
  convertToTenThousand,
  formatNumber2,
  convertToWQYF,
} from "@/utils/pharmacy";
import { targetTotal, targetList } from "@/api/target";
import kpiFill from "@/images/kpi-fill.png";
import Years from "./components/years";
import noData from "@/images/no-data.png";
const scrollMyFlag = ref(false);
const yearsRef = ref(null);
const show = ref(false);
const state = reactive({
  scrollTop: 0,
  total: 0,
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
  currentQuarter: null,
  list: [],
  targeTotal: {},
  params: {
    pageNum: 1,
    pageSize: 10,
    nickName: "",
  },
  tetx1: "",
  tetx2: "",
  text3: null,
  qurNum: null,
  monthNum: null,
});
const open = (index) => {
  // state.list.forEach((item) => {
  //   if (item.insCode === i.insCode) {
  //     item.flag = !item.flag;
  //   }
  // });
  state.list[index].flag = !state.list[index].flag;
};
const topSearch = (search) => {
  state.params.nickName = search;
  state.params.pageNum = 1;
  state.list = [];
  getlist();
};

const onScrollToLower = () => {
  console.log("下拉加载1");
  if (scrollMyFlag.value && state.list.length < state.total) {
    console.log("下拉加载2");
    state.params.pageNum++;
    scrollMyFlag.value = false;
    getlist();
  }
};
const getTargetTotal = async () => {
  const res = await targetTotal();
  state.targeTotal = res.data;
  state.targeTotal.year = state.targeTotal.yearList
    ? state.targeTotal.yearList[0]
    : "";
  getlist();
};
const getlist = async () => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  state.params.year = state.targeTotal.year ? state.targeTotal.year : "";
  const res = await targetList(state.params);
  if (res.code == 200) {
    Taro.hideLoading();
    state.total = res.total;
    res.rows = res.rows.map((item) => {
      item.flag = false;
      return item;
    });
    if (!state.list.length) {
      state.list = res.rows;
    } else {
      state.list = [...state.list, ...res.rows];
    }
    scrollMyFlag.value = true;
  } else {
    Taro.hideLoading();
  }
};
const detail = (item, month) => {
  const detail = {
    year: item.year,
    postCode: item.postCode,
    insCrmCode: item.insCrmCode,
  };
  Taro.navigateTo({
    url: `/pages/target/detail?detail=${JSON.stringify(
      detail
    )}&month=${month}&nickName=${item.nickName || ""}&deptName=${
      item.deptName || ""
    }`,
  });
};
const getCurrentQuarter = () => {
  const month = new Date().getMonth(); // 获取当前月份，注意月份是从0开始的
  return Math.floor((month + 3) / 3); // 计算季度，+3是为了使结果为1-4而不是0-3
};

const prevQuarter = () => {
  state.currentQuarter =
    state.currentQuarter === 1 ? 4 : state.currentQuarter - 1;
};
const nextQuarter = () => {
  state.currentQuarter =
    state.currentQuarter === 4 ? 1 : state.currentQuarter + 1;
};

const prevMonth = () => {
  if (state.month === 1) {
    state.month = 12; // 如果是1月，切换到12月
  } else {
    state.month--; // 否则，减去1个月
  }
};
const nextMonth = () => {
  if (state.month === 12) {
    state.month = 1; // 如果是12月，切换到1月
  } else {
    state.month++; // 否则，增加1个月
  }
};

const selectYears = () => {
  yearsRef.value.open(state.targeTotal.year);
};

const yearsConfirm = (code) => {
  state.targeTotal.year = code;
  state.params.pageNum = 1;
  state.list = [];
  getlist();
};

const getQuarterTarget = () => {
  if (state.currentQuarter === 1) {
    state.qurNum = state.targeTotal.quarterOne;

    return formatNumber2(state.targeTotal.quarterOne || 0);
  }
  if (state.currentQuarter === 2) {
    state.qurNum = state.targeTotal.quarterTwo;
    return formatNumber2(state.targeTotal.quarterTwo || 0);
  }
  if (state.currentQuarter === 3) {
    state.qurNum = state.targeTotal.quarterThree;
    return formatNumber2(state.targeTotal.quarterThree || 0);
  }
  if (state.currentQuarter === 4) {
    state.qurNum = state.targeTotal.quarterFour;
    return formatNumber2(state.targeTotal.quarterFour || 0);
  }
};
const getMonthTarget = () => {
  const monthTargets = {
    1: state.targeTotal.janTarget,
    2: state.targeTotal.febTarget,
    3: state.targeTotal.marTarget,
    4: state.targeTotal.aprTarget,
    5: state.targeTotal.mayTarget,
    6: state.targeTotal.junTarget,
    7: state.targeTotal.julTarget,
    8: state.targeTotal.augTarget,
    9: state.targeTotal.sepTarget,
    10: state.targeTotal.octTarget,
    11: state.targeTotal.novTarget,
    12: state.targeTotal.decTarget,
  };
  const target = monthTargets[state.month];
  state.monthNum = target;
  return target !== undefined ? formatNumber2(target) : 0;
};

const numDetail = (type) => {
  show.value = true;
  if (type === "year") {
    state.text3 = state.targeTotal.sum;
    state.tetx1 = state.targeTotal.year;
    state.tetx2 = "年";
  }
  if (type === "qur") {
    state.text3 = state.qurNum;
    state.tetx1 = state.currentQuarter;
    state.tetx2 = "季度";
  }

  if (type === "month") {
    state.text3 = state.monthNum;
    state.tetx1 = state.month;
    state.tetx2 = "月";
  }
};

const formatWQYF = (obj) => {
  return `${obj.w}万${obj.q}千${obj.y}元${obj.f}分`;
};
onMounted(() => {
  Taro.setNavigationBarTitle({ title: "推广有效性" });
  state.currentQuarter = getCurrentQuarter();
  getTargetTotal();
});
</script>
<style lang="scss">
.target {
  .target-top {
    background: #fff;
    display: flex;
    align-items: center;
    padding: 16px;
    padding-bottom: 0;
    .top-item {
      width: 109px;
      height: 79px;
      background: #fcfcfc;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #e5e6eb;
      text-align: center;
      .top-text {
        margin-top: 16px;
        color: #4e595e;
        font-size: 14px;

        .top-icons {
          width: 16px;
          height: 16px;
          display: block;
          vertical-align: middle;
        }
      }
      .top-num {
        color: #1d212b;
        font-size: 22px;
        font-weight: bold;
        text-decoration: underline;
      }
    }
  }
  .target-search {
    display: flex;
    align-items: center;
    background: #fff;
    padding: 16px;
    border-radius: 0 0 16px 16px;
    .year {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #2551f2;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-right: 4px;
    }
  }
  .searchBar {
    width: 100%;
    padding: 0;
    padding-left: 16px;
  }
  .searchBar .nut-searchbar {
    width: 100%;
  }
  .target-content {
    padding: 0 16px;

    .target-item {
      background: #fff;

      margin-top: 16px;
      border-radius: 8px;
      .item-top {
        padding: 16px;
        .item-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .sub-name {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .kpi {
            padding: 0 8px;
            background: #e8f0ff;
            border-radius: 4px;
            .kpi-icon {
              background: #2551f2;
              color: #fff;
              width: 16px;
              height: 16px;
              font-size: 8px;
              line-height: 16px;
              text-align: center;
              border-radius: 2px;
            }
          }
        }
      }
      .item-report {
        padding: 16px;
        .item-table {
          border: 1px solid #e5e6eb;
          border-radius: 0 0 8px 8px;
          .item-table-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e6eb;

            .label {
              width: 30%;
              color: #121d29;
              font-size: 12px;
              text-align: center;
            }
            .value {
              width: 70%;
              color: #2551f2;
              text-decoration: underline;
              font-size: 14px;
              text-align: center;
            }
          }
        }
        .item-table-title {
          display: flex;
          align-items: center;
          padding: 8px 0;
          border: 1px solid #e5e6eb;
          border-bottom: none;

          .label {
            width: 30%;
            color: #869199;
            font-size: 14px;
            text-align: center;
          }
          .value {
            width: 70%;
            color: #869199;
            font-size: 14px;
            text-align: center;
          }
        }
      }
    }
  }
  .cIncons {
    display: block;
    width: 14px;
    height: 14px;
  }
  .nut-searchbar__search-input .nut-searchbar__input-inner-icon {
    padding: 0;
  }
  .searchBar
    .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    font-size: 12px;
  }
}
.target-title-popup {
  font-size: 16px;
  color: #121d29;
  text-align: center;
}
.target-num-popup {
  font-size: 16px;
  text-align: center;
  margin-top: 8px;
  color: #2551f2;
}
</style>
