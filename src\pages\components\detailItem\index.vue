<template>
  <view class="detail-item">
    <view v-if="props.isImageUrl">
      <view style="width: 100%; color: #869199">{{ props.label }}</view>
      <view class="image" v-for="item in props.value" :key="item">
        <img
          :src="item"
          alt=""
          class="image-item"
          @click="handlePreview(index)"
        />
      </view>
    </view>
    <view class="item" v-else @click="clickItem">
      <view class="detail-item-title">
        <view class="label">{{ props.label }}</view>
        <view class="value"
          ><text>{{ props.value }}</text></view
        >
      </view>
    </view>
    <nut-image-preview
      v-if="props.isImageUrl"
      :autoplay="0"
      :show="showPreview"
      :images="props.value && props.value.map((d) => d)"
      @close="hideImgPreview"
      :init-no="imgPreviewIndex"
    />
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
const emit = defineEmits(["click-item"]);
const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  value: {
    type: [String, Number],
    default: "",
  },
  isImageUrl: {
    type: Boolean,
    default: false,
  },
});
const state = reactive({});
const showPreview = ref(0);
const imgPreviewIndex = ref(0);
const handlePreview = (index) => {
  imgPreviewIndex.value = index;
  showImgPreview();
};
const showImgPreview = () => {
  showPreview.value = true;
};

const hideImgPreview = () => {
  showPreview.value = false;
};

const clickItem = () => {
  emit("click-item");
};
</script>
<style lang="less" scoped>
.detail-item {
  .item {
    .detail-item-title {
      display: flex;
      align-items: baseline;
      padding: 6px 0;
      .label {
        width: 30%;
        color: #869199;
      }
      .value {
        color: #1d212b;
        width: 70%;
        display: block;
      }
    }
  }
  .image {
    display: flex;
    .image-item {
      width: 80px;
      height: 80px;
    }
  }
}
</style>
