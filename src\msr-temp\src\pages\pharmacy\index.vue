<!-- 机构列表页 -->
<template>
  <view class="pharmacy">
    <SearchBar
      @seacrh-top="topInsSearch"
      @filters="insFilters"
      placeholder="请输入药店名称或编码"
      v-show="state.currentTab === '0'"
      :active="state.activeFlag"
    />
    <SearchBar
      @seacrh-top="topCusSearch"
      @filters="cusFilters"
      placeholder="请输入关键字搜索"
      v-show="state.currentTab === '1'"
      :active="state.approveActiveFlag"
    />
    <view>
      <nut-tabs
        v-model="state.currentTab"
        backgroud="#FFFFFF"
        @change="tabChange"
      >
        <nut-tab-pane title="药店" pane-key="0">
          <InsList
            ref="insListRef"
            :tab="state.currentTab"
            @change-pcdvalue="changePcdvalue"
          />
        </nut-tab-pane>
        <nut-tab-pane title="药店审批" pane-key="1">
          <!-- <ApproveList
            ref="approveListRef"
            :tab="state.currentTab"
            @change-filter="changeApprove"
          /> -->
          <ApproveList
            ref="approveListRef"
            :tab="state.currentTab"
            @change-filter="changeApprove"
            processType="bpm_drugstore_apply"
          />
        </nut-tab-pane>
      </nut-tabs>
    </view>
    <view
      style="
        position: fixed;
        height: 56px;
        text-align: right;
        bottom: 80px;
        right: 16px;
      "
      v-if="
        (initHrInfo.identity === 'resp' || state.addPharmacyFlag === '1') &&
        state.currentTab === '0'
      "
      @click="searchPharmacy"
    >
      <view class="oval">
        <view style="margin-right: 8px"
          ><IconFont
            name="uploader"
            color="#fff"
            size="12px"
            style="display: block"
          ></IconFont
        ></view>

        <view style="color: #fff; font-size: 15px">新增</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow, useDidHide } from "@tarojs/taro";
import { ref, onMounted, reactive, onUnmounted, onBeforeUnmount } from "vue";
import InsList from "./ins/instList.vue";
import plus from "../../images/inst_plus.png";
import { sysAddPharmacy } from "../../api/pharmacy.js";
import { IconFont } from "@nutui/icons-vue-taro";
import SearchBar from "../../pages/components/searchBar";
import ApproveList from "@/bpm-temp/src/pages/approve_list/index.vue";
const router = useRouter();
const query = router.params;
const initHrInfo = Taro.getStorageSync("initHrInfo");
const insListRef = ref(null);
const approveListRef = ref(null);

const state = reactive({
  currentTab: "0",
  activeFlag: false,
  approveActiveFlag: false,
  previousUrl: "",
  addPharmacyFlag: "",
});

const changePcdvalue = (value) => {
  if (value) {
    state.activeFlag = true;
  } else {
    state.activeFlag = false;
  }
};
const changeApprove = (value) => {
  console.log("2222", value);
  if (value) {
    state.approveActiveFlag = true;
  } else {
    state.approveActiveFlag = false;
  }
};

const topInsSearch = (search) => {
  insListRef.value.topSearch(search);
};

const topCusSearch = (search) => {
  approveListRef.value.topSearch(search);
};
const insFilters = () => {
  insListRef.value.insFilters();
};
const cusFilters = (search) => {
  approveListRef.value.approveFilters(search);
};

const tabChange = () => {
  if (state.currentTab === "0") {
    insListRef.value.initInsList();
  } else {
    approveListRef.value?.initList();
  }
};

const searchPharmacy = () => {
  Taro.navigateTo({
    url: `/pages/pharmacy/components/search`,
  });
};
const handlePopState = (event) => {
  Taro.navigateTo({
    url: "/pages/index/index",
  });
};
useDidShow(() => {
  if (query.index === "1") {
    state.currentTab = "1";
  }
  Taro.setNavigationBarTitle({ title: "药店管理" });
  tabChange();
  state.previousUrl = window.location.pathname;
  // window.addEventListener("popstate", handlePopState);
});
const getsysJurInsImport = async () => {
  const res = await sysAddPharmacy();
  state.addPharmacyFlag = res.msg;
};
onMounted(() => {
  getsysJurInsImport();
});
// useDidHide(() => {
//   console.log("1111");
//   window.removeEventListener("popstate", handlePopState);
// });
</script>

<style lang="scss">
input::placeholder {
  color: yellow;
}

.pharmacy {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100vw;
  overflow: hidden;
  .searchBar {
    background: #fff;
  }
  .oval {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 82px;
    height: 41px;
    background: linear-gradient(144deg, #597dff 0%, #2551f2 100%);
    box-shadow: 0px 2px 4px 0px rgba(37, 81, 242, 0.45);
    border-radius: 100px 100px 100px 100px;
  }

  .nut-tabs__titles-item__line {
    background: blue;
  }

  .nut-tab-pane {
    background-color: transparent;
    padding: 18px 0px;
  }
  .nut-tab-pane {
    padding: 0 0;
  }
}
</style>
