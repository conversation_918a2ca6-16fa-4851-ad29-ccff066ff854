<template>
  <!-- 使用 form-create-mobile 组件 -->
  <form-create-mobile :rule="rule" v-model:api="fApi" v-model="formData" :option="options" :disabled="true"/>
</template>


<script setup>
import { ref } from 'vue';
const fApi = ref({});
const formData = ref({});
const options = {
  onSubmit: (formData) => {
    alert(JSON.stringify(formData)); // 提交表单时弹出表单数据
  },
  resetBtn: false, // 显示重置按钮
  submitBtn: false
};
const rule = ref([
  {
    type: 'input',
    field: 'goods_name',
    title: '商品名称',
    value: 'form-create'
  },
  {
    type: 'checkbox',
    field: 'label',
    title: '标签',
    value: [0, 1, 2, 3],
    options: [
      { label: '好用', value: 0 },
      { label: '快速', value: 1 },
      { label: '高效', value: 2 },
      { label: '全能', value: 3 }
    ]
  }
]);
</script>