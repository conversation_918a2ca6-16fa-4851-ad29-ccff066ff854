<!-- 机构和客户列表 组件 -->
<template>
  <view class="cusList">
    <view class="title"
      >共{{ state.total }}条客户认领记录<span v-if="!ISGEN"
        >(请注意可能存在重复认领)</span
      ></view
    >
    <scroll-view
      style="height: 500px; position: relative"
      :scroll-y="true"
      :scroll-top="state.scrollTop"
      @scrolltolower="onScrollToLower"
    >
      <view
        style="
          position: absolute;
          top: 26%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!state.cusList.length && scrollMyFlag"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>
      <view v-for="item in state.cusList" :key="item.jurId" class="item">
        <view class="content" @click="goCusDetail(item)">
          <view class="name">
            <view style="display: flex; align-items: center; flex-wrap: wrap">
              <view style="font-size: 18px" clasa="customerName"
                >{{ item.customerName }}
              </view>

              <nut-tag
                plain
                :color="getcolorProfession(item.professionTech)"
                :text-color="getTextProfession(item.professionTech)"
                style="margin-left: 8px; vertical-align: text-top"
                v-if="item.professionTech"
              >
                {{ item.professionTech }}
              </nut-tag>
              <nut-tag
                plain
                :color="'#2551F2'"
                :text-color="'#2551F2'"
                style="margin-left: 8px; vertical-align: text-top"
                v-if="item.isSpeaker === '1'"
              >
                医生讲者
              </nut-tag>
              <view
                style="display: flex; align-items: center"
                v-if="item.insDeptName"
              >
                <text class="hosType" style="margin-right: 5px">{{
                  item.insDeptName
                }}</text>
              </view>
              <nut-tag
                plain
                color="#C6CAD1"
                text-color="#4E595E"
                style="vertical-align: text-top"
                v-if="item.insLabel"
              >
                {{ item.insLabel }}
              </nut-tag>
            </view>
            <view
              style="
                display: flex;
                flex-direction: row;
                align-self: center;
                margin-right: 16px;
              "
              @click="handelVisit(item)"
            >
              <!-- <view class="productTag" v-if="item.claiming === '1'">
                <Clock
                  color="#FF7D00"
                  size="12"
                  style="vertical-align: middle"
                />
                <text style="margin-left: 2px">审批中</text>
              </view> -->
              <view class="goto">
                <img :src="arrowRight" style="width: 15px; height: 17px" />
              </view>
            </view>
          </view>

          <view
            style="
              display: flex;
              justify-content: space-between;
              align-items: baseline;
              padding-right: 14px;
            "
          >
            <view style="font-size: 14px; color: #869199"
              >机构：{{ item.insName }}</view
            >
          </view>

          <view style="color: #869199; font-size: 14px" v-if="item.mgr.length"
            >代表：<text v-for="j in item.mgr" :key="j.name"
              >{{ j.value }}({{ j.name }})</text
            >
          </view>

          <view
            style="
              color: #869199;
              font-size: 14px;

              -webkit-line-clamp: 1; /* 显示的行数 */
              -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
              overflow: hidden; /* 隐藏超出的内容 */
              text-overflow: ellipsis;
            "
            v-if="item.productList.length"
            >产品：{{ getProduct(item.productList)
            }}<text v-if="item.productList.length > 3">{{
              ` | +${item.productList.length - 3}`
            }}</text>
          </view>
          <view class="opera-button">
            <!--            <view @click.prevent.stop="handleAddSpeaker(item)" class="appraiseButton bule" v-if="item.isSpeaker !== '1'">-->
            <!--              新增为讲者-->
            <!--            </view>-->
            <view
              @click.prevent.stop="noTitleClick(item)"
              class="appraiseButton gray"
              v-if="
                props.sysJuCustDelObj.settingStatus === '1'
              "
            >
              客户解绑
            </view>
            <view
              @click.prevent.stop="goAppraise(item)"
              class="appraiseButton bule"
              v-if="!props.appcodeCust"
            >
              去拜访
            </view>
          </view>
        </view>
      </view>
      <view
        v-if="state.cusList.length > 0 && state.cusList.length == state.total"
        style="color: #869199; font-size: 16px; text-align: center"
        >已全部加载完毕</view
      >
    </scroll-view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import {
  defineEmits,
  defineProps,
  ref,
  reactive,
  defineExpose,
  onMounted,
} from "vue";
import arrow from "../../../images/arrow.png";
import arrowRight from "../../../images/arrow-right.png";
import address from "../../../images/location_a.png";
import hospital from "../../../images/iconhospital.png";
import cus_ins from "../../../images/hospital_v4.png";
import user from "../../../images/user_2.png";
import {
  getPostCode,
  getcolorProfession,
  getTextProfession,
} from "../../../utils/area.js";
import { appCode, tenantId, baseApi, appId, h5Url, pcUrl } from "../../../utils/content";
import {
  customerList,
  approveInitiate,
  customerDetail,
} from "../../../api/area.js";
import { Clock } from "@nutui/icons-vue-taro";
import noData from "../../../images/no-data.png";
import { getProcessIdApi } from "../../../utils/content";
const feishuNotice = ref({
  appLink: `${h5Url}`,
  pcLink: `${pcUrl}`,
});
const ISGEN = ["/ade/"].includes(baseApi);

const scrollMyFlag = ref(false);
const emit = defineEmits(["change-filter", "goAppraise", "noTitleClick"]);
const props = defineProps({
  tab: {
    type: String,
    default: "",
  },
  appcodeCust: {
    type: String,
    default: "",
  },
  sysJuCustDelObj: {
    type: Object,
    default: {},
  },
});

const noTitleClick = async (item) => {
  // const initUserInfo = Taro.getStorageSync("initUserInfo");
  // const posts = initUserInfo?.user?.posts?.[0]
  // const dept =initUserInfo?.user?.dept
  // console.log(initUserInfo, "-initUserInfo")
  Taro.showLoading({
    icon: "loading",
  });
  state.info = item;
  let params = {
    jurCode: state.info.jurCode,
    insCode: state.info.insCode,
    customerCode: state.info.customerCode,
  };
  params.isClaim = "1";
  const detailInfo = (await customerDetail(params))?.data;
  Taro.hideLoading();

  Taro.showModal({
    content: "点击确认提交客户解绑审批，进入我的审批查看审批单状态",
    confirmText: "确定",
    duration: 2000,
    success: async function (res) {
      console.log(11111, item);
      if (res.confirm) {
        const initHrInfo = Taro.getStorageSync("initHrInfo");

        console.log(detailInfo, "-detailInfo");
        const initHrInfoInfo = {
          applicant: initHrInfo.empName,
          applicantCode: initHrInfo.empCode,
          postIdList: initHrInfo.postIdList,
        };
        const applicantInfo = {
          ...initHrInfoInfo,
          // postCode: posts.postCode,
          // postName: posts.postName,
          // deptCode: dept.deptCode,
          // deptName: dept.deptName,
          postCode: detailInfo.postCode,
          postName: detailInfo.postName,
          deptCode: detailInfo.deptCode,
          deptName: detailInfo.deptName,
          ancestors: detailInfo.ancestors,
        };
        const params = {
          applyContent: {
            ...state.info,
            ...detailInfo,
            ...initHrInfoInfo,
            type: "custUnbound",
            products: [],
            ...props.sysJuCustDelObj,
            // ...posts,
            // ...dept
          },
          insCode: state.info.insCode,
          insMdmCode: state.info.insMdmCode,
          insName: state.info.insName,
          appCode: appCode,
          tenantId: tenantId,
          applyType: "7",
          jurCode: state.info.jurCode,
          ...applicantInfo,
          customerCode: state.info.customerCode,
          customerName: state.info.customerName,
          channel: "2",
          processId: state.processId,
          ...feishuNotice.value,
        };
        const res = await approveInitiate(params);
        if (res.code === 200) {
          await Taro.showToast({
            title: res.msg || "解绑成功",
            icon: "none",
            duration: 2000,
          });
          setTimeout(() => {
            topSearch();
          }, 1000);
        } else {
          Taro.showToast({
            title: res.msg || "解绑失败",
            icon: "none",
            duration: 2000,
          });
        }
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
  // emit("noTitleClick", true,item);
};
const state = reactive({
  scrollTop: 0,
  pageNum: 1,
  pageSize: 10,
  params: {
    search: "",

    teachTitle: "",
    job: "",
    jurCodeList: getPostCode().split(","),
  },
  total: 0,

  cusList: [],
  processId: "",
});
const topSearch = (search) => {
  state.params.search = search;
  state.total = 0;
  state.pageNum = 1;
  state.cusList = [];
  getcusList();
};
const getProduct = (list) => {
  if (list.length) {
    return list
      .slice(0, 3)
      .map((item) => item.name)
      .join(" | ");
  }
};
const cusFilters = () => {
  Taro.navigateTo({
    url: `/pages/area/customer/cusFilters?&info=${JSON.stringify(
      state.params
    )}`,
  });
};

const handleAddSpeaker = (item) => {
  const access_token = Taro.getStorageSync("access_token");
  const url =
    process.env.NODE_ENV === "production"
      ? `https://athena-mds-mp.dgtmeta.com`
      : `https://athena-mds-mp.t.dgtmeta.com`;
  window.location.href = `${url}/pages/speaker/index?source=crm&token=${access_token}&appid=${
    appId
  }&originUrl=${
    window.location.origin
  }&code=process.env.TARO_APP_BASE_API&info=${JSON.stringify({
    ...item,
    name: item.customerName,
    type: "讲者类型",
    deptName: item.insDeptName,
    professionTechTitle: item.professionTech,
  })}&type=addSpeaker&sourceBuName=${process.env.TARO_APP_CODE}`;
};

const onScrollToLower = () => {
  if (scrollMyFlag.value && state.cusList.length < state.total) {
    state.pageNum++;
    scrollMyFlag.value = false;
    getcusList();
  }
};
const goAppraise = (item) => {
  emit("goAppraise", item);
};

const getcusList = () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  customerList(state.pageNum, state.pageSize, state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.data.total;
        if (!state.cusList.length) {
          state.cusList = res.data.rows;
        } else {
          state.cusList = [...state.cusList, ...res.data.rows];
        }
        state.msg = res.msg;
        Taro.hideLoading();
        scrollMyFlag.value = true;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      Taro.hideLoading();
    });
};

const initCusList = () => {
  const cusFilters = Taro.getStorageSync("cusFilters");
  if (cusFilters) {
    state.params.teachTitle = cusFilters.teachTitle;
    state.params.job = cusFilters.job;
    Taro.removeStorageSync("cusFilters");
  }
  state.pageNum = 1;
  state.cusList = [];
  getcusList();
  emit("change-filter", state.params);
};
const getKey = async () => {
  state.processId = await getProcessIdApi("jur_customer_apply");
};
//#region  点击列表项跳转到机构页
const goCusDetail = (item) => {
  Taro.navigateTo({
    url: `/pages/area/customer/cusDeatil?jurCode=${item.jurCode}&insCode=${item.insCode}&customerCode=${item.customerCode}&claiming=${item.claiming}`,
  });
};
defineExpose({
  initCusList,
  topSearch,
  cusFilters,
});
onMounted(() => {
  getKey();
});
</script>
<style lang="scss">
.gray {
  border: #e5e6eb 1px solid;
  color: #4e595e;
  font-size: 14px;
  font-weight: 400;
}

.bule {
  color: #2551f2;
  background-color: #e8f0ff;
  font-size: 14px;
  font-weight: 400;
}

.appraiseButton {
  z-index: 999;
  width: 80px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: 100px;
  margin-right: 12px;
  margin-top: 6px;
}

.opera-button {
  display: flex;
  justify-content: flex-end;
}

.cusList {
  padding: 0px 0px;
  width: 100vw;
  // height: 630px;
  font-family: PingFang SC, PingFang SC;

  .title {
    color: #869199;
    font-size: 14px;
    padding: 18px 12px 8px 12px;
  }

  .item {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    margin-left: 12px;
    margin-right: 12px;

    .content {
      padding: 16px 16px 12px 16px;
      padding-right: 0px;

      .name {
        display: flex;
        font-weight: 500;
        font-size: 16px;
        color: #1d212b;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2px;
      }

      .level {
        height: 18px;
        border-radius: 2px 2px 2px 2px;
        border: 1px solid #c6cad1;
        vertical-align: middle;
        text-align: center;
        display: inline;
        padding: 0px 4px;

        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #4e595e;
        line-height: 18px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      .title {
        margin-left: 8px;
        height: 24px;
        //background: #E8F0FF;
        border-radius: 2px 2px 2px 2px;
        //padding: 4px 6px 3px;
        display: flex;
        justify-content: center;
        align-self: center;

        .font {
          height: 17px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: #2551f2;
          line-height: 17px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-left: 6px;
          margin-right: 6px;
          display: flex;
          justify-content: center;
          align-self: center;
        }
      }

      .custCnt {
        height: 22px;
        font-weight: 400;
        font-size: 12px;
        color: #869199;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .nut-divider.nut-divider-vertical {
        border-left: 1px solid #869199;
      }

      .hosType {
        height: 18px;
        border-radius: 2px 2px 2px 2px;
        border: 1px solid #c6cad1;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #4e595e;
        line-height: 18px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        padding: 0 4px;
        margin-left: 4px;
      }
    }

    .insIcon {
      display: inline-block;
      margin-right: 6px;
      width: 18px;
      height: 18px;
      vertical-align: text-bottom;
    }
  }

  .productTag {
    font-size: 12px;
    text-align: center;
    background: #fff3e8;
    color: #ff7d00;
    height: 20px;
    padding: 0 2px;
    line-height: 20px;
    margin-right: 12px;
    border-radius: 2px;
  }
}
</style>
