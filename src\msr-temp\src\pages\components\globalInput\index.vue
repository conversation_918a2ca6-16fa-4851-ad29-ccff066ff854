<template>
  <view class="globa-input">
    <!-- <nut-input
      v-model="value[0].content"
      placeholder="请输入"
      type="text"
      :disabled="disabled"
    /> -->
    <view style="margin-left: 8px; font-size: 14px">{{
      value[0].content
    }}</view>
  </view>
</template>
<script setup>
import { reactive } from "vue";
const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
const state = reactive({});
</script>
<style lang="scss">
.globa-input {
  .input-text[disabled] {
    color: #1d1e1e !important;
  }
  input {
    color: #1d1e1e !important;
  }
}
</style>
