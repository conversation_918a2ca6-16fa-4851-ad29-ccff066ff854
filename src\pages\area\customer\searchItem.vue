<template>
  <view class="box">
    <view class="left">
      <view class="leftTop">
        <view class="insDeptName">{{ props.item.customerName }}</view>
        <view
          class="line"
          v-if="props.item.customerName && props.item.insDeptName"
        />
        <view class="insDeptName">{{ props.item.insDeptName }}</view>
      </view>
      <view class="icon-flex">
        <view class="icon" v-if="props.item.addable == 0" key="1">本机构</view>
        <view class="icon" v-if="props.item.changeable == 1" key="2"
          >本辖区</view
        >
      </view>
      <view class="ad">{{ props.item.insName }}</view>
    </view>
    <view class="right">
      <!-- <view
        @click="emit('buttonChanged', 1, item)"
        class="righeItem"
        key="1"
        v-if="props.item.changeable == 1"
        >客户变更</view
      > -->
      <view
        @click="emit('buttonChanged', 2, item)"
        class="righeItem"
        key="2"
        v-if="props.item.changeable == 1"
        >编辑产品</view
      >
      <view
        @click="emit('buttonChanged', 3, item)"
        class="righeItem"
        key="3"
        v-if="props.item.claimable == 1"
        >申请认领</view
      >
      <view
        @click="emit('buttonChanged', 4, item)"
        class="righeItem"
        key="4"
        v-if="props.item.addable == 1"
        >添加执业点</view
      >
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(["buttonChanged"]);

import { defineProps } from "vue";
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
});
</script>

<style lang="scss" scoped>
.line {
  margin-top: 4px;
  margin-right: 8px;
  height: 14px;
  width: 1px;
  background: #869199;
}
.righeItem {
  width: 102px;
  height: 32px;
  background: #e8f0ff;
  border-radius: 100px 100px 100px 100px;
  border: 1px solid #e8f0ff;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #2551f2;
  line-height: 33px;
  text-align: center;
  margin: 4px 0;
}
.icon {
  padding: 0 4px;
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #94bfff;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #2551f2;
  line-height: 17px;
  text-align: center;
  margin-right: 5px;
  width: fit-content;
}
.insDeptName {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #1d212b;
  line-height: 21px;
  margin-right: 8px;
}
.customerName {
}
.ad {
  margin-top: 10px;

  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #869199;
  line-height: 16px;
  text-align: left;
}
.icon-flex {
  display: flex;
  margin-top: 10px;
}
.left {
  align-content: center;
}
.leftTop {
  display: flex;
}
.box {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
}
.right {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
</style>
