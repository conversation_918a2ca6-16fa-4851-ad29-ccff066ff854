<template>
  <view class="cusDeatil">
    <view class="section">
      <view class="cus-information">
        <view class="information-name">
          <view
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <view style="display: flex; align-items: center">
              <view
                style="
                  margin-right: 8px;
                  color: #1d212b;
                  font-size: 20px;
                  font-weight: 500;
                "
                >{{ state.info.customerName }}</view
              >

              <nut-tag
                plain
                :color="getcolorProfession(state.info.professionTech)"
                :text-color="getTextProfession(state.info.professionTech)"
                v-if="state.info.professionTech"
              >
                {{ state.info.professionTech }}
              </nut-tag>
              <img
                :src="vector"
                alt=""
                class="sexIcon"
                v-if="state.info.sex === '1'"
              />
              <img
                :src="man"
                alt=""
                class="sexIcon"
                v-if="state.info.sex === '0'"
              />
            </view>
          </view>
        </view>

        <view
          ><img :src="cus_ins" alt="" class="insIcon" />{{
            state.info.insName
          }}</view
        >
        <view v-if="state.info.insDeptName"
          >科室： {{ state.info.insDeptName }}</view
        >
        <view
          style="color: #869199; font-size: 14px"
          v-if="state.info.mgr && state.info.mgr.length > 0"
          >代表：{{ state.info.mgr.map((item) => item.value).join("、") }}
        </view>
      </view>
    </view>
    <view>
      <nut-tabs
        v-model="state.currentTab"
        backgroud="#FFFFFF"
        @change="tabChange"
      >
    <nut-tab-pane title="基础信息" pane-key="0">
      <CusTabDetail ref="cusDetailRef" />
    </nut-tab-pane>
    <nut-tab-pane title="详细信息" pane-key="1" v-if=props.appcode>
      <CusDetailInfo ref="cusDetailInfoRef" :handleNumShow="handleNumShow"/>
    </nut-tab-pane>
      </nut-tabs>
    </view>

    <FooterButton
      text="编辑产品"
      :plain="true"
      textColor="#2551f2"
      @click-button="clickButton"
    >
      <template #icon>
        <img :src="arrowRight" alt="" style="width: 20px; height: 15px" />
      </template>
    </FooterButton>
<GlobalDialog ref="globalDialogRef" title="输入数值"  subTetx="提交申请" @confirm="numConfirm">
  <view class="num-input">
    <view style="color: red;margin-right: 4px;">*</view>
        <nut-input
          v-model="state.monthlyOutpatient"
          class="nut-input-text"
          placeholder="请输入数值"
          type="number"
        >
        </nut-input>
       </view> 
    
</GlobalDialog>

  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import arrowRight from "../../../images/arrow-right.png";
import arrowRight2 from "../../../images/arrow-right_2.png";
import { customerDetail,monthlyOutpatient } from "../../../api/area.js";
import vector from "../../../images/woman.png";
import man from "../../../images/man.png";
import cus_ins from "../../../images/cus_ins.png";
import { Edit } from "@nutui/icons-vue-taro";
import CusTabDetail from "../components/cusTabDetail.vue";
import {
  getPostCode,
  getcolorProfession,
  getTextProfession,
} from "../../../utils/area.js";
import CusDetailInfo from "../components/cusDetailInfo.vue";
import GlobalDialog from "../../../pages/components/globalDialog/index.vue";
import { appCode, tenantId } from "../../../utils/content";
const props = defineProps({
  appcode:{
    type:String,
    default:''
    
  }
})
const router = useRouter();
const formData = ref({})
const query = router.params;
const cusDetailRef = ref(null);
const cusDetailInfoRef = ref(null)
const globalDialogRef = ref(null)
const initHrInfo = Taro.getStorageSync("initHrInfo");
const state = reactive({
  activekeys: "1",
  id: query.id,
  info: {},
  postUseName: "",
  list: [],
  showFooterPopupVisible: false,
  currentTab:0,
  numShow:false,
  num:11,
  monthlyOutpatient:null,
});

const getDetail = async () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  let params = {
    jurCode: query.jurCode,
    insCode: query.insCode,
    customerCode: query.customerCode,
  };
  console.log(query.claiming, "query.claiming");
  params.isClaim = "1";
  const res = await customerDetail(params);
  state.info = res.data;

  cusDetailRef.value.setInfo(res.data);
  if(props.appcode) {
    cusDetailInfoRef.value.setInfo(res.data);
  }


  Taro.hideLoading();
};

const clickButton = () => {
  Taro.setStorage({ key: "detailInfo", data: state.info });
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  const org = initHrInfo.orgList?.filter((item) => item.postType === "1")[0];
  Taro.navigateTo({
    url: `/pages/area/components/editProduct?type=cust`,
  });
};
const handleNumShow = () => {
  state.monthlyOutpatient = state.info.monthlyOutpatient
  globalDialogRef.value.open()
}
const numConfirm = async () => {
  if(!state.monthlyOutpatient &&  state.monthlyOutpatient !==0) {
   return Taro.showToast({
        title: "请输入数值",
        icon: "none",
        duration: 2000,
      });
  }
  Taro.showLoading({
    mask: true,
    title: "提交中",
  });
  const applicantInfo = {
          applicant: initHrInfo.empName,
          applicantCode: initHrInfo.empCode,
          postIdList: initHrInfo.postIdList,
          postCode: state.info.postCode,
          postName: state.info.postName,
          deptCode:state.info.deptCode,
          deptName: state.info.deptName,
          ancestors: state.info.ancestors,
          
  }  

  const params = {
    applyContent: {
          ...state.info,
          monthlyOutpatient: state.monthlyOutpatient,
          ...applicantInfo,
        },
        ...applicantInfo,
        customerCode: state.info.customerCode || "",
        enableWorkflow: true,
        customerName: state.info.customerName,
        insCode: state.info.insCode,
        insName: state.info.insName,
        insMdmCode: state.info.insMdmCode,
        appCode: appCode(),
        tenantId: tenantId,
        applyType:'8',
        jurCode: state.info.jurCode
      }
  const res = await monthlyOutpatient(params)
  Taro.hideLoading();
  Taro.showToast({
     title: "提交成功",
     icon: "none",
     duration: 3000,
  });
  globalDialogRef.value.cancel()
}
useDidShow(async () => {
  Taro.setNavigationBarTitle({ title: "客户详情" });
  await getDetail();
});
</script>

<style lang="scss">
.cusDeatil {
  padding-bottom: 93px;
  // background: #fff;
  .section {
    font-size: 14px;
    color: #869199;
    background: #fff;

    padding: 16px;
    .cus-information {
      padding: 16px;
      background: #f4f5f7;
      background-image: url("../../../images/card-bg.png");
      background-size: 100% 100%;
      .information-name {
        .sexIcon {
          display: inline-block;
          margin-left: 5px;
          width: 18px;
          height: 18px;
          vertical-align: text-bottom;
        }

        .hosType {
          height: 22px;
          border-radius: 2px 2px 2px 2px;
          border: 1px solid #94bfff;

          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #2551f2;
          line-height: 22px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 4px;
        }
      }
      .insIcon {
        display: inline-block;
        margin-right: 6px;
        width: 18px;
        height: 18px;
        vertical-align: text-bottom;
      }
    }
  }
  .tabs-box {
    margin-bottom: 20px;
    .tabs-box-quest-list-item {
      background-color: #fff;
      margin-bottom: 12px;
      padding-left: 16px;
      padding-top: 12px;
      border-radius: 4px;
    }
  }
  .nut-tab-pane {
    padding: 0;
    background: none;
    padding: 12px;
  }

  .title {
    width: max-content;
    height: 24px;
    //background: #E8F0FF;
    border-radius: 2px 2px 2px 2px;
    //padding: 4px 6px 3px;
    display: flex;
    justify-content: center;
    align-self: center;

    .font {
      height: 17px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 12px;
      color: #2551f2;
      line-height: 17px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-left: 6px;
      margin-right: 6px;
      display: flex;
      justify-content: center;
      align-self: center;
    }
  }
 .num-input {
  font-size: 16px;
  text-align: center;
  margin-top: 8px;
  color: #2551f2;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  .nut-input {
    background: #F3F4F5;
    padding: 7px 0;

  }
}

}
</style>
