<template>
  <view class="bg-white px-[16px]">
    <view>
      <view class="text-[#1D212B] text-[14px] py-[10px]">基本信息</view>
      <view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >药店名称</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.dsName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >药店别名</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.dsNameAlias
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >药店类型</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.dsType
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >区域类型</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.dsAreaType
          }}</view>
        </view>

        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >是否医保定点</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.fixedPoint
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >省市区</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.districtValue
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >地址</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.address
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >法人</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.legalPerson
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >统一社会信用代码</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.businessTerm
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >经营期限</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.businessTerm
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >上级药店</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.superiorDsName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >上级药店编码</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.superiorDsMdmCode
          }}</view>
        </view>
      </view>
    </view>
    <view class="pt-[16px]">
      <view class="text-[#1D212B] text-[14px] py-[10px]">经营信息</view>
      <view class="bg-white rounded-[8px] mx-[8px] mb-[16px]">
        <view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >药店性质</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.dsNature
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >支付类型</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.economicType
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >经营范围</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.businessScope
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >执药师数量</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.docNum
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >营业时间</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.openingTime
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >周六日(含节假日)营业时间</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.weekOpeningTime
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >药店电话</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.dsContactTel
            }}</view>
          </view>
          <view class="flex items-top justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >专用药师手机号</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.docContactTel
            }}</view>
          </view>

          <view class="flex flex-col mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 mb-[16px]"
              >照片(营业执照、经营许可证、门头照)</view
            >
            <view class="flex">
              <img
                v-for="(item, index) in props.formData.ossIdList"
                :key="item"
                :src="item"
                alt=""
                style="width: 80px; height: 80px; margin-right: 4px"
                @click="handlePreview(index)"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
    <nut-image-preview
      :autoplay="0"
      :show="showPreview"
      :images="state.fileList"
      @close="hideImgPreview"
      :init-no="imgPreviewIndex"
    />
  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref, computed } from "vue";
import { imageUrl } from "@/bpm-temp/src/api/bpm";

const router = useRouter();
const query = router.params;

const props = defineProps({
  formData: {
    type: Object,
    default: {},
  },
});
const options = ref([]);
const state = reactive({
  info: {},
  specList: [],
  fileList: [],
});
const showPreview = ref(false);
const imgPreviewIndex = ref(0);
const handlePreview = (index) => {
  imgPreviewIndex.value = index;
  showImgPreview();
};
const showImgPreview = () => {
  showPreview.value = true;
};

const hideImgPreview = () => {
  showPreview.value = false;
};

onMounted(() => {
  props.formData = props.formData || {};
  console.log("props.formData", props.formData);
  if (props.formData.fileList) {
    props.formData.ossIdList = [];
    props.formData.fileList.forEach(async (i) => {
      const res = await imageUrl(i.ossId);
      props.formData.ossIdList.push(res.data.rows[0].url);
      state.fileList.push({ src: res.data.rows[0].url });
    });
  }
});
</script>

<style lang="scss"></style>
