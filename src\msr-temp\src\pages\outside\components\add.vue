<template>
  <view class="outside-add-form">
    <view class="outside-add" style="margin-top: 16px">
      <view class="top-time">
        <view style="display: flex; align-items: center">
          <view v-if="initUserInfo.avatar"
            ><img :src="avatar" style="width: 48px; height: 48px"
          /></view>
          <view class="nameAvatar" v-else>{{
            initUserInfo.nickName.slice(-2)
          }}</view>

          <view class="sign-name">
            <view class="name">{{ initUserInfo.nickName }}</view>
            <view class="time">{{ state.dateTime }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="add-form">
      <nut-form
        class="form"
        :label-width="200"
        :model-value="state.params"
        ref="ruleForm"
      >
        <nut-form-item
          label="岗位"
          prop="type"
          :label-width="100"
          required
          :rules="[{ required: true, message: '请选择' }]"
        >
          <nut-input
            clearable
            input-align="right"
            :border="false"
            readonly
            @click="selectClick('post')"
            v-if="!state.params.postName"
            placeholder="请选择"
          >
            <template #right>
              <img :src="arrow" class="arrowIcon" />
            </template>
          </nut-input>
          <view
            v-else
            @click="selectClick('post')"
            style="font-size: 16px; text-align: right"
            >{{ state.params.postName }}</view
          >
        </nut-form-item>
        <nut-form-item
          label="类型"
          prop="type"
          :label-width="100"
          required
          :rules="[{ required: true, message: '请选择类型' }]"
        >
          <nut-input
            clearable
            input-align="right"
            :border="false"
            readonly
            @click="selectClick('type')"
            v-if="!state.params.typeName"
            placeholder="请选择"
          >
            <template #right>
              <img :src="arrow" class="arrowIcon" />
            </template>
          </nut-input>
          <view
            v-else
            @click="selectClick('type')"
            style="font-size: 16px; text-align: right"
            >{{ state.params.typeName }}</view
          >
        </nut-form-item>
        <nut-form-item
          label="开始时间"
          prop="startTime"
          :label-width="100"
          required
          :rules="[{ required: true, message: '请选择开始时间' }]"
        >
          <nut-input
            clearable
            input-align="right"
            :border="false"
            readonly
            @click="selectClick('start')"
            v-if="!state.params.startTime"
            placeholder="请选择"
          >
            <template #right>
              <img :src="arrow" class="arrowIcon" />
            </template>
          </nut-input>
          <view
            v-else
            @click="selectClick('start')"
            style="font-size: 16px; text-align: right"
            >{{ getTime(state.params.startTime) }}</view
          >
        </nut-form-item>
        <nut-form-item
          label="结束时间"
          prop="endTime"
          :label-width="100"
          required
          :rules="[{ required: true, message: '请选择结束时间' }]"
        >
          <nut-input
            clearable
            input-align="right"
            :border="false"
            readonly
            @click="selectClick('end')"
            v-if="!state.params.endTime"
            placeholder="请选择"
          >
            <template #right>
              <img :src="arrow" class="arrowIcon" />
            </template>
          </nut-input>
          <view
            v-else
            @click="selectClick('end')"
            style="font-size: 16px; text-align: right"
            >{{ getTime(state.params.endTime) }}</view
          >
        </nut-form-item>
        <view class="reason">
          <div style="">备注</div>

          <nut-textarea
            v-model="state.params.reason"
            limit-show
            max-length="200"
            placeholder="请输入"
          />
        </view>
      </nut-form>
    </view>
    <FooterButton text="提交" @click-button="clickButton" />
  </view>
  <PopupRadio ref="typePopupRef" @radio-confirm="radioConfirm" />
  <StartTime ref="startTimeRef" @startTime="selectStartTime" />
  <EndTime ref="endTimeRef" @endTime="selectEndTime" />
</template>
<script setup>
import { onMounted, reactive, ref, onUnmounted } from "vue";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import dayjs from "dayjs";
import arrow from "../../../images/arrow.png";
import "dayjs/locale/zh-cn";
import PopupRadio from "../../../pages/components/popupRadio/popupRadio";

import FooterButton from "../../../pages/components/footerButton/index";
import StartTime from "./startTime";
import EndTime from "./endTime";
import { validApply } from "../../../api/outside";
import { getProcessListApi, appId, h5Url, pcUrl } from "../../../utils/content";
const initUserInfo = Taro.getStorageSync("initUserInfo").user;
const initHrInfo = Taro.getStorageSync("initHrInfo");
const orgList = initHrInfo.orgList.map((item) => {
  return {
    dictLabel: `${item.value}-${item.name}`,
    dictValue: item.id,
  };
});
const list = Taro.getStorageSync("outsideTypeList");
const startTimeRef = ref(null);
const endTimeRef = ref(null);
const typePopupRef = ref(null);
const ruleForm = ref(null);
const feishuNotice = ref({
  appUrl: `${h5Url}`,
  pcUrl: `${pcUrl}`,
});
const state = reactive({
  dateTime: dayjs().format("YYYY-MM-DD"),
  type: "",
  params: {
    type: "",
    typeName: "",
    applyType: "valid_day",
    reason: "",
    startTime: null,
    endTime: null,
    days: "",
    processId: "",
    postName: "",
    postId: "",
  },
});

const selectClick = (type) => {
  state.type = type;
  if (type === "type") {
    typePopupRef.value.open(list, state.params.type);
  }
  if (type === "start") {
    startTimeRef.value.open(state.params.startTime);
  }
  if (type === "end") {
    if (!state.params.startTime) {
      return Taro.showToast({
        title: "请先选择开始时间",
        icon: "none",
        duration: 2000,
      });
    }
    endTimeRef.value.open(state.params.startTime);
  }
  if (type === "post") {
    typePopupRef.value.open(orgList, state.params.postId);
  }
};

const radioConfirm = (val) => {
  if (state.type === "type") {
    state.params.typeName = list.filter(
      (item) => item.dictValue === val
    )[0].dictLabel;
    state.params.type = val;
  }
  if (state.type === "post") {
    state.params.postName = orgList.filter(
      (item) => item.dictValue === val
    )[0].dictLabel;
    state.params.postId = val;
  }
};
const getTimeFlge = (dateArray1, dateArray2) => {
  let dateString1 = dateArray1.slice(0, -1).join("-");
  let dateString2 = dateArray2.slice(0, -1).join("-");
  const start = dayjs(dateString1);
  const end = dayjs(dateString2);
  if (end < start) {
    Taro.showModal({
      content: "结束时间不能小于开始时间",
      showCancel: false,
    });

    return false;
  } else if (
    start.isSame(end, "day") &&
    dateArray1.slice(-1)[0] === "12" &&
    dateArray2.slice(-1)[0] === "00"
  ) {
    Taro.showModal({
      content: "结束时间不能小于开始时间",
      showCancel: false,
    });
    return false;
  } else {
    return true;
  }
};
const getDays = (dateArray1, dateArray2) => {
  let dateString1 = dateArray1.slice(0, -1).join("-");
  let dateString2 = dateArray2.slice(0, -1).join("-");
  const date1 = dayjs(dateString1);
  const date2 = dayjs(dateString2);
  const diffInDays = date2.diff(date1, "day");
  let days = Number(diffInDays - 1);

  if (dateArray1.slice(-1)[0] === "00") {
    days = days + 1;
  }
  if (dateArray1.slice(-1)[0] === "12") {
    days = days + 0.5;
  }
  if (dateArray2.slice(-1)[0] === "00") {
    days = days + 0.5;
  }
  if (dateArray2.slice(-1)[0] === "12") {
    days = days + 1;
  }
  state.params.days = days;
};
const selectStartTime = (list) => {
  if (!state.params.endTime) {
    state.params.startTime = list;
  } else {
    if (getTimeFlge(list, state.params.endTime)) {
      state.params.startTime = list;
      getDays(state.params.startTime, state.params.endTime);
    }
  }
};
const getTime = (list) => {
  if (!list) return;
  const time = list.slice(0, -1).join("-");
  if (list.slice(-1) == "00") {
    return `${time} 上午`;
  } else {
    return `${time} 下午`;
  }
};
const selectEndTime = (list) => {
  if (getTimeFlge(state.params.startTime, list)) {
    state.params.endTime = list;
    getDays(state.params.startTime, state.params.endTime);
  }
};

const clickButton = async () => {
  ruleForm.value.validate().then(async ({ valid, errors }) => {
    if (valid) {
      Taro.showLoading({
        mask: true,
        title: "加载中",
      });
      state.params = {
        ...state.params,
        ...feishuNotice.value,
      };
      const res = await validApply(state.params);
      Taro.hideLoading();

      Taro.showToast({
        title: "提交成功",
        icon: "none",
        duration: 2000,
      });
      Taro.navigateBack({
        delta: 1,
      });
    } else {
      console.log("error submit!!", errors);
    }
  });
};

const getKey = async () => {
  state.params.processId = await getProcessListApi();
};
onMounted(() => {
  Taro.setNavigationBarTitle({ title: "区域外时间" });
  getKey();
});
</script>
<style lang="scss">
.outside-add-form {
  .outside-add {
    position: relative;
    margin: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 10px 16px;
    border-radius: 8px;
    margin-top: 0;

    .nameAvatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #92a8f8;
      color: #fff;
      line-height: 50px;
      text-align: center;
    }

    img {
      width: 48px;
      height: 48px;
      vertical-align: middle;
    }

    .sign-name {
      margin-left: 8px;
      font-size: 18px;

      .name {
        font-weight: 500;
      }
    }

    .time {
      color: #869199;
      font-size: 12px;
    }
  }

  .arrowIcon {
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }

  .reason {
    color: #1d212b;
    font-size: 16px;
    padding-top: 12px;
    .nut-cell {
      display: block;
    }

    .nut-textarea {
      margin-top: 8px;
    }
  }

  .add-form {
    background-color: #fff;
    padding: 12px;
    border-radius: 8px;
    margin: 0 12px 12px 12px;
    .input-text::placeholder {
      color: #c9cdd0;
    }
    .nut-form-item__label {
      font-size: 16px;
    }
    .nut-cell-group__wrap {
      box-shadow: none;
    }
    .nut-cell__title {
      color: #1d212b;
    }
    .nut-input-left-box {
      margin-right: 10px;
    }
    .nut-input {
      padding: 0;
      border-bottom: none;
    }
  }
  .input-text::placeholder {
    color: #c9cdd0;
    word-break: break-all;
  }
}
</style>
