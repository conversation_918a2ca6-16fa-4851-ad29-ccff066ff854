<!-- 机构列表页 -->
<template>
  <view class="pharmacy">
    <SearchBar
      @seacrh-top="topInsSearch"
      @filters="insFilters"
      placeholder="请输入机构名称或药店名称"
      :active="state.activeFlag"
    />
    <!-- <SearchBar
      @seacrh-top="topCusSearch"
      @filters="cusFilters"
      placeholder="请输入机构名称/客户名称/科室名称"
      v-show="state.currentTab === '1'"
      :active="state.approveActiveFlag"
    /> -->
    <view>
      <InsList ref="insListRef" @change-pcdvalue="changePcdvalue" />
      <!-- <nut-tabs
        v-model="state.currentTab"
        backgroud="#FFFFFF"
        @change="tabChange"
      >
        <nut-tab-pane title="关联药店" pane-key="0">
          <InsList
            ref="insListRef"
            :tab="state.currentTab"
            @change-pcdvalue="changePcdvalue"
          />
        </nut-tab-pane>
        <nut-tab-pane title="申请单" pane-key="1">
          <ApproveList
            ref="approveListRef"
            :tab="state.currentTab"
            @change-filter="changeApprove"
          />
        </nut-tab-pane>
      </nut-tabs> -->
    </view>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow, useDidHide } from "@tarojs/taro";
import { ref, onMounted, reactive, onUnmounted, onBeforeUnmount } from "vue";
import InsList from "./ins/instList.vue";
// import ApproveList from "./approve/approveList.vue";
import plus from "../../images/inst_plus.png";

import SearchBar from "../../pages/components/searchBar";

const insListRef = ref(null);
const approveListRef = ref(null);
const state = reactive({
  currentTab: "0",
  activeFlag: false,
  approveActiveFlag: false,
  previousUrl: "",
});

const changePcdvalue = (value) => {
  if (value) {
    state.activeFlag = true;
  } else {
    state.activeFlag = false;
  }
};
const changeApprove = (value) => {
  console.log("2222", value);
  if (value) {
    state.approveActiveFlag = true;
  } else {
    state.approveActiveFlag = false;
  }
};

const topInsSearch = (search) => {
  insListRef.value.topSearch(search);
};

const topCusSearch = (search) => {
  approveListRef.value.topSearch(search);
};
const insFilters = () => {
  insListRef.value.insFilters();
};
const cusFilters = (search) => {
  approveListRef.value.approveFilters(search);
};

const tabChange = () => {
  insListRef.value.initInsList();
};

const searchPharmacy = () => {
  Taro.navigateTo({
    url: `/pages/pharmacy/components/search`,
  });
};

useDidShow(() => {
  Taro.setNavigationBarTitle({ title: "关联药店" });
  tabChange();
});
</script>

<style lang="scss">
input::placeholder {
  color: yellow;
}

.pharmacy {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100vw;
  overflow: hidden;
  .searchBar {
    background: #fff;
  }
  .oval {
    background-image: url("../../images/inst_oval.png");
    background-size: cover;
    background-repeat: no-repeat;

    width: 75px;
    height: 75px;
    margin-right: 24px;
    display: inline-block;
    text-align: center;
  }

  .nut-tabs__titles-item__line {
    background: blue;
  }

  .nut-tab-pane {
    background-color: transparent;
    padding: 18px 0px;
  }
  .nut-tab-pane {
    padding: 0 0;
  }
}
</style>
