<!-- 肿瘤CRM小程序头部导航栏 -->
<template>
    <view class="navBar">
        <nut-navbar left-show @click-back="back">
            <template #content>
                <slot name="titleContent"></slot>
            </template>
        </nut-navbar>
    </view>
</template>
<script setup>
import { defineEmits, defineProps, ref } from 'vue'
const emit = defineEmits(['click-back'])
const props = defineProps({

})

const back = () => {
    emit('click-back')
}

</script>
<style lang="scss">
.navBar {
    height: 44px;
    background: linear-gradient(180deg, #D2E7FF 0%, #FFFFFF 96%);
}

.nut-navbar {
    background-color: transparent;
}
</style>
