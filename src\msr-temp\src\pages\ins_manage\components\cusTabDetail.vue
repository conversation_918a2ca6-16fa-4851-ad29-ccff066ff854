<template>
  <view class="cusTabDetail">
    <view style="font-size: 16px; color: #1d212b">客户详情</view>
    <view class="cusDetail-item">
      <view class="cusDetail-item-title">
        <view class="label">姓名</view>
        <view class="value"
          ><text>{{ state.info.customerName }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">机构名称</view>
        <view class="value"
          ><text>{{ state.info.insName }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">是否主执业点</view>
        <view class="value"
          ><text>{{ state.info.masterFlag === "0" ? "否" : "是" }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">标准科室</view>
        <view class="value"
          ><text>{{ state.info.insDeptName }}</text></view
        >
      </view>
      <!-- 缺少 -->
      <!-- <view class="cusDetail-item-title">
        <view class="label">科室标签</view>
        <view class="value"
          ><text>{{ state.info.insDeptTag }}</text></view
        >
      </view> -->
      <view class="cusDetail-item-title">
        <view class="label">行政级别</view>
        <view class="value"
          ><text>{{ state.info.administrationLevel }}</text></view
        >
      </view>

      <view class="cusDetail-item-title">
        <view class="label">职称</view>
        <view class="value"
          ><text>{{ state.info.professionTech }}</text></view
        >
      </view>

      <view class="cusDetail-item-title">
        <view class="label">产品</view>
        <view class="value"
          ><text>{{ getDetailProduct(state.info.productList) }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">省份</view>
        <view class="value"
          ><text>{{ state.info.province }}</text></view
        >
      </view>
      <!-- 缺少 -->
      <view class="cusDetail-item-title">
        <view class="label">性别</view>
        <view class="value"
          ><text>{{
            state.info.sex === "1" ? "女" : state.info.sex === "0" ? "男" : ""
          }}</text></view
        >
      </view>
      <!-- 缺少 -->
      <view class="cusDetail-item-title">
        <view class="label">备注</view>
        <view class="value"
          ><text>{{ state.info.remark }}</text></view
        >
      </view>
    </view>
  </view>
</template>
<script setup>
import { View } from "@tarojs/components";
import { reactive } from "vue";
import { getDetailProduct } from "../../../utils/area.js";
const state = reactive({
  info: {},
});

const setInfo = (info) => {
  state.info = info;
};

defineExpose({
  setInfo,
});
</script>
<style lang="scss">
.cusTabDetail::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}
.cusTabDetail {
  font-size: 14px;
  color: #869199;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  overflow-y: scroll;

  .title {
    font-size: 20px;
    color: #1d212b;
  }
  .cusDetail-item {
    margin-top: 10px;
    .cusDetail-item-title {
      display: flex;
      align-items: center;
      padding: 6px 0;
      .label {
        width: 30%;
      }
      .value {
        color: #1d212b;
        width: 70%;
        display: block;
      }
    }
  }
}
</style>
