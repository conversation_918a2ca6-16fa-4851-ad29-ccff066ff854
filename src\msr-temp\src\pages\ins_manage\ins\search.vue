<template>
  <view class="search-ins">
    <view class="section">
      <nut-input
        v-model="state.params.insName"
        placeholder="请输入"
        clearable
        input-align
        :border="false"
        :formatter="formatter"
      >
        <template #left>
          <view class="label">机构名称</view>
        </template>
      </nut-input>
      <view class="footer-button">
        <nut-row type="flex" justify="center">
          <nut-col :span="24">
            <nut-button
              color="#2551F2"
              @click="clickSearchButton"
              shape="square"
              block
              :disabled="!state.params.insName"
              >搜索</nut-button
            >
          </nut-col>
        </nut-row>
      </view>
    </view>
    <view class="result" v-if="state.insList.length">搜索结果</view>
    <view class="result-sub" v-if="state.insList.length"
      >以下机构在系统中已存在，请前往我的辖区申请认领</view
    >

    <view
      class="result"
      v-if="!state.insList.length && num !== 0 && !state.loading"
      >搜索未找到该机构，可申请添加</view
    >
    <view class="result-text" style="font-size: 16px">
      <scroll-view
        style="height: 500px"
        :scroll-y="true"
        :scroll-top="state.scrollTop"
        @scrolltolower="onScrollToLower"
      >
        <view v-for="item in state.insList" :key="item.jurId" class="instItem">
          <view>
            <view
              style="
                display: flex;
                justify-content: space-between;
                vertical-align: middle;
                align-items: center;
                padding: 10px;
              "
            >
              <view
                style="
                  display: flex;
                  flex-wrap: wrap;
                  align-items: center;
                  gap: 5px;
                "
              >
                <view class="insName">{{ item.insName }}</view>
                <view
                  v-show="item.insMdmCode"
                  style="
                    font-size: 12px;
                    color: #2551f2;
                    border: 1px solid #2551f2;
                    padding: 0 4px;
                    display: inline-block;
                    height: 20px;
                    line-height: 20px;
                  "
                  >{{ item.insMdmCode }}</view
                >
              </view>

              <view
                v-if="item.isMyClaim == 0"
                class="righeItem"
                @click="goDetail(item)"
                >查看详情</view
              >
            </view>
            <view
              style="
                text-align: left;
                padding-left: 10px;
                color: #999999;
                font-size: 14px;
                margin-bottom: 4px;
              "
              >{{ item.province }}{{ item.city }}{{ item.district
              }}{{ item.address }}</view
            >
          </view>
        </view>
        <view
          v-if="state.insList.length > 0 && state.insList.length == state.total"
          style="color: #869199; font-size: 16px; text-align: center"
          >已全部加载完毕</view
        >
      </scroll-view>
    </view>

    <FooterButton
      text="申请添加机构"
      @click-button="clickAddButton"
      :buttonIcon="arrowRight"
      v-if="num !== 0 || state.params.insName"
      :disabled="num === 0 || !state.params.insName"
    >
      <template #icon>
        <Uploader color="#fff" />
      </template>
    </FooterButton>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { onMounted, reactive, watch, ref } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { Uploader } from "@nutui/icons-vue-taro";
import arrowRight from "../../../images/arrow-right.png";

import { formatter } from "../../../utils/customer.js";
import { crmApplyList } from "../../../api/ins.js";
import {
  getPostCode,
  getInsLevelColor,
  getInsLevelTextColor,
} from "../../../utils/area.js";
import { appCode, tenantId, baseApi } from "../../../utils/content";

const ISADE = ["/ade/"].includes(baseApi);
const initHrInfo = Taro.getStorageSync("initHrInfo");
console.log(initHrInfo);
const router = useRouter();
const scrollMyFlag = ref(false);
const num = ref(0);
const query = router.params;
const org = initHrInfo.orgList.filter((item) => item.postType === "1")[0];
const state = reactive({
  footerButtontext: "搜索",
  insList: [],
  scrollTop: 0,
  total: 0,
  pageNum: 1,
  pageSize: 10,
  params: {
    insName: "",
    jurCodeList: getPostCode().split(","),
  },
  loading: false,
});

// 跳转至详情页
const goDetail = (item) => {
  Taro.navigateTo({
    url: `/pages/area/ins/insDeatil?jurCode=${item.jurCode}&insCode=${item.insCode}&claiming=${item.claiming}`,
  });
};

const getinsList = () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  state.loading = true;
  crmApplyList(state.pageNum, state.pageSize, state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.data.total;
        if (!state.insList.length) {
          state.insList = res.data.rows || [];
        } else {
          state.insList = [...state.insList, ...res.data.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = true;
        state.loading = false;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      Taro.hideLoading();
    });
};
const clickSearchButton = () => {
  num.value++;
  if (state.loading) return;
  state.pageNum = 1;
  state.total = 0;
  state.insList = [];
  state.params.insName = state.params.insName.replace(/\s/g, "");
  getinsList();
};

const clickAddButton = () => {
  const flag = state.insList.some(
    (item) => item.insName === state.params.insName
  );
  if (flag) {
    const text = "如需认领，请前往我的辖区-点击认领，搜索该机构进行机构认领";
    const test2 =
      "请仔细核对搜索结果，联系助理认领已有机构或继续添加新机构（添加完新机构后同样需要联系助理认领）";
    Taro.showModal({
      title: "系统中存在同名机构！",
      content: ISADE ? text : test2,
      confirmText: "我知道了",
      showCancel: false,
      duration: 2000,
    });
  } else
    Taro.navigateTo({
      url: `pages/area/ins/addIns?insName=${state.params.insName}&type=add`,
    });
};

const onScrollToLower = () => {
  if (scrollMyFlag.value && state.insList.length < state.total) {
    state.pageNum++;
    scrollMyFlag.value = false;
    getinsList();
  }
};

watch(
  () => state.params.insName,
  (newValue) => {
    if (!newValue) {
      num.value = 0;
      state.insList = [];
    }
  }
);
onMounted(() => {
  Taro.setNavigationBarTitle({ title: "新增机构" });
});
</script>
<style lang="scss">
.taro-modal__title {
  color: #f32f29;
  font-weight: 500;
  font-size: 16px;
}

.taro-modal__text {
  color: #869199;
  font-size: 14px;
  font-weight: 400;
}
.righeItem {
  width: 102px;
  height: 32px;
  background: #e8f0ff;
  border-radius: 100px 100px 100px 100px;
  border: 1px solid #e8f0ff;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #2551f2;
  line-height: 33px;
  text-align: center;
  margin: 4px 0;
}
.search-ins {
  padding-bottom: 80px;

  .section {
    background: #fff;
    font-size: 16px;
    padding: 0 16px;
  }

  .result-text {
    padding-top: 16px;
    text-align: center;
    // background: #eff3f5;
  }

  .result-sub {
    color: #869199;
    padding-top: 14px;
    text-align: center;
    font-size: 14px;
  }

  .result {
    color: #2551f2;
    padding-top: 16px;
    text-align: center;
    font-size: 16px;
  }

  .instItem {
    background-color: #ffffff;
    margin-bottom: 10px;

    .insName {
      font-weight: 500;
      font-size: 16px;
      color: #1d212b;
      text-align: left;
    }
  }

  .label {
    &::before {
      content: "*";
      color: red;
      margin-top: 6px;
      margin-right: 5px;
      vertical-align: middle;
    }
  }

  .nut-radio-group {
    padding: 0 16px;
  }

  .footer-button {
    background-color: #ffffff;
    padding: 8px 0;
    width: 100%;

    .nut-button {
      border-radius: 4px;
    }

    .border-none {
      border: none;
    }

    .nut-button__wrap {
      img {
        width: 10px;
        height: 10px;
      }
    }
  }
}
</style>
