import Taro from "@tarojs/taro";

const request = (params) => {
  const { url, data, method, timeout = 60000, text = false } = params;
  const TokenKey = Taro.getStorageSync("access_token");
  if (data instanceof FormData) {
    delete config.headers["Content-Type"];
  }

  return new Promise((resolve, reject) => {
    Taro.request({
      url: process.env.TARO_APP_API + url,
      data: data,
      method,
      timeout,
      header: {
        Authorization: TokenKey ? `Bearer ${TokenKey}` : "",
        clientid: process.env.TARO_APP_CLIENT_ID,
      },
      success(response) {
        resolve(response.data);
      },
      fail(error) {
        reject(error, "http");
      },
    });
  });
};

export default request;
