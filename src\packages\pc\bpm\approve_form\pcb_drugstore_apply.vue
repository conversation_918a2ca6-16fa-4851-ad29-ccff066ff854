<template>
  <div>
    <div class="text-[#1D212B] text-[14px] py-[10px]">基本信息</div>
    <div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">药店名称</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.dsName
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">药店别名</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.dsNameAlias
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">药店类型</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.dsType
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">区域类型</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.dsAreaType
          }}
        </div>
      </div>

      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">是否医保定点</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.fixedPoint
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">省市区</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.districtValue
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">地址</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.address
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">法人</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.legalPerson
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">统一社会信用代码</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.businessTerm
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">经营期限</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.businessTerm
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">上级药店</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.superiorDsName
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">上级药店编码</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.superiorDsMdmCode
          }}
        </div>
      </div>
    </div>
  </div>
  <div class="pt-[16px]">
    <div class="text-[#1D212B] text-[14px] py-[10px]">经营信息</div>
    <div class="bg-white rounded-[8px] mx-[8px] mb-[16px]">
      <div>
        <div class="flex items-top mb-[8px] justify-between">
          <div class="text-[#869199] text-[14px] font-400 w-[124px]">药店性质</div>
          <div class="text-[#1D212B] text-[14px] font-400 text-right">
            {{
              props.formData.dsNature
            }}
          </div>
        </div>
        <div class="flex items-top mb-[8px] justify-between">
          <div class="text-[#869199] text-[14px] font-400 w-[124px]">支付类型</div>
          <div class="text-[#1D212B] text-[14px] font-400 text-right">
            {{
              props.formData.economicType
            }}
          </div>
        </div>
        <div class="flex items-top mb-[8px] justify-between">
          <div class="text-[#869199] text-[14px] font-400 w-[124px]">经营范围</div>
          <div class="text-[#1D212B] text-[14px] font-400 text-right">
            {{
              props.formData.businessScope
            }}
          </div>
        </div>
        <div class="flex items-top mb-[8px] justify-between">
          <div class="text-[#869199] text-[14px] font-400 w-[124px]">执药师数量</div>
          <div class="text-[#1D212B] text-[14px] font-400 text-right">
            {{
              props.formData.docNum
            }}
          </div>
        </div>
        <div class="flex items-top mb-[8px] justify-between">
          <div class="text-[#869199] text-[14px] font-400 w-[124px]">营业时间</div>
          <div class="text-[#1D212B] text-[14px] font-400 text-right">
            {{
              props.formData.openingTime
            }}
          </div>
        </div>
        <div class="flex items-top mb-[8px] justify-between">
          <div class="text-[#869199] text-[14px] font-400 w-[124px]">周六日(含节假日)营业时间</div>
          <div class="text-[#1D212B] text-[14px] font-400 text-right">
            {{
              props.formData.weekOpeningTime
            }}
          </div>
        </div>
        <div class="flex items-top mb-[8px] justify-between">
          <div class="text-[#869199] text-[14px] font-400 w-[124px]">药店电话</div>
          <div class="text-[#1D212B] text-[14px] font-400 text-right">
            {{
              props.formData.dsContactTel
            }}
          </div>
        </div>
        <div class="flex items-top justify-between">
          <div class="text-[#869199] text-[14px] font-400 w-[124px]">专用药师手机号</div>
          <div class="text-[#1D212B] text-[14px] font-400 text-right">
            {{
              props.formData.docContactTel
            }}
          </div>
        </div>

        <div class="flex flex-col mb-[8px]">
          <div class="text-[#869199] text-[14px] font-400 mb-[16px]">照片(营业执照、经营许可证、门头照)</div>
          <div style="display: flex">
            <div style="margin-right: 20px" v-for="(item,index) in state.fileList" :key="index">
              <el-image :initial-index="index" :preview-src-list="state.fileList" style="width: 300px; height: 200px;" :src="item" fit="fill" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>


import { dowTemplte } from '@/bpm/src/api/flow/index';
const router = useRouter();
const query = router.params;

const props = defineProps({
  formData: {
    type: Object,
    default: {},
  },
});
const options = ref([]);
const state = reactive({
  info: {},
  specList: [],
  fileList: [],
});
const showPrediv = ref(false);
const imgPredivIndex = ref(0);
const handlePrediv = (index) => {
  imgPredivIndex.value = index;
  showImgPrediv();
};
const showImgPrediv = () => {
  showPrediv.value = true;
};

const hideImgPrediv = () => {
  showPrediv.value = false;
};

onMounted(() => {
 if( props.formData.fileList) {
  props.formData.fileList.forEach(async (el) => {
     const res = await dowTemplte(el.ossId);
    state.fileList.push(res.data.rows?.[0]?.url);
  })
  }
});
</script>

<style lang="scss"></style>
