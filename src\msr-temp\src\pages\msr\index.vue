<template>
  <view class="index"></view>
</template>

<script setup>
import Taro, { useRouter } from "@tarojs/taro";
import { reactive, onMounted } from "vue";
import { appletLogin, getUserInfo, hrDetail } from "@/api/login.js";
import {
  getSelectObjectiveList,
  getVisitTypeList,
  getAssistPurpose,
} from "@/utils/content.js";
import {
  getDeptNameList,
  getProfessionList,
  geAdministrationList,
  getCusLevelList,
  getCusPotenLevel,
  getDepartmentLabelList,
} from "@/utils/customer";
import {
  getInsLevel,
  getInsprofessionType,
  getEconomicType,
  getInsGrade,
} from "@/utils/ins";
import {
  getPharmacyType,
  getAreaType,
  getPharmacyNature,
  getPaymentType,
  getBusinessScope,
  getApproveType,
  getDevelopType,
} from "@/utils/pharmacy";

import { getDistrict, getApplyFormType } from "@/utils/area.js";
import { appCode, tenantId } from "@/utils/content";
const router = useRouter();
const state = reactive({
  code: "",
});
const init = async () => {
  function getParameterByName(name) {
    console.log("1", name, window.location);
    name = name.replace(/[\[\]]/g, "\\$&");
    console.log("2", name);
    const regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)");
    console.log("3", regex);
    const results = regex.exec(window.location.href);
    console.log("4", results);
    console.log(5, decodeURIComponent(results[2].replace(/\+/g, " ")));
    if (!results) return null;
    if (!results[2]) return "";
    return decodeURIComponent(results[2].replace(/\+/g, " "));
  }
  // if (getParameterByName("code")) {
  //   window.location.href =
  //     "/#/page/crm/index?code=" + getParameterByName("code");
  //   console.log(6, decodeURIComponent(results[2].replace(/\+/g, " ")));
  // }
  Taro.showLoading({
    title: "加载中...",
    icon: "loading",
  });

  const params = {
    source: "FEISHU",
    code: getParameterByName("code"),
    appCode: appCode,
    tenantId: tenantId,
  };
  const loginData = await appletLogin(params);
  if (loginData.code === 200) {
    Taro.setStorageSync("access_token", loginData.data.access_token);
    try {
      const userData = await getUserInfo();
      Taro.setStorage({ key: "initUserInfo", data: userData.data });
      const hrData = await hrDetail({
        appCode: appCode,
        tenantId: tenantId,
      });
      Taro.setStorage({ key: "initHrInfo", data: hrData.data });
      Taro.hideLoading();
      Taro.redirectTo({
        url: `/pages/index/index`,
      });
      goAssistDetail();

      getSelectObjectiveList();
      getVisitTypeList();
      getDeptNameList();
      getProfessionList();
      geAdministrationList();
      getCusLevelList();
      getAssistPurpose();
      getDistrict();
      getCusPotenLevel();
      getDepartmentLabelList();
      getInsLevel();
      getInsprofessionType();
      getEconomicType();
      getPharmacyType();
      getAreaType();
      getPharmacyNature();
      getPaymentType();
      getBusinessScope();
      getApproveType();
      getDevelopType();
      getInsGrade();
      getApplyFormType();
    } catch (e) {
      Taro.hideLoading();
      console.log(e);
    }
  }
  Taro.hideLoading();
};
const goAssistDetail = () => {
  console.log(
    decodeURIComponent(router.params.state).split(","),
    "-router.params.state"
  );
  if (router.params.state) {
    const arr = decodeURIComponent(router.params.state);
    Taro.navigateTo({
      url: `pages/collaborativevisit/assistDetail?visitInstitutionId=${
        arr.split(",")[0]
      }&assistId=${arr.split(",")[1]}`,
    });
  }
};

onMounted(() => {
  Taro.clearStorageSync();
  init();
});
</script>

<style lang="scss"></style>
