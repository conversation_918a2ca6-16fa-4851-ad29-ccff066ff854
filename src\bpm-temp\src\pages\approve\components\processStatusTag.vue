<template>
  <div class="status-tag" :style="{color:task(props.instanceState).textColor,backgroundColor:task(props.instanceState).bgcColor}">{{ task(props.instanceState).text }}</div>
</template>
<script setup>
import { defineProps, } from "vue";
const props = defineProps({
  textColor:{
    type: String,
    default: "#2551F2"
  },
  bgcColor:{
    type: String,
    default: "#E8F0FF"
  },
  instanceState: {
    type: String
  }
})

const task = (instanceState) => {
  //流程实例状态 状态 0，审批中 1，审批通过 2，审批拒绝 3，撤销审批 4，超时结束 5，强制终止
  switch (instanceState) {
    case '0' :
      return {
        text: '审批中',
        textColor: '#FF8800',
        bgcColor: '#FFF3E6'
      }

    case '1' :
      return {
        text: '审批通过',
        textColor: '#34C724',
        bgcColor: '#EBF9E9'
      }

    case '2' :
      return {
        text: '审批拒绝',
        textColor: '#F54A45',
        bgcColor: '#FEEDEC'
      }

    case '3' :
      return {
        text: '撤销审批',
        textColor: '#1D212B',
        bgcColor: '#F3F4F5'
      }

    case '4' :
      return {
        text: '超时结束',
        textColor: '#1D212B',
        bgcColor: '#F3F4F5'
      }

    case '5' :
      return {
        text: '强制终止',
        textColor: '#1D212B',
        bgcColor: '#F3F4F5'
      }
    default :
      return {}
  }
}
</script>
<style lang="less" scoped>
.status-tag {
  height: 22px;
   padding: 0 8px;
   font-size: 14px;
  border-radius: 0 8px 0 8px;
   line-height: 22px;
}
</style>
