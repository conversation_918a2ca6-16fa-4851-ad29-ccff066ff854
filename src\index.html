<!DOCTYPE html>
<html>
  <head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta
      content="width=device-width,initial-scale=1,user-scalable=no"
      name="viewport"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no,address=no" />
    <meta name="apple-mobile-web-app-status-bar-style" content="white" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <script
      src="https://code.jquery.com/jquery-1.12.4.js"
      integrity="sha256-Qw82+bXyGq6MydymqBxNPYTaUXXq7c8v3CwiYwLLNXU="
      crossorigin="anonymous"
    ></script>
    <!-- 引入 JSSDK -->
    <!-- JS 文件版本在升级功能时地址会变化，如有需要（比如使用新增的 API），请重新引用「网页应用开发指南」中的JSSDK链接，确保你当前使用的JSSDK版本是最新的。-->
    <script
      type="text/javascript"
      src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.16.js"
    ></script>
    <script>
      (function (win, export_obj) {
        win["LogAnalyticsObject"] = export_obj;
        if (!win[export_obj]) {
          function _collect() {
            _collect.q.push(arguments);
          }
          _collect.q = _collect.q || [];
          win[export_obj] = _collect;
        }
        win[export_obj].l = +new Date();
      })(window, "collectEvent");

      window.collectEvent("init", {
        app_id: 10000052, // 参考2.1节获取，注意类型是number而非字符串
        channel_domain: "https://snssdk.genscigroup.com", // 设置私有化部署数据上送地址，参考2.2节获取
        log: true, // true:开启日志，false:关闭日志
        autotrack: true, // 全埋点开关，true开启，false关闭
        enable_stay_duration: true,
        spa: true,
      });
      // 此处可添加设置uuid、设置公共属性等代码
      window.collectEvent("start"); // 通知SDK设置完毕，可以真正开始发送事件了
      const getLocation = () => {
        try {
          if ("geolocation" in navigator) {
            navigator.geolocation.getCurrentPosition(
              async (position) => {
                const latitude = position.coords.latitude; // 纬度
                const longitude = position.coords.longitude; // 经度
                console.log(longitude, ",", latitude, "经纬度");
                // if(!latitude || !latitude) return false;
                // if(window.location.pathname === '/pages/login/index') return false;
                window.collectEvent("config", {
                  GS_latitude: latitude,
                  GS_longitude: longitude,
                  GS_timer: new Date().getTime(),
                  GS_empNo:
                    localStorage.getItem("initHrInfo") &&
                    JSON.parse(localStorage.getItem("initHrInfo")) &&
                    JSON.parse(localStorage.getItem("initHrInfo")).data &&
                    JSON.parse(localStorage.getItem("initHrInfo")).data.empCode,
                  GS_empName:
                    localStorage.getItem("initHrInfo") &&
                    JSON.parse(localStorage.getItem("initHrInfo")) &&
                    JSON.parse(localStorage.getItem("initHrInfo")).data &&
                    JSON.parse(localStorage.getItem("initHrInfo")).data.empName,
                  GS_customGap: 1,
                });
                window.collectEvent("position_event", {
                  GS_latitude: latitude,
                  GS_longitude: longitude,
                  GS_timer: new Date().getTime(),
                  GS_empNo:
                    localStorage.getItem("initHrInfo") &&
                    JSON.parse(localStorage.getItem("initHrInfo")) &&
                    JSON.parse(localStorage.getItem("initHrInfo")).data &&
                    JSON.parse(localStorage.getItem("initHrInfo")).data.empCode,
                  GS_empName:
                    localStorage.getItem("initHrInfo") &&
                    JSON.parse(localStorage.getItem("initHrInfo")) &&
                    JSON.parse(localStorage.getItem("initHrInfo")).data &&
                    JSON.parse(localStorage.getItem("initHrInfo")).data.empName,
                  GS_customGap: 1,
                });
                window.collectEvent("send");
                setTimeout(() => {
                  window.collectEvent("config", {
                    GS_customGap: 0,
                  });
                }, 10000);
              },
              function (error) {},
              {}
            );
          } else {
          }
        } catch (e) {
          console.log(e, "定位报错信息");
        }
      };
      getLocation();
      setInterval(() => {
        getLocation();
      }, 15 * 1000 * 60); // 15分钟上报一次
    </script>
    <script
      async
      src="https://lf3-data.volccdn.com/obj/data-static/log-sdk/collect/5.0/collect-privity-v5.1.12.js"
    ></script>
    <title>成人内分泌CRM</title>
    <script>
      if (window.location.hostname.includes("athena-ais")) {
        window.document.title = "免疫事业部CRM";
      } else {
        window.document.title = "成人内分泌CRM<";
      }
    </script>
    <script>
      <%= htmlWebpackPlugin.options.script %>
    </script>
    <script>
      window._AMapSecurityConfig = {
        securityJsCode: "9b4c3dbff424030192879358eab1ece1",
      };
    </script>
    <!-- <script type="text/javascript">
      window._AMapSecurityConfig = {
        serviceHost: `${process.env.TARO_APP_API}/_AMapService`,
      };
    </script> -->
    <script
      type="text/javascript"
      src="https://webapi.amap.com/maps?v=2.0&key=bb5f9b6e971fa4ea858755323732d0fe"
    ></script>
    <script src="//webapi.amap.com/ui/1.1/main.js?v=1.1.1"></script>
  </head>

  <body>
    <div id="app"></div>
  </body>
</html>
