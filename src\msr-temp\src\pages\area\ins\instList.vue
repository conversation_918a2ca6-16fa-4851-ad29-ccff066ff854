<template>
  <view class="insList">
    <view class="title"
      >共{{ state.total }}条机构认领记录<span v-if="!ISGEN"
        >(请注意可能存在重复认领)</span
      >
    </view>
    <scroll-view
      style="height: 500px"
      :scroll-y="true"
      :scroll-top="state.scrollTop"
      @scrolltolower="onScrollToLower"
    >
      <view
        style="
          position: absolute;
          top: 28%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!state.insList.length && scrollMyFlag"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>
      <view v-for="item in state.insList" :key="item.jurId" class="instItem">
        <view
          @click="gotoInst(item)"
          style="
            display: flex;
            flex-direction: column;
            padding: 16px;
            padding-bottom: 12px;
          "
        >
          <view
            style="display: flex; flex-direction: row; align-items: flex-start"
          >
            <!-- <img :src="hospital" style="width: 24px; height: 24px" /> -->
            <view
              style="
                font-weight: 500;
                font-size: 18px;
                color: #1d212b;
                margin-left: 4px;
                -webkit-line-clamp: 2; /* 显示的行数 */
                -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
                overflow: hidden; /* 隐藏超出的内容 */
                text-overflow: ellipsis;
              "
            >
              {{
                item.insName.length > 10
                  ? item.insName.substring(0, 10) + "..."
                  : item.insName
              }}
              <nut-tag
                plain
                color="#94BFFF"
                text-color="#2551F2"
                style="margin-top: -4px; vertical-align: middle"
              >
                {{ item.insMdmCode }}
              </nut-tag></view
            >
            <view style="margin-left: auto">
              <img :src="arrowRight" style="width: 15px; height: 17px" />
            </view>
          </view>
          <view
            style="
              display: flex;
              justify-content: space-between;
              vertical-align: middle;
              align-items: center;
            "
          >
            <view style="display: flex; align-items: center; flex-wrap: wrap">
              <nut-tag
                v-if="item.insGrade || item.insLevel"
                plain
                :color="getInsLevelColor(item.insGrade)"
                :text-color="getInsLevelTextColor(item.insGrade)"
                style="margin-right: 8px; vertical-align: text-top"
              >
                {{ item.insGrade }}{{ item.insLevel }}
              </nut-tag>
              <!-- <text class="hosType" v-if="item.insType">{{
                item.insType
              }}</text> -->
              <nut-tag
                plain
                color="#C6CAD1"
                text-color="#4E595E"
                style="vertical-align: text-top"
                v-if="item.insType"
              >
                {{ item.insType }}
              </nut-tag>
              <nut-tag
                plain
                color="#C6CAD1"
                text-color="#4E595E"
                style="vertical-align: text-top"
                v-if="item.label && ISINSGEN"
              >
                {{ item.label }}
              </nut-tag>
              <nut-tag
                plain
                color="#C6CAD1"
                text-color="#4E595E"
                style="vertical-align: text-top"
                v-if="item.insLabel"
              >
                {{ item.insLabel }}
              </nut-tag>
            </view>

            <view class="productTag" v-if="item.claiming === '1'">
              <Clock color="#FF7D00" size="12" style="vertical-align: middle" />
              <text style="margin-left: 2px">审批中</text>
            </view>
          </view>
          <view
              style="color: #869199; font-size: 14px; margin-top: 3px"
              v-if="item.wbId"
              >网报编码:
                <text>{{ item.wbId }}</text
              >
          </view>
          <view
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <view
              style="color: #869199; font-size: 14px; margin-top: 3px"
              v-if="item.mgr.length"
              >代表:
              <text v-for="j in item.mgr" :key="j.name"
                >{{ j.value }}({{ j.name }})</text
              >
            </view>
          </view>
          <view
            style="
              color: #869199;
              font-size: 14px;
              -webkit-line-clamp: 1; /* 显示的行数 */
              -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
              overflow: hidden; /* 隐藏超出的内容 */
              text-overflow: ellipsis;
            "
            v-if="item.productList.length"
            >产品：{{ getProduct(item.productList)
            }}<text v-if="item.productList.length > 3">{{
              ` | +${item.productList.length - 3}`
            }}</text>
          </view>

          <view class="opera-button">
            <view
              @click.prevent.stop="noTitleClick(item)"
              class="appraiseButton gray"
              v-if="
                props.sysJuInsDelObj.settingStatus === '1' &&
                appCode !== 'athena_pcb'
              "
            >
              机构解绑
            </view>
            <view
              @click.prevent.stop="goInsSign(item)"
              class="appraiseButton bule"
              v-if="!props.appcodeIns"
            >
              去签到
            </view>
          </view>
        </view>
      </view>
      <view
        v-if="state.insList.length > 0 && state.insList.length == state.total"
        style="color: #869199; font-size: 16px; text-align: center"
        >已全部加载完毕</view
      >
    </scroll-view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import {
  defineEmits,
  defineProps,
  ref,
  reactive,
  defineExpose,
  onMounted,
} from "vue";
import arrow from "../../../images/arrow.png";
import arrowRight from "../../../images/arrow-right.png";
import address from "../../../images/location_a.png";
import hospital from "../../../images/iconhospital.png";
import {
  getInsLevelColor,
  getInsLevelTextColor,
  getPostCode,
} from "../../../utils/area.js";
import {
  institutionList,
  initiateIns,
  approveInitiate,
} from "../../../api/area.js";
import { Clock } from "@nutui/icons-vue-taro";
import noData from "../../../images/no-data.png";

import { appCode, tenantId, baseApi, h5Url, pcUrl } from "../../../utils/content";
import { getProcessIdApi } from "../../../utils/content";
const feishuNotice = ref({
  appLink: `${h5Url}`,
  pcLink: `${pcUrl}`,
});
const ISGEN = ["/ade/"].includes(baseApi);
const ISINSGEN = ["/gen/"].includes(baseApi);
const ISSKN = ["/skn/"].includes(baseApi);
const initHrInfo = Taro.getStorageSync("initHrInfo");
const orgList = initHrInfo.orgList.filter((item) => item.postType === "1")[0];
const emit = defineEmits(["changePcdvalue"]);

const scrollMyFlag = ref(false);
const props = defineProps({
  tab: {
    type: String,
    default: "",
  },
  appcodeIns: {
    type: String,
    default: "",
  },
  sysJuInsDelObj: {
    type: Object,
    default: {},
  },
  insNoTitleClick: {
    type: Function,
    default: () => {},
  },
  insNoTitleCancel: {
    type: Function,
    default: () => {},
  },
});

const state = reactive({
  scrollTop: 0,
  pageNum: 1,
  pageSize: 10,
  params: {
    insName: "",

    province: "",
    city: "",
    district: "",
    jurCodeList: getPostCode().split(","),
    queryJur: "1",
  },
  total: 0,
  pcdvalue: "",
  insList: [],
  item: {},
});
const getProduct = (list) => {
  if (list.length) {
    return list
      .slice(0, 3)
      .map((item) => item.name)
      .join(" | ");
  }
};
const getinsList = () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });

  institutionList(state.pageNum, state.pageSize, state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.data.total;
        if (!state.insList.length) {
          console.log("33333", res.data.rows);
          state.insList = res.data.rows || [];
        } else {
          state.insList = [...state.insList, ...res.data.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = true;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      Taro.hideLoading();
    });
};

const topSearch = (search) => {
  state.params.insName = search;
  state.total = 0;
  state.pageNum = 1;
  state.insList = [];
  getinsList();
};

const insFilters = () => {
  const info = {
    province: state.params.province,
    city: state.params.city,
    district: state.params.district,
    pcdvalue: state.pcdvalue,
  };
  Taro.navigateTo({
    url: `/pages/area/ins/insFilters?info=${JSON.stringify(info)}`,
  });
};
const noTitleClick = async (item) => {
  state.item = item;
  props.insNoTitleClick();
};
const delConfirm = async () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  const applicantInfo = {
    applicant: initHrInfo.empName,
    applicantCode: initHrInfo.empCode,
    postIdList: initHrInfo.postIdList,
    postCode: orgList.code,
    postName: orgList.name,
    deptCode: orgList.deptCode,
    deptName: orgList.value,
    ancestors: orgList.ancestors,
  };
  const params = {
    applyContent: {
      ...state.item,
      type: "insUnbound",
      ...props.sysJuInsDelObj,
      products: [],
    },
    enableWorkflow: true,
    applyType: "12",
    insName: state.item.insName,
    jurCode: state.item.jurCode,
    insCode: state.item.insCode,
    appCode: appCode(),
    tenantId: tenantId,
    ...applicantInfo,
    channel: "2",
    processId: state.processId,
    ...feishuNotice.value,
  };
  const res = await approveInitiate(params);
  if (res.code === 200) {
    props.insNoTitleCancel();
    Taro.showToast({
      title: "操作成功",
      icon: "none",
      duration: 2000,
    });
  }
  Taro.hideLoading();
};

const onScrollToLower = () => {
  if (scrollMyFlag.value && state.insList.length < state.total) {
    state.pageNum++;
    scrollMyFlag.value = false;
    getinsList();
  }
};

const initInsList = () => {
  const insFilters = Taro.getStorageSync("insFilters");
  if (insFilters) {
    state.params.province = insFilters.province;
    state.params.city = insFilters.city;
    state.params.district = insFilters.district;
    state.pcdvalue = insFilters.pcdvalue;
    Taro.removeStorageSync("insFilters");
  }
  state.total = 0;
  state.pageNum = 1;
  state.insList = [];
  getinsList();
  emit("change-pcdvalue", state.pcdvalue);
};

//#region  点击列表项跳转到机构页
const gotoInst = (item) => {
  Taro.navigateTo({
    url: `/pages/area/ins/insDeatil?jurCode=${item.jurCode}&insCode=${item.insCode}&claiming=${item.claiming}`,
  });
};
const goInsSign = (item) => {
  Taro.setStorage({ key: "insInfo", data: item });
  Taro.navigateTo({
    url: ISSKN ? "pages/insSignSkn/sign/signIn" : "pages/insSign/sign/signIn",
  });
};
const getKey = async () => {
  state.processId = await getProcessIdApi("jur_institution_apply");
};
defineExpose({
  topSearch,
  insFilters,
  initInsList,
  delConfirm,
});
onMounted(() => {
  getKey();
});
</script>
<style lang="scss">
.insList {
  padding: 0px 0px;
  width: 100vw;
  // height: 630px;
  font-family: PingFang SC, PingFang SC;

  .title {
    color: #869199;
    font-size: 14px;
    padding: 18px 12px 8px 12px;
  }

  .instItem {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    margin-left: 12px;
    margin-right: 12px;

    .opera-button {
      display: flex;
      justify-content: flex-end;

      .appraiseButton {
        z-index: 999;
        width: 80px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 100px;
        margin-right: 12px;
        margin-top: 6px;
      }

      .gray {
        border: #e5e6eb 1px solid;
        color: #4e595e;
        font-size: 14px;
        font-weight: 400;
      }

      .bule {
        color: #2551f2;
        background-color: #e8f0ff;
        font-size: 14px;
        font-weight: 400;
      }
    }
  }

  .hosType {
    height: 18px;
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #94bfff;

    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #2551f2;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
  }

  .productTag {
    font-size: 12px;
    text-align: center;
    background: #fff3e8;
    color: #ff7d00;
    height: 20px;
    padding: 0 2px;
    line-height: 20px;
    margin-right: 12px;
    border-radius: 2px;
  }

  .nut-tag {
    margin-right: 8px;
  }
}
</style>
