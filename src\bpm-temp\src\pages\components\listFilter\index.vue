<template>
  <view class="list-filter">
    <view
      class="top-search-item"
      :class="item.active ? 'bgc' : ''"
      v-for="item in props.list"
      :key="item.key"
      @click="activeItem(item.key)"
    >
      <view style="white-space: nowrap">
        {{ item.text }}
      </view>
    </view>
    <view class="top-search-right" @click="selecSubPre" v-if="props.selectFlag">
      <img :src="screen" alt="" />
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import screen from "@/images/screen.png";
const representPopupRef = ref(null);

const props = defineProps({
  selectFlag: {
    type: Boolean,
    default: false,
  },
  subEmpCode: {
    type: Array,
    default: [],
  },
  scrollFlag: {
    type: Boolean,
    default: false,
  },
  list: {
    type: Array,
    default: [],
  },
});
const emit = defineEmits(["activeItem", "sub-user-select"]);
const state = reactive({});

const activeItem = (key) => {
  emit("activeItem", key);
};

const selecSubPre = () => {};

const selectPre = (list) => {
  emit("sub-user-select", list);
};
</script>
<style lang="scss">
.list-filter::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}
.list-filter {
  display: flex;
  padding: 8px 16px;
  overflow: hidden;
  overflow-x: scroll;
  align-items: center;
  background: #edeff5;
  .top-search-item {
    height: 27px;
    padding: 0px 9px;
    border-radius: 16px;
    background: #fff;
    font-size: 14px;
    line-height: 27px;
    margin-right: 8px;
    display: inline-block;
    white-space: nowrap;
    color: #4e595e;
    border: 1px solid #fff;
  }

  .line {
    margin: 0 4px;
    color: #eeee;
  }

  .top-search-right {
    position: absolute;
    right: 0px;
    font-size: 14px;
    line-height: 27px;
    text-align: center;
    width: 40px;
    background: linear-gradient(
      90deg,
      rgba(237, 239, 245, 0) 0%,
      #edeff5 17%,
      #edeff5 100%
    );
    height: 30px;

    img {
      width: 20px;
      height: 20px;
      vertical-align: middle;
    }
  }
  .bgc {
    color: #2551f2;
    background: var(--brand-color-1, #e8f0ff);
    border: 1px solid #92a8f8;
  }

  .top-search-value {
    max-width: 148px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .rotate {
    color: #2551f2;
    transform: rotate(180deg);
  }
}
</style>
