import HTTPREQUEST from "@/servers/http";

// 协访列表
export const assistApplyHandle = (params) => {
  return HTTPREQUEST.post("/msr/assist/apply/handle", params);
};

export const assistApply = (params) => {
  return HTTPREQUEST.get("/crm/assist/apply", params);
};

export const assistList = (params) => {
  return HTTPREQUEST.get(
    "/msr/assist/list",
    params,
    "application/x-www-form-urlencoded"
  );
};

export const subordinateList = (params) => {
  return HTTPREQUEST.get("/msr/assist/subordinate/list", params);
};
export const assistDetail = (params) => {
  return HTTPREQUEST.get("/crm/assist/detail", params);
};
export const assistCalendarList = (params) => {
  return HTTPREQUEST.get("/msr/assist/calendar/list", params);
};
export const dictDataList = () => {
  const params = {
    dictType: "assist_score",
  };
  return HTTPREQUEST.get("/plt/dict/data/list", params);
};

export const assistEvaluate = (params) => {
  return HTTPREQUEST.post("/msr/assist/evaluate", params);
};
export const assistSignIn = (params) => {
  return HTTPREQUEST.post("/crm/assist/is_can/sign_in", params);
};
