<template>
  <view class="insTabDetail">
    <view style="font-size: 16px; color: #1d212b">机构详情</view>
    <view class="cusDetail-item">
      <view class="cusDetail-item-title">
        <view class="label">机构名称</view>
        <view class="value"
          ><text>{{ state.info.insName }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">机构等级</view>
        <view class="value"
          ><text>{{ state.info.insGrade }}{{ state.info.insLevel }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">状态</view>
        <view class="value"
          ><text>{{
            state.info.status === "0"
              ? "应用"
              : state.info.status === "1"
              ? "禁用"
              : ""
          }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">机构类型</view>
        <view class="value"
          ><text>{{ state.info.insType }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">经济类型</view>
        <view class="value"
          ><text>{{ state.info.economicalType }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">省份</view>
        <view class="value"
          ><text>{{ state.info.province }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">城市</view>
        <view class="value"
          ><text>{{ state.info.city }}</text></view
        >
      </view>
      <view class="cusDetail-item-title" v-if="ISGEN">
        <view class="label">妇科门诊量(人/年)</view>
        <view class="value"
          ><text>{{ state.info.dayOutpatient }}</text></view
        >
      </view>
      <view class="cusDetail-item-title">
        <view class="label">产品</view>
        <view class="value"
          ><text>{{ getDetailProduct(state.info.productList) }}</text></view
        >
      </view>
    </view>
  </view>
</template>
<script setup>
import { View } from "@tarojs/components";
import { reactive } from "vue";
import { getDetailProduct } from "../../../utils/area.js";
import { baseApi } from "../../../utils/content";

const ISGEN = ['/gen/'].includes(baseApi)
const state = reactive({
  info: {},
});

const setInfo = (info) => {
  state.info = info;
};

defineExpose({
  setInfo,
});
</script>
<style lang="scss">
.insTabDetail::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}
.insTabDetail {
  font-size: 14px;
  color: #869199;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  overflow-y: scroll;
  margin: 12px;

  .title {
    font-size: 20px;
    color: #1d212b;
  }
  .cusDetail-item {
    margin-top: 10px;
    .cusDetail-item-title {
      display: flex;
      padding: 6px 0;
      .label {
        width: 28%;
      }
      .value {
        color: #1d212b;
        width: 70%;
        display: block;
      }
    }
  }
}
</style>
