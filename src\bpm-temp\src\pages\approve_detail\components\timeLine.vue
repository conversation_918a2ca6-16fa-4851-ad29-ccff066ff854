<template>
  <view class="relative bg-white approve-detail-line pb-[16px]">
    <view class="line absolute w-[1px] h-[100%] bg-[#2551f2] left-[34px]"></view>
    <view v-for="(item, index) in flowTaskHistoryVo" class="first-node flex gap-[12px] relative z-99  pl-[16px]">
      <view v-if="index === flowTaskHistoryVo.length - 1" class="absolute left-[34px] top-[0px] w-[4px] bg-white h-[120%]"></view>
      <view class="left w-[28px] h-[28px] rounded-[50%] b-white z-[99]">
        <img :src="getNodeIcon(item)" class="w-[28px] h-[28px] rounded-[50%]" style="border: 4px solid #fff">
      </view>
      <view class="right w-[100%] pr-[16px] mt-[4px]">
        <view class="text-[#1D212B] text-[16px] flex" style="align-items: center;justify-content: space-between;">
          <view class="flex" style="align-items: center;">
            <view class="max-w-[145px]">{{ item.taskName }} </view>
<!--            审批类型 = 或签/会签/抄送 展示-->
            <view class="mx-[8px] text-[#E5E6EB] text-[12px]" v-if="item.taskType === 2 || item.performType === 2 || item.performType === 3"> | </view>
            <view class="text-[12px] text-[#86909C]" v-if="item.performType === 2 ">会签</view>
            <view class="text-[12px] text-[#86909C]" v-if="item.performType === 3 ">或签</view>
            <view class="text-[12px] text-[#86909C]" v-if="item.performType === 2 || item.performType === 3 || item.taskType === 2">{{item?.flowTaskActorHistoryVos?.length}}人</view>
          </view>
          <view v-if="index != 0 && item.taskType !== 2" class="timeLine-task-tag"
            :style="{ color: task(item.taskState).textColor, borderColor: task(item.taskState).bgcColor }">{{
              task(item.taskState).text }}</view>
        </view>
        <view class="flex gap-[8px] flex-wrap mt-[2px]">
          <view class="flex items-center justify-between gap-[8px] min-w-[100%]" v-for="actor in item?.flowTaskActorHistoryVos || []" :key="actor.id">
            <view>
              <view class="flex gap-[2px] items-center rounded-[50px] bg-[#F3F4F5] p-[2px]">
                <view class="flow_name_avatar">{{ actor.actorName?.slice(-1) }}</view>
                <view class="text-[14px]">{{actor.actorName?.length > 4 ? `${actor.actorName.slice(0, 3)}...` : actor.actorName}}</view>
                <view class="text-[14px] pr-[6px] text-[#86909C]">({{ actor.creator }})</view>
              </view>
              <view class="text-[#86909C] text-[12px] mt-[4px]">{{ actor.updateTime }}</view>
              <view class="text-[#86909C] text-[12px] mt-[4px]">
                {{ getOpinion(actor) }}
              </view>
            </view>
            <view class="text-[14px] text-[#1D212B] flex items-center gap-[2px]" v-if="actor.opinionStatus == 1"><view class="w-[6px] h-[6px] rounded-[50%] bg-[#00B578]"></view>已通过</view>
            <view class="text-[14px] text-[#1D212B] flex items-center gap-[2px]" v-else-if="actor.opinionStatus == 2"><view class="w-[6px] h-[6px] rounded-[50%] bg-[#F32F29]"></view>已拒绝</view>
            <view class="text-[14px] text-[#1D212B]" v-else-if="actor.opinionStatus == 3">已撤销</view>
          </view>
<!--          <view> {{ item?.flowTaskActorHistoryVos.map(d => d.actorName).join(',') }}</view>-->
        </view>
        <view class="text-[#1D212B] text-[14px] mt-[4px]">
        </view>
<!--        <view class="text-[#86909C] text-[12px] mt-[4px]">-->
<!--          {{ item?.flowTaskActorHistoryVos?.[item?.flowTaskActorHistoryVos?.length - 1]?.updateTime }}-->
<!--        </view>-->

      </view>
      <view class="absolute w-[3px] h-[100%] bg-white left-[34px] z-[2]"
        v-show="index === flowTaskHistoryVo?.length - 1 && !todoTaskActorVo"></view>
    </view>

<!--    <view class="last-node flex gap-[12px] relative z-999 bg-white pl-[16px]" v-if="todoTaskActorVo"-->
<!--      style="border-radius:  0 0 8px 8px;">-->
<!--      <view class="left rounded-[50%] border-[4px] b-white border-[4px]">-->
<!--        <img :src="pendingIcon" class="w-[28px] h-[28px] rounded-[50%]" style="border: 4px solid #fff">-->
<!--      </view>-->
<!--      <view class="right h-[90px]" style="margin-left: -9px; min-width: 80%">-->
<!--        <view class="text-[#1D212B] text-[16px]" style="align-items: center;">-->
<!--          <view class="flex justify-between items-center">-->
<!--            <view>-->
<!--              {{ todoTaskActorVo?.taskName }}-->
<!--            </view>-->
<!--            &lt;!&ndash;            审批类型 = 或签/会签 展示&ndash;&gt;-->
<!--            <view class="mx-[8px] text-[#E5E6EB] text-[12px] flex-0" v-if="(todoTaskActorVo.performType === 2 || todoTaskActorVo.performType === 3)"> | </view>-->
<!--            <view class="text-[12px] text-[#86909C] flex-1" v-if="(todoTaskActorVo.performType === 2 || todoTaskActorVo.performType === 3)">{{todoTaskActorVo?.flowTaskActorList?.length}}人{{ todoTaskActorVo.performType === 2 ? '会签' : '或签'}}</view>-->
<!--            <view class="timeLine-task-tag" style="color: #F77234; border-color: #FCC59F; margin-left: auto">审批中</view>-->
<!--          </view>-->
<!--          <view class="flex flex-wrap gap-[8px] mt-[8px] justify-between" v-for="actor in todoTaskActorVo?.flowTaskActorList || []">-->
<!--            <view class="flex gap-[2px] items-center rounded-[50px] bg-[#F3F4F5] p-[2px]">-->
<!--              <view class="nameAvatar">{{ actor.actorName?.slice(-1) }}</view>-->
<!--              <view class="text-[14px]">{{actor.actorName?.length > 4 ? `${actor.actorName.slice(0, 3)}...` : actor.actorName}}</view>-->
<!--              <view class="text-[14px] pr-[6px] text-[#86909C]">({{ actor.creator }})</view>-->
<!--            </view>-->
<!--          &lt;!&ndash;     或签/会签 显示各个审批人的状态       &ndash;&gt;-->
<!--            <view v-if="(todoTaskActorVo.performType === 2 || todoTaskActorVo.performType === 3)" class="text-[#1D212B] text-[14px] flex items-center gap-[8px]">-->
<!--              <view class="w-[6px] h-[6px] rounded-[50%] bg-[#00B578]" v-if="actor.taskState === 2"></view>-->
<!--              <view class="w-[6px] h-[6px] rounded-[50%] bg-[#F77234]" v-else></view>-->
<!--              {{ task(actor.taskState).text}}-->
<!--            </view>-->
<!--          </view>-->

<!--        </view>-->

<!--        &lt;!&ndash;        <view class="text-[#86909C] text-[12px] mt-[4px]">审批人</view>&ndash;&gt;-->
<!--      </view>-->
<!--    </view>-->
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import startNodeIcon from './../../../assets/images/approce_status_done.png';
import pendingIcon from './../../../assets/images/approve_status_pending.png';
import refuseIcon from './../../../assets/images/approce_status_refuse.png';
import cancleIcon from './../../../assets/images/approve_status_cancle.png';
import chaosongIcon from './../../../assets/images/chaosong.png';

const props = defineProps({
  flowTaskHistoryVo: {
    type: Array,
    default: []
  },
  todoTaskActorVo: {
    type: Object,
    default: {}
  }
})

const getNodeIcon = (item) => {
  console.log("item", item)
  if (item.performType == 0) {
    return startNodeIcon
  }else if (item.performType  == 9) {
    return chaosongIcon
  }else if (item.taskState == 1) {
    // taskState字段（1.审批中 2，完成 3，拒绝 4，撤销审批）
    return pendingIcon
  }else if (item.taskState == 2) {
    return startNodeIcon
  }else if (item.taskState == 3) {
    return refuseIcon
  }else if (item.taskState == 4) {
    return cancleIcon
  }


  return startNodeIcon

}
const getOpinion = (item) => {
  if(!item.opinion) return ''
  if (item.opinionStatus == 1) {
    return `通过原因：${item.opinion}`
  }
  if (item.opinionStatus == 2) {
    return `拒绝原因：${item.opinion}`
  }
  if (item.opinionStatus == 3) {
    return `撤销原因：${item.opinion}`
  }
}

const task = (taskState) => {
  if (taskState == 2) {
    return {
      text: '审批通过',
      textColor: '#00B578',
      bgcColor: '#A6E5D0'
    }
  }
  if (taskState == 3) {
    return {
      text: '审批拒绝',
      textColor: '#F32F29',
      bgcColor: '#FAACA9'
    }
  }
  if (taskState == 4) {
    return {
      text: '审批撤销',
      textColor: '#4E595E',
      bgcColor: '#C6CAD1'
    }
  }

  return {
    text: '审批中',
    textColor: '#F77234',
    bgcColor: '#FCC59F'
  }
}
</script>
<style lang="scss">
.flow_name_avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #92a8f8;
  color: #fff;
  line-height: 24px;
  text-align: center;
  min-width: 24px;
  margin-right: 4px;
  font-size: 12px;
}
.approve-detail-line {
  border-radius: 0 0 8px 8px;

  .timeLine-task-tag {

    padding: 2px 4px;
    font-size: 12px;
    border-radius: 2px;

    border: 1px solid;
    margin-left: 12px;
  }

}
</style>