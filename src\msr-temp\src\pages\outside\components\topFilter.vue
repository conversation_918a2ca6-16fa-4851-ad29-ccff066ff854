<template>
  <view class="outside-topFilter">
    <GlobalPopup ref="popupRef" :style="{ maxHight: '500px' }">
      <view style="margin: 16px; padding-bottom: 80px">
        <view class="title">筛选类型</view>
        <view>
          <nut-row :gutter="10">
            <nut-col
              :span="12"
              v-for="item in state.outsideTypeList"
              @click="selectType(item)"
            >
              <div class="content" :class="item.flag ? 'light' : ''">
                {{ item.dictLabel }}
              </div>
            </nut-col>
          </nut-row>
          <nut-row :gutter="10">
            <nut-col :span="12" @click="selectCancle">
              <div
                class="content"
                :class="
                  state.params.applyType === 'valid_day_cancel' ? 'light' : ''
                "
              >
                销假
              </div>
            </nut-col>
          </nut-row>
          <view class="title">筛选状态</view>
          <nut-row :gutter="10">
            <nut-col
              :span="12"
              v-for="item in state.applyFormStatus"
              @click="selectStatus(item)"
            >
              <div class="content" :class="item.flag ? 'light' : ''">
                {{ item.dictLabel }}
              </div>
            </nut-col>
          </nut-row>
          <view v-if="state.cusOption.length && props.bpmType !== '0'">
            <view class="title">筛选下属</view>
            <view class="sublist">
              <nut-row :gutter="10">
                <nut-col
                  :span="6"
                  v-for="item in state.cusOption"
                  @click="selectSub(item)"
                >
                  <div class="content" :class="item.flag ? 'light' : ''">
                    {{ item.empName }}
                  </div>
                </nut-col>
              </nut-row>
            </view>
          </view>
        </view>
      </view>

      <FooterButtonTwo
        rightText="确认"
        @click-left="resetButton"
        @click-right="confirmButton"
        leftText="重置"
        :plainFlag="false"
        leftColor="#E8F0FF"
        leftTextColor="#2551F2"
      />
    </GlobalPopup>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { ref, onMounted, reactive, defineEmits } from "vue";
import GlobalPopup from "@/pages/components/globalPopup/globalPopup.vue";
import FooterButtonTwo from "@/pages/components/footerButtonTwo/footerButtonTwo";
import { hrSubEmp } from "@/api/institutionalVisitsApi";
const emit = defineEmits("confirm");
const list = Taro.getStorageSync("outsideTypeList");
const initHrInfo = Taro.getStorageSync("initHrInfo");
const applyFormTypeList = Taro.getStorageSync("applyFormTypeList").filter(
  (item) => JSON.parse(item.remark).type === "tum"
);
const statusList = JSON.parse(applyFormTypeList[0].remark).data;
const popupRef = ref(null);
const props = defineProps({
  bpmType: {
    type: String,
    default: "",
  },
});
const state = reactive({
  params: {
    applyType: "",
    typeId: "",
    userId: "",
    userNameList: [],
    typeName: [],
    status: "",
    statusName: [],
  },
  cusOption: [],
  outsideTypeList: [],
  applyFormStatus: statusList,
});
const selectType = (i) => {
  state.outsideTypeList.forEach((item) => {
    if (item.dictValue === i.dictValue) {
      item.flag = !item.flag;
    } else {
      item.flag = false;
    }
  });
  const flag = state.outsideTypeList.some((item) => item.flag);
  if (flag) {
    state.params.applyType = "";
  }
  state.params.typeId = state.outsideTypeList
    .filter((item) => item.flag)
    .map((item) => item.dictValue)
    .join(",");
  state.params.typeName = state.outsideTypeList
    .filter((item) => item.flag)
    .map((item) => item.dictLabel);
  console.log("dddd", state.params);
};

const selectStatus = (i) => {
  state.applyFormStatus.forEach((item) => {
    if (item.dictValue === i.dictValue) {
      item.flag = !item.flag;
    } else {
      item.flag = false;
    }
  });

  state.params.status = state.applyFormStatus
    .filter((item) => item.flag)
    .map((item) => item.dictValue)
    .join(",");
  state.params.statusName = state.applyFormStatus
    .filter((item) => item.flag)
    .map((item) => item.dictLabel);
};

const selectCancle = () => {
  if (!state.params.applyType) {
    state.params.applyType = "valid_day_cancel";
    state.params.typeId = "";
    state.params.typeName = [];
    state.outsideTypeList = state.outsideTypeList.map((item) => {
      return {
        ...item,
        flag: false,
      };
    });
  } else {
    state.params.applyType = "";
  }
};

const getCusOption = async () => {
  const params = {
    subPostIdList: initHrInfo.subPostIdList,
  };
  const res = await hrSubEmp(params);

  state.cusOption = res.data.map((item) => {
    return {
      ...item,
      flag: false,
    };
  });
  if (state.params.userId) {
    state.cusOption.forEach((item) => {
      if (state.params.userId.split(",").includes(item.empCode)) {
        item.flag = true;
      }
    });
  }
};

const selectSub = (i) => {
  state.cusOption.forEach((item) => {
    if (item.empCode === i.empCode) {
      item.flag = !item.flag;
    }
  });
  state.params.userId = state.cusOption
    .filter((item) => item.flag)
    .map((item) => item.empCode)
    .join(",");
  state.params.userNameList = state.cusOption
    .filter((item) => item.flag)
    .map((item) => item.empName);
};
const open = (obj) => {
  popupRef.value.open();
  getCusOption();
  state.outsideTypeList = list.map((item) => {
    return {
      ...item,
      flag: false,
    };
  });
  state.applyFormStatus = state.applyFormStatus.map((item) => {
    return {
      ...item,
      flag: false,
    };
  });

  state.params = JSON.parse(JSON.stringify(obj));
  if (state.params.typeId) {
    state.outsideTypeList.forEach((item) => {
      if (state.params.typeId.split(",").includes(item.dictValue)) {
        item.flag = true;
      }
    });
  }

  if (state.params.status) {
    state.applyFormStatus.forEach((item) => {
      if (state.params.status.split(",").includes(item.dictValue)) {
        item.flag = true;
      }
    });
  }
};
const resetButton = () => {
  state.params = {
    applyType: "",
    typeId: "",
    userId: "",
    userNameList: [],
    typeName: [],
    status: "",
    statusName: [],
  };
  state.outsideTypeList = state.outsideTypeList.map((item) => {
    return {
      ...item,
      flag: false,
    };
  });
  state.applyFormStatus = state.applyFormStatus.map((item) => {
    return {
      ...item,
      flag: false,
    };
  });
  state.cusOption = state.cusOption.map((item) => {
    return {
      ...item,
      flag: false,
    };
  });
};

const confirmButton = () => {
  popupRef.value.close();
  emit("confirm", state.params);
};
defineExpose({
  open,
});
</script>

<style lang="scss">
.outside-topFilter {
  padding: 16px;

  .content {
    line-height: 40px;
    color: #4e595e;
    text-align: center;
    border-radius: 6px;
    background: #f3f4f5;
    margin-bottom: 10px;
    font-size: 14px;
  }

  .light {
    background: #e8f0ff;
    color: #2551f2;
  }

  .title {
    font-size: 13px;
    color: #869199;
    margin-bottom: 8px;
  }

  .sublist {
    max-height: 196px;
    overflow: hidden;
    overflow-y: scroll;
  }
  .footerButtonTwo {
    border-top: 8px solid #f3f4f5;
  }
}
</style>
