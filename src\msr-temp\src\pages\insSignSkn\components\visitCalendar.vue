<template>
  <view>
    <view class="ins-sign-visit-calendar">
      <view style="display: flex; align-items: center">
        <view @click="openCalendar" class="calendarIcon">
          <img
            :src="calendar"
            alt=""
            style="width: 24px; height: 24px; display: inline-block"
          />
        </view>
        <!-- <view style="margin-right: 10px"
          >{{ dayjs(props.visitTime).format("MM/DD") }} -
          {{ dayjs(props.visitEndTime).format("MM/DD") }}</view
        > -->
        <view style="margin-right: 10px">{{ getTime() }}</view>
      </view>
      <view style="display: flex">
        <view class="button-group">
          <view
            class="buttons"
            v-for="item in state.buttonArray"
            :key="item.code"
            @click="buttonGroup(item.code)"
            :class="state.buttonFlag === item.code ? 'button-active' : ''"
            >{{ item.value }}</view
          >
        </view>
        <view
          class="top-search-right"
          @click="selecSubPre"
          v-if="props.selectFlag === 1"
        >
          <img :src="screen" alt="" />
        </view>
      </view>
    </view>
    <view>
      <nut-calendar
        v-model:visible="state.show"
        is-auto-back-fill
        type="range"
        :default-value="state.topTime"
        :start-date="state.stateDate"
        :end-date="state.endDate"
        @close="bb"
        @choose="choose"
        @select="aa"
      >
      </nut-calendar>
    </view>
  </view>
  <RepresentPopup ref="representPopupRef" @select-pre="selectPre" />
</template>
<script setup>
import { onMounted, reactive, ref } from "vue";
import calendar from "../../../images/calendar.png";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import screen from "../../../images/screen.png";
import RepresentPopup from "./representPopup";
const representPopupRef = ref(null);
const emit = defineEmits(["chage-time", "sub-user-select"]);
const props = defineProps({
  visitTime: {
    type: String,
    default: "",
  },
  visitEndTime: {
    type: String,
    default: "",
  },
  selectFlag: {
    type: String,
    default: "",
  },
  subEmpCode: {
    type: Array,
    default: [],
  },
});
const state = reactive({
  show: false,
  stateDate: null,
  endDate: null,
  topTime: null,
  time: dayjs().format("YYYY-MM-DD"),
  buttonFlag: "",
  buttonArray: [
    { value: "今天", code: "1" },
    { value: "本周", code: "2" },
  ],
  currentTime: [],
});

const getTime = () => {
  if (props.visitTime == props.visitEndTime) {
    return dayjs(props.visitTime).format("MM/DD");
  } else {
    return (
      dayjs(props.visitTime).format("MM/DD") +
      "-" +
      dayjs(props.visitEndTime).format("MM/DD")
    );
  }
};
const openCalendar = () => {
  state.show = true;
  state.topTime = [props.visitTime, props.visitEndTime];
  state.stateDate = dayjs().subtract(2, "year").format("YYYY-MM-DD");
  state.endDate = dayjs().add(2, "year").format("YYYY-MM-DD");
};

const choose = (list) => {
  state.topTime = [list[0][3], list[1][3]];
  emit("chage-time", list[0][3], list[1][3]);
  state.buttonFlag = "";
};

const buttonGroup = (code) => {
  state.buttonFlag = code;
  if (code === "1") {
    state.currentTime = [
      dayjs().format("YYYY-MM-DD"),
      dayjs().format("YYYY-MM-DD"),
    ];
  }
  if (code === "2") {
    let currentDate = dayjs();
    if (currentDate.day() == 0) {
      currentDate = currentDate.subtract(1, "day");
    }
    let monday = currentDate.day(1);
    let sunday = currentDate.day(7);
    // 格式化日期为 YYYY-MM-DD
    const mondayFormatted = monday.format("YYYY-MM-DD");
    const sundayFormatted = sunday.format("YYYY-MM-DD");
    state.currentTime = [mondayFormatted, sundayFormatted];
  }
  if (code === "3") {
    const currentDate = dayjs();
    const nextMonday = currentDate.add(1, "week").day(1);
    const nextSunday = currentDate.add(1, "week").day(7);
    // 格式化日期为 YYYY-MM-DD
    const mondayFormatted = nextMonday.format("YYYY-MM-DD");
    const sundayFormatted = nextSunday.format("YYYY-MM-DD");
    state.currentTime = [mondayFormatted, sundayFormatted];
  }
  emit("chage-time", state.currentTime[0], state.currentTime[1]);
};

const selecSubPre = () => {
  representPopupRef.value.open(props.subEmpCode);
};

const selectPre = (list) => {
  emit("sub-user-select", list);
};
</script>
<style lang="scss" scoped>
.ins-sign-visit-calendar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  padding: 16px;
  .calendarIcon {
    height: 32px;
    width: 32px;
    border-radius: 50%;
    background: #f3f4f5;
    text-align: center;
    line-height: 45px;
    margin-right: 10px;
  }
  .button-group {
    width: 110px;
    height: 32px;
    border-radius: 52px;
    background: #f3f4f5;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;

    .buttons {
      width: 52px;
      height: 26px;
      line-height: 26px;
      font-size: 14px;
      border-radius: 32px;
      color: #4e595e;
    }
    .button-active {
      color: #2551f2;
      background: #fff;
    }
  }
  .top-search-right {
    font-size: 14px;
    line-height: 32px;
    text-align: center;
    width: 32px;
    height: 32px;
    background: #f3f4f5;
    border-radius: 50%;
    margin-left: 8px;
    img {
      width: 15px;
      height: 15px;
      vertical-align: middle;
    }
  }
}
.nut-calendar
  .nut-calendar__content
  .nut-calendar__panel
  .nut-calendar__days
  .nut-calendar__day--choose::after {
  background-color: #92a8f8;
}
.nut-calendar
  .nut-calendar__content
  .nut-calendar__panel
  .nut-calendar__days
  .nut-calendar__day--choose {
  color: #2551f2;
}
</style>
