<template>
  <view class="selectIns">
    <GlobalPopup ref="popupRef">
      <TopSearch
        @top-search="search"
        placeholder="请输入机构名称或编码"
        ref="topSearchRef"
      />
      <scroll-view
        style="height: 500px"
        :scroll-y="true"
        :scroll-top="0"
        @scrolltolower="scrollToLower"
      >
        <nut-radio-group v-model="state.radioChecked" text-position="left">
          <GlobalNutRadio
            v-for="item in state.insList"
            :value="item.code"
            :key="item.code"
          >
            <template #name>
              <view>
                {{ item.value }}
              </view>
            </template>
          </GlobalNutRadio>
        </nut-radio-group>
      </scroll-view>

      <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import TopSearch from "../../../pages/components/topSearch/topSearch.vue";
import GlobalPopup from "../../../pages/components/globalPopup/globalPopup.vue";
import GlobalNutRadio from "../../../pages/components/globalNutRadio/globalNutRadio.vue";
import { institution } from "../../../api/area.js";
import { getPostCode } from "../../../utils/area.js";
const emit = defineEmits(["ins-confirm"]);
const popupRef = ref(null);
const state = reactive({
  insList: [],
  radioChecked: "",
  params: {
    insName: "",
    pageSize: 10,
    pageNum: 1,
    jurCode: "",
    type: "",
  },
  flag: false,
  total: 0,
});

const search = (name) => {
  if (state.flag) return;
  state.params.insName = name;
  state.params.pageNum = 1;
  state.insList = [];
  getInstitutionList();
};

const getInstitutionList = async () => {
  state.flag = true;
  const res = await institution(state.params);
  if (!state.insList.length) {
    state.insList = res.data.rows;
  } else {
    state.insList = [...state.insList, ...res.data.rows];
  }
  state.total = res.data.total;
  state.flag = false;
};

const scrollToLower = () => {
  if (!state.flag && state.insList.length < state.total) {
    state.params.pageNum++;
    getInstitutionList();
  }
};
const confirm = () => {
  const ins = state.insList.filter(
    (item) => item.code === state.radioChecked
  )[0];
  emit("ins-confirm", ins);
  popupRef.value.close();
  state.radioChecked = "";
  state.insList = [];
};

const open = (t, code, jurCode, type) => {
  state.radioChecked = code;
  state.params.jurCode = jurCode;
  state.params.type = type;
  popupRef.value.open();
  state.params.pageNum = 1;
  state.params.insName = "";
  state.total = 0;
  state.insList = [];
  getInstitutionList();
};
defineExpose({
  open,
});
</script>
<style lang="scss">
.selectIns {
  background: #edeff5;
  .nut-radio-group {
    padding: 0 16px;
  }
  .nut-radio {
    background: #fff;
  }

  .footer-comfirm {
    height: 60px;
    padding: 16px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    padding-right: 0;
  }
}
</style>
