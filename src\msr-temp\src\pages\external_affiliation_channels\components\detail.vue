<template>
  <view class="speakerDetail">
    <view class="section">
      <view class="cus-information">
        <view class="information-name">
          <view style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            ">
            <view style="
                margin-right: 8px;
                color: #1d212b;
                font-size: 20px;
                font-weight: 500;
              ">{{ state.info.name }}
              <nut-tag plain color="#94BFFF" text-color="#2551F2" style="vertical-align: text-bottom">
                {{ state.info.type }}
              </nut-tag>
            </view>
          </view>
        </view>

        <view style="display: flex; align-items: start">
          机构：{{ state.info.insName }}
        </view>
      </view>
    </view>
    <view class="pt-[16px]">
      <view class="bg-white rounded-[8px] mx-[8px]">
        <view class="p-[16px] flex items-center gap-[8px] border-[1px] border-solid border-transparent border-b-[#E5E6EB]">
          <view class="px-[8px] py-[2px] bg-[#597DFF] text-white text-[14px] text-center rounded-[2px]">1</view>
          <view class="text-[#1D212B] text-[18px] font-500">药店名称</view>
          <view class="text-[#869199] text-[14px]">药店编码</view>
        </view>
        <view class="p-[16px]">
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">药店类型</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">上级药店</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">上级药店编码</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">DDI直连</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">药店性质</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">药店支付类型</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>

          <view class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"></view>

          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">品规名称</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">是否开发商开发</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">开发状态</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">是否上架</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>

          <view class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"></view>

          <view class="flex flex-col mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 mb-[16px]">照片(营业执照、经营许可证、门头照)</view>
            <view class="text-[#1D212B] text-[14px] font-400">图片</view>
          </view>
        </view>
      </view>
    </view>

    <view class="pt-[16px]">
      <view class="bg-white rounded-[8px] mx-[8px]">
        <view class="p-[16px] flex items-center gap-[8px] border-[1px] border-solid border-transparent border-b-[#E5E6EB]">
          <view class="px-[8px] py-[2px] bg-[#597DFF] text-white text-[14px] text-center rounded-[2px]">1</view>
          <view class="text-[#1D212B] text-[18px] font-500">药店名称</view>
          <view class="text-[#869199] text-[14px]">药店编码</view>
        </view>
        <view class="p-[16px]">
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">药店类型</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">上级药店</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">上级药店编码</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">DDI直连</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">药店性质</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">药店支付类型</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>

          <view class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"></view>

          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">品规名称</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">是否开发商开发</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>
          <view class="flex items-center mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">开发状态</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
            <view class="text-[#2551F2] text-[14px] font-400 ml-[10px]" @tap="handleChange('devStatus')">变更</view>
          </view>
          <view class="flex items-center">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]">是否上架</view>
            <view class="text-[#1D212B] text-[14px] font-400">药店xxxx</view>
          </view>

          <view class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"></view>

          <view class="flex flex-col mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 mb-[16px]">照片(营业执照、经营许可证、门头照)</view>
            <view class="text-[#1D212B] text-[14px] font-400">图片</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <ChangeDevStatus ref="changeDevStatusRef" />
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref, computed } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import SpeakerpaymentDetail from "./paymentDetail.vue";
import SpeakerInfoDetail from "./infoDetail.vue";
import { speakerDetailApi } from "../../../api/speaker.js";
import location_a from "../../../images/ph_address.png";
import DetailItem from "../../../pages/components/detailItem/index.vue";
import ChangeDevStatus from "./changeDevStatus";
const router = useRouter();
const query = router.params;
const speakerInfoDetailRef = ref(null);
const speakerpaymentDetail = ref(null)
const changeDevStatusRef = ref(null)
const state = reactive({
  info: {},
  approvalStatus: "1",
});

const getDetail = async () => {
  Taro.showToast({
    title: "加载中",
    icon: "loading",
  });
  const res = await speakerDetailApi({code:query.code});
  state.info = res.data;
  speakerInfoDetailRef.value.setInfo(res.data);
  speakerpaymentDetail.value.setInfo(res.data);
  Taro.hideLoading();
};
const relaceApi = (value) => {
  if(value) return  '*'.repeat(value.length);

}
const clickButton = () => {
  Taro.navigateTo({
      url: `/pages/speaker/components/addSpeaker?type=edit&info=${JSON.stringify(state.info)}`,
    });
}

const handleChange = (type) => {
  if(type === 'devStatus') {
    changeDevStatusRef.value?.open()
  }
}

useDidShow(async () => {
  Taro.setNavigationBarTitle({ title: "讲者详情" });
  await getDetail();
});
</script>

<style lang="scss">
.speakerDetail {
  padding-bottom: 93px;

  .section {
    font-size: 14px;
    color: #869199;
    background: #fff;

    padding: 16px;

    .cus-information {
      padding: 16px;
      background: #f4f5f7;
      background-image: url("../../../images/card-bg.png");
      background-size: 100% 100%;

      .information-name {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .hosType {
        height: 22px;
        border-radius: 2px 2px 2px 2px;
        border: 1px solid #94bfff;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #2551f2;
        line-height: 22px;
        padding: 0 4px;
        margin-right: 4px;
      }
    }
  }

  .icons {
    width: 14px;
    height: 14px;
    display: block;
    margin-right: 4px;
    margin-top: 3px;
  }

  .nut-tab-pane {
    font-size: 14px;

    padding: 0;
    background: none;
    padding: 12px;

  }
}
</style>
