import dayjs from "dayjs";
import httpRequest from "@/servers/http";
import CryptoJS from "crypto-js";
import { appCode, tenantId, appId } from "@/utils/content";
function Auth() {
  console.log("start apiAuth");
  if (!window.h5sdk) {
    console.log("invalid h5sdk");
    // alert("please open in feishu");
    return;
  }
  // 调用config接口的当前网页url
  const url = location.href.split("#")[0];
  console.log("接入方前端将需要鉴权的url发给接入方服务端,url为:", url);
  // 向接入方服务端发起请求，获取鉴权参数（appId、timestamp、nonceStr、signature）
  httpRequest
    .get(`/itg/ticket?appCode=${appCode}&tenantId=${tenantId}&url=${url}`)
    .then((res) => {
      console.log(
        "接入方服务端返回给接入方前端的结果(前端调用config接口的所需参数):",
        res
      );
      // 通过error接口处理API验证失败后的回调
      window.h5sdk.error((err) => {
        throw ("h5sdk error:", JSON.stringify(err));
      });
      const ts = dayjs().valueOf();
      const noncestr = "Y7a8KkqX041bsSwT";
      const verifyStr = `jsapi_ticket=${
        res.data.jsapiTicket
      }&noncestr=${noncestr}&timestamp=${ts.toString()}&url=${url}`;
      const sha1Hash = CryptoJS.SHA1(verifyStr).toString();
      console.log("当前环境 APPID", appId);
      // 调用config接口进行鉴权
      window.h5sdk.config({
        appId: appId,
        timestamp: ts,
        nonceStr: noncestr,
        signature: sha1Hash,
        jsApiList: ["chooseImage"],
        //鉴权成功回调
        onSuccess: (res) => {
          console.log(`config success: ${JSON.stringify(res)}`);
        },
        //鉴权失败回调
        onFail: (err) => {
          throw `config failed: ${JSON.stringify(err)}`;
        },
      });
      // 完成鉴权后，便可在 window.h5sdk.ready 里调用 JSAPI
      window.h5sdk.ready(() => {
        // window.h5sdk.ready回调函数在环境准备就绪时触发
        // 调用 getUserInfo API 获取已登录用户的基本信息，详细文档参见https://open.feishu.cn/document/uYjL24iN/ucjMx4yNyEjL3ITM
        tt.getUserInfo({
          // getUserInfo API 调用成功回调
          success(res) {
            console.log(`getUserInfo success: ${JSON.stringify(res)}`);
            // 单独定义的函数showUser，用于将用户信息展示在前端页面上
            // showUser(res.userInfo);
          },
          // getUserInfo API 调用失败回调
          fail(err) {
            console.log(`getUserInfo failed:`, JSON.stringify(err));
          },
        });
        // 调用 showToast API 弹出全局提示框，详细文档参见https://open.feishu.cn/document/uAjLw4CM/uYjL24iN/block/api/showtoast
        // tt.showToast({
        //   title: "鉴权成功",
        //   icon: "success",
        //   duration: 3000,
        //   success(res) {
        //     console.log("showToast 调用成功", res.errMsg);
        //   },
        //   fail(res) {
        //     console.log("showToast 调用失败", res.errMsg);
        //   },
        //   complete(res) {
        //     console.log("showToast 调用结束", res.errMsg);
        //   },
        // });
      });
    })
    .catch(function (e) {
      console.error(e);
    });
}
const apiAuth = () => {
  try {
    Auth();
  } catch (err) {}
};
export { apiAuth };
