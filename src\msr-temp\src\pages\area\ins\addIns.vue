<template>
  <view class="ins-addIns">
    <view>
      <nut-form :model-value="state.params" ref="ruleForm">
        <view
          style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          "
          >基本信息</view
        >
        <view class="section">
          <nut-form-item label="申请人" prop="applicant" :label-width="100">
            <view style="font-size: 16px; text-align: right">
              {{ initHrInfo.empName }}({{ initHrInfo.empCode }})</view
            >
          </nut-form-item>
          <view v-if="state.sysJuInsAddFlag === '1'">
            <nut-form-item
              label="部门"
              prop="deptName"
              :label-width="100"
              required
              :rules="[{ required: true, message: '必填' }]"
            >
              <nut-input
                clearable
                input-align="right"
                :border="false"
                readonly
                @click="selectPostName"
              >
                <template #right>
                  <view
                    @click="selectPostName"
                    style="font-size: 16px; text-align: right"
                    ><text v-if="state.sysJuInsAddInfo.deptName">{{
                      state.sysJuInsAddInfo.deptName
                    }}</text
                    ><text
                      v-if="!state.sysJuInsAddInfo.deptName"
                      style="color: #c9cdd0"
                      >新增机构，审批通过后自动认领该机构到选择部门下</text
                    >
                  </view>
                  <img :src="arrow" class="arrowIcon" />
                </template>
              </nut-input>
            </nut-form-item>
            <nut-form-item label="岗位" prop="postName" :label-width="100">
              <view style="font-size: 16px; text-align: right">{{
                state.sysJuInsAddInfo.postName
              }}</view>
            </nut-form-item>
          </view>
          <view v-else>
            <nut-form-item label="岗位" prop="postName" :label-width="100">
              <view style="font-size: 16px; text-align: right">{{
                state.info.postName
              }}</view>
            </nut-form-item>
            <nut-form-item label="部门" prop="ancestors" :label-width="100">
              <view style="font-size: 16px">{{ state.info.ancestors }}</view>
            </nut-form-item>
          </view>

          <nut-form-item
            label="机构名称"
            prop="insName"
            required
            :label-width="100"
            :rules="[{ required: true, message: '请填写机构名称' }]"
          >
            <nut-input
              v-model="state.params.insName"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
              v-if="query.type"
            >
            </nut-input>
            <view style="font-size: 16px; text-align: right" v-else>{{
              state.params.insName
            }}</view>
          </nut-form-item>
          <nut-form-item
            label="机构别名"
            prop="insNameAlias"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.insNameAlias"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="上级机构"
            prop="superiorInsName"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.superiorInsName"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              style="opacity: 0; height: 0; width: 0; overflow: hidden"
              @click="selectClick('superiorInsName')"
              v-if="!state.params.superiorInsName"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
            <view
              v-else
              @click="selectClick('superiorInsName')"
              style="font-size: 16px; text-align: right"
              >{{ state.params.superiorInsName || `>` }}</view
            >
          </nut-form-item>

          <nut-form-item
            label="机构级别"
            prop="insGrade"
            :label-width="100"
            required
            :rules="[{ required: true, message: '请选择机构级别' }]"
          >
            <nut-input
              v-model="state.params.insGrade"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              readonly
              @click="selectClick('insGrade')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="机构等次"
            prop="insLevel"
            :label-width="100"
            required
            :rules="[{ required: true, message: '请选择机构等次' }]"
          >
            <nut-input
              v-model="state.params.insLevel"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              readonly
              @click="selectClick('insLevel')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="机构类型"
            prop="insType"
            required
            :label-width="100"
            :rules="[{ required: true, message: '请选择机构类型' }]"
          >
            <nut-input
              v-model="state.params.insType"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              readonly
              @click="selectClick('insProfessionType')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>

          <nut-form-item
            label="统一社会信用代码"
            prop="socialCreditCode"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.socialCreditCode"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="经济类型"
            prop="economicType"
            required
            :label-width="100"
            :rules="[{ required: true, message: '请选择经济类型' }]"
          >
            <nut-input
              v-model="state.params.economicType"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              readonly
              @click="selectClick('economicType')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="省市区"
            prop="district"
            required
            :label-width="100"
            :rules="[{ required: true, message: '请选择省市区' }]"
          >
            <nut-input
              v-model="state.params.district"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              readonly
              @click="selectClick('district')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>

          <nut-form-item
            label="定位"
            prop="address"
            :label-width="100"
            :rules="[{ required: true, message: '请选择定位位置' }]"
            required
          >
            <view
              style="font-size: 16px; text-align: right"
              @click="addressName('addressName')"
            >
              {{ state.params.address
              }}<img
                v-if="!state.params.address"
                :src="location_ins"
                class="location_ins"
            /></view>
          </nut-form-item>
          <nut-form-item
            label="地址"
            prop="address"
            required
            :label-width="100"
            :rules="[{ required: true, message: '请选择定位' }]"
          >
            <view style="font-size: 16px; text-align: right">
              {{ state.params.address }}</view
            >
          </nut-form-item>
          <nut-form-item
            label="产品"
            prop="productName"
            :label-width="100"
            required
            :rules="[{ required: true, message: '请选择产品' }]"
            v-if="state.sysJuInsAddFlag === '1'"
          >
            <nut-input
              v-model="state.params.productName"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              style="opacity: 0; height: 0; width: 0; overflow: hidden"
              @click="selectClick('productsName')"
              v-if="!state.params.productName"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
            <view
              v-else
              @click="selectClick('productsName')"
              style="font-size: 16px; text-align: right"
              >{{ state.params.productName || `>` }}</view
            >
          </nut-form-item>
        </view>

        <!-- <view
          style="
            height: 48px;
            line-height: 48px;
            font-size: 14px;
            padding: 0 16px;
            color: #869199;
          "
          >详细信息</view
        > -->
        <!-- <view class="section">
          <nut-form-item label="客户人数(人)" prop="customerNum" :label-width="100">
            <nut-input
              v-model="state.params.customerNum"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="20"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="日门诊量"
            prop="dailyOutPatientVolume"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.dailyOutPatientVolume"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="20"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item label="床位数(张)" prop="bedNum" :label-width="100">
            <nut-input
              v-model="state.params.bedNum"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="20"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="主要联系人"
            prop="primaryContact"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.primaryContact"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="20"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item label="联系电话" prop="telephone" :label-width="100">
            <nut-input
              v-model="state.params.telephone"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="20"
            >
            </nut-input>
          </nut-form-item>

          <nut-form-item
            label="特色专科"
            prop="specialSpecialties"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.specialSpecialties"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
            >
            </nut-input>
          </nut-form-item>

          <nut-form-item
            label="年度进药总额(万元)"
            prop="annualTotalDrugIntake"
            :label-width="140"
          >
            <nut-input
              v-model="state.params.annualTotalDrugIntake"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="20"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="是否被托管"
            prop="trusteeship"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.trusteeship"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              readonly
              @click="selectClick('trusteeship')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>

          <nut-form-item
            label="是否二次议价"
            prop="secondaryBargaining"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.secondaryBargaining"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              readonly
              @click="selectClick('secondaryBargaining')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
        </view> -->
      </nut-form>
      <FooterButton
        text="提交申请"
        :isLoading="isLoading"
        @click-button="clickRight"
      />
    </view>
    <PopupRadio ref="popupRadioRef" @radio-confirm="radioConfirm" />
    <SelectIns ref="selectInsRef" @ins-confirm="insConfirm" />
    <DistrictPopup ref="districtPopupRef" @district-confirm="districtConfirm" />
    <UserPopup ref="userPopupRef" @select-user="selectUser" />
    <AddProduct ref="addProductRef" @select-pro="selectPro" />
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
import CellClick from "../../../pages/components/cellClick/index.vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import PopupRadio from "../../../pages/components/popupRadio/popupRadio";
import { initiateIns, sysJuInsAddApi } from "../../../api/area.js";
import arrow from "../../../images/arrow.png";
import location_ins from "../../../images/location_ins.png";
import SelectIns from "./selectIns.vue";
import DistrictPopup from "../../../pages/components/districtPopup/index";
import { appCode, tenantId, h5Url, pcUrl } from "../../../utils/content";
import UserPopup from "../../../pages/area/components/userPopup.vue";
import AddProduct from "../components/addProduct.vue";
import { getProcessIdApi } from "../../../utils/content";
const feishuNotice = ref({
  appLink: `${h5Url}`,
  pcLink: `${pcUrl}`,
});
const initHrInfo = Taro.getStorageSync("initHrInfo");
const orgList = initHrInfo.orgList.filter((item) => item.postType === "1")[0];
const isLoading = ref(false);
const selectInsRef = ref(null);
const router = useRouter();
const query = router.params;
const popupRadioRef = ref(null);
const gdmapRef = ref(null);
const ruleForm = ref(null);
const districtPopupRef = ref(null);
const userPopupRef = ref(null);
const addProductRef = ref(null);
const state = reactive({
  rightText: "确定", //根据进来的情况判断

  info: {
    applicant: initHrInfo.empName,
    applicantCode: initHrInfo.empCode,
    postCode: orgList.code,
    postName: orgList.name,
    deptCode: orgList.deptCode,
    deptName: orgList.value,
    ancestors: orgList.ancestors,
    postIdList: initHrInfo.postIdList,
  },
  sysJuInsAddInfo: {
    applicant: initHrInfo.empName,
    applicantCode: initHrInfo.empCode,
    postCode: "",
    postName: "",
    deptCode: "",
    deptName: "",
    ancestors: "",
    jurCode: "",
    postIdList: initHrInfo.postIdList,
  },
  params: {
    type: "insert",
    insName: "",
    insLevel: "",
    insType: "",
    socialCreditCode: "",
    economicType: "",
    district: "",
    address: "",
    insGrade: "",
    deptName: "",

    // 经纬度
    // customerNum: "",
    // dailyOutPatientVolume: "",
    // bedNum: "",
    // primaryContact: "",
    // telephone: "",
    // specialSpecialties: "",
    // annualTotalDrugIntake: "",
    // trusteeship: "否",
    // secondaryBargaining: "否",
    latitude: "",
    longitude: "",
    superiorInsName: "",
    superiorInsCode: "",
    superiorInsMdmCode: "",
    insNameAlias: "",
    // specialSpecialties: "",
    productName: "",
    products: [],
  },
  masterValue: query.type === "add" ? "是" : "否",
  type: "",
  list: [
    { dictLabel: "是", dictValue: "是" },
    { dictLabel: "否", dictValue: "否" },
  ],
  sexValue: "",
  sysJuInsAddFlag: "0",
  processId: "",
});

const clickRight = async () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  ruleForm.value.validate().then(async ({ valid, errors }) => {
    if (valid) {
      isLoading.value = true;
      const params = {
        applyContent: {
          ...state.params,
        },

        enableWorkflow: true,
        applyType: "1",
        insName: state.params.insName,
        appCode: appCode,
        tenantId: tenantId,
        ...(state.sysJuInsAddFlag === "1" ? state.sysJuInsAddInfo : state.info),
        ...feishuNotice.value,
        channel: "2",
        processId: state.processId,
      };

      if (query.type === "add") {
        params.type = "insert";
      } else {
        params.type = "update";
      }
      const res = await initiateIns(params);
      isLoading.value = false;
      if (res.code === 200) {
        Taro.showToast({
          title: "成功",
          icon: "none",
          duration: 2000,
        });
        Taro.hideLoading();
        Taro.reLaunch({
          url: `/pages/ins_manage/index`,
        });
      }
    } else {
      Taro.hideLoading();
      console.log("error submit!!", errors);
    }
  });
};
const selectClick = (type) => {
  state.type = type;
  let options = [];
  if (type === "superiorInsName") {
    selectInsRef.value.open(state.params.insCode);
  }
  if (type === "insLevel") {
    options = Taro.getStorageSync("insLevel");
    popupRadioRef.value.open(options, state.params.insLevel);
  }
  if (type === "insProfessionType") {
    options = Taro.getStorageSync("insPprofessionType");
    popupRadioRef.value.open(options, state.params.insType);
  }
  if (type === "district") {
    options = Taro.getStorageSync("district");
    districtPopupRef.value.open([], "ins");
  }
  if (type === "economicType") {
    options = Taro.getStorageSync("economicType");
    popupRadioRef.value.open(options, state.params.economicType);
  }
  if (type === "insGrade") {
    options = Taro.getStorageSync("insGrade");
    popupRadioRef.value.open(options, state.params.insGrade);
  }

  // if (type === "trusteeship") {
  //   options = state.list;
  //   popupRadioRef.value.open(options, state.params.trusteeship);
  // }

  if (type === "secondaryBargaining") {
    options = state.list;
    popupRadioRef.value.open(options, state.params.secondaryBargaining);
  }
  if (type === "productsName") {
    if (!state.sysJuInsAddInfo.deptName) {
      return Taro.showToast({
        title: "请先选择部门",
        icon: "none",
        duration: 2000,
      });
    }
    const params = {
      type: "jur",
      jurCode: state.sysJuInsAddInfo.jurCode,
    };
    const checked = state.params.products.map((item) => item.name);
    addProductRef.value.open("all", params, checked);
  }
};

const addressName = () => {
  Taro.navigateTo({
    url: `/pages/components/gdmap`,
  });
};

const insConfirm = (ins) => {
  state.params.superiorInsName = ins.insName;
  state.params.superiorInsCode = ins.insCode;
  state.params.superiorInsMdmCode = ins.insMdmCode;
};

const radioConfirm = (val) => {
  if (state.type === "insLevel") {
    state.params.insLevel = val;
  }
  if (state.type === "insProfessionType") {
    state.params.insType = val;
  }
  if (state.type === "economicType") {
    state.params.economicType = val;
  }
  if (state.type === "trusteeship") {
    state.params.trusteeship = val;
  }

  if (state.type === "secondaryBargaining") {
    state.params.secondaryBargaining = val;
  }

  if (state.type === "insGrade") {
    state.params.insGrade = val;
  }
};

const districtConfirm = (value, codeList) => {
  state.params.district = value;
  // state.params.province = codeList[0] || "";
  // state.params.city = codeList[1] || "";
  // state.params.district = codeList[2] || "";
};
const selectPostName = () => {
  userPopupRef.value.open(state.sysJuInsAddInfo.postCode);
};
const getSysJuInsAddApi = async () => {
  const res = await sysJuInsAddApi();
  state.sysJuInsAddFlag = JSON.parse(res.msg).settingStatus;
  state.params = {
    ...state.params,
    ...JSON.parse(res.msg),
  };
};

const selectUser = (item) => {
  state.sysJuInsAddInfo.ancestors = item.ancestors;
  state.sysJuInsAddInfo.deptName = item.deptName;
  state.params.deptName = item.deptName;
  state.sysJuInsAddInfo.deptCode = item.deptCode;
  state.sysJuInsAddInfo.postCode = item.postCode;
  state.sysJuInsAddInfo.postName = item.postName;
  state.sysJuInsAddInfo.jurCode = item.jurCode;
};
const selectPro = (arr) => {
  state.params.products = arr;
  state.params.productName = arr.map((item) => item.name).join(",");
};
const getKey = async () => {
  state.processId = await getProcessIdApi("institution_apply");
};
onMounted(() => {
  if (query.type === "add") {
    Taro.setNavigationBarTitle({ title: "新增机构申请" });
    state.params.insName = decodeURIComponent(query.insName);
  } else {
    Taro.setNavigationBarTitle({ title: "变更机构申请" });
    state.params = JSON.parse(decodeURIComponent(query.info));
    state.params = {
      ...state.params,
      ...state.info,
    };
  }
  getSysJuInsAddApi();
  getKey();
});
useDidShow(() => {
  const location = Taro.getStorageSync("location");
  if (location) {
    state.params.latitude = location.location.lat;
    state.params.longitude = location.location.lng;
    state.params.address = location.name + location.address;
    Taro.removeStorageSync("location");
  }
});
</script>
<style lang="scss">
.ins-addIns {
  padding-bottom: 90px;

  .section {
    font-size: 14px;
    padding: 0 16px;
    background: #fff;

    .label {
      color: #1d212b;

      &::before {
        content: "*";
        color: red;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }

  .nut-cell__title {
    color: #1d212b;
  }

  .nut-input-left-box {
    margin-right: 10px;
  }

  .arrowIcon {
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }

  .location_ins {
    width: 24px;
    height: 24px;
  }

  .nut-cell-group__wrap {
    margin-top: 0;
    background: none;
  }

  .nut-cell__title {
    font-size: 16px;
  }

  // .nut-input__inner {
  //   white-space: pre-wrap !important;
  // }
  // .nut-input {
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-form-item__body__slots {
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-cell__value {
  //   display: block;
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-cell__value .nut-form-item__body {
  //   display: block;
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
}
</style>
