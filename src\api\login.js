import HTTPREQUEST from "@/servers/http";


export const login = (params) => {
  return HTTPREQUEST.post("/auth/login", params);
};

export const getUserInfo = (params) => {
  return HTTPREQUEST.get("/plt/user/getInfo", params);
};

export const appletLogin = (params) => {
  return HTTPREQUEST.post("/auth/social/applet/login", params);
};

// export const hrDetail = (params) => {
//   return HTTPREQUEST.get("/tum/hr/detail", params);
// };

export const hrDetail = (params) => {
  return HTTPREQUEST.get(`/msr/sys/detail`, params);
};
