<template>
  <view class="globa-checkbox-group">
    <nut-checkbox-group
      v-model="state.checkboxgroup"
      ref="group"
      v-for="item in options"
      :key="item.id"
      @change="changeBox"
    >
      <nut-checkbox :label="item.id" style="margin-top: 4px">
        <template #icon>
          <img :src="icon" alt="" class="radioIcon" />
        </template>
        <template #checkedIcon>
          <img :src="checkedIcon" alt="" class="radioIcon" />
        </template>
        {{ item.content }}
      </nut-checkbox>
    </nut-checkbox-group>
  </view>
</template>
<script setup>
import { reactive, defineEmits } from "vue";
import icon from "../../../images/radio_icon.png";
import checkedIcon from "../../../images/radio_checked_icon.png";
const emits = defineEmits(["emitChangeBox"]);
const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  options: {
    type: Array,
    default: () => [],
  },
});
const state = reactive({
  checkboxgroup: props.value,
});
const changeBox = (val) => {
  emits("emitChangeBox", val);
};
</script>
<style lang="scss">
.radioIcon {
  width: 16px;
  height: 16px;
  display: inline-block;
}
.globa-checkbox-group {
  .nut-checkbox__label--disabled {
    color: #1d1e1e !important;
  }
  .nut-checkbox {
    padding: 0;
  }
  .nut-checkbox__label {
    font-size: 14px;
  }
}
</style>
