<template>
  <view class="popupChexked">
    <GlobalPopup ref="popupRef">
      <view display="flex">
        <view class="check-group">
          <nut-checkbox-group
            style="
              max-height: 60vh;
              overflow: auto;
              padding: 0 16px;
              padding-bottom: 40px;
              background: #fff;
              margin-bottom: 8px;
            "
            v-model="state.checked"
          >
            <GlobalNutCheckbox
              v-for="item in state.checkedList"
              :value="item.dictValue"
              :key="item.dictValue"
            >
              <template #name>
                <view>
                  {{ item.dictLabel }}
                </view>
              </template>
            </GlobalNutCheckbox>
          </nut-checkbox-group>
        </view>
      </view>
      <view class="footerButtonSpe" style="z-index: 99" @click="ckeckConfirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import Taro, { useRouter } from "@tarojs/taro";
import { reactive, ref } from "vue";
import GlobalPopup from "@/pages/components/globalPopup/globalPopup.vue";
import GlobalNutCheckbox from "@/pages/components/globalNutCheckbox/globalNutCheckbox.vue";
const emit = defineEmits(["radio-confirm"]);
const popupRef = ref(null);
const state = reactive({
  checkedList: [],
  checked: [],
});

const open = (list, checked) => {
  document.body.style.overflow = "hidden";
  if (checked) {
    state.checked = checked.split(",");
  }
  console.log(state.checked, "扶植");
  popupRef.value.open();
  state.checkedList = list;
};
const ckeckConfirm = () => {
  emit("check-confirm", state.checked);
  document.body.style.overflow = "";

  popupRef.value.close();
  state.checked = [];
};

defineExpose({
  open,
});
</script>
<style lang="scss" scoped>
.popupChexked {
  background: #edeff5;
  .check-group {
    overflow-y: scroll;
    padding: 0;
    background: #f3f4f5;
  }

  .footerButtonSpe {
    margin-top: 8px;
    color: #2551f2;
    background: #fff;
    z-index: 99999;
    height: 50px;
    padding-top: 10px;
    text-align: center;
  }
  .nut-popup {
    // padding-top: 6px;
    // padding-bottom: 110px;
    background: #edeff5;
  }
  .nut-radio-group {
    padding: 0 16px;
  }
}
</style>
