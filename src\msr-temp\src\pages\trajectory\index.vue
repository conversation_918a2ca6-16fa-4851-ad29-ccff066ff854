<template>
  <div>
    <div id="container"></div>
    <!--    <div class="info">-->
    <!--      信息窗体可在地图任意位置打开-->
    <!--    </div>-->
    <div class="position-fix flex items-center gap-[5px]" @click="position()">
      <Locationg3 />
      <div>手动定位</div>
    </div>
    <div class="filter" @click="open()">筛选</div>
    <div class="user-card">
      <div class="name-bar">
        <div class="name">
          {{ state.empName }}&nbsp;&nbsp;&nbsp;&nbsp;{{
            dayjs(timer).format("YYYY-MM-DD")
          }}
          <span style="color: #ffb637" v-if="isToday(dayjs(timer))">今天</span>
        </div>
        <div class="dept">部门：{{ state.dept }}</div>
        <div class="dept">CRM运行状态下，系统每15分钟自动抓取定位</div>
      </div>
    </div>

    <view class="userPopup">
      <GlobalPopup ref="popupRef">
        <div class="timer-filter" @click="show = true">
          时间：{{ dayjs(timer).format("YYYY-MM-DD") }}
        </div>
        <TopSearch @top-search="search" placeholder="请输入姓名" />
        <view
          v-if="!state.flag && !state.cusOption.length"
          class="result-loading"
          >搜索暂无数据</view
        >
        <nut-radio-group
          @change="onChange"
          v-model="state.radioChecked"
          text-position="left"
        >
          <GlobalNutRadio
            v-for="item in state.cusOption"
            :value="item.empCode"
            :key="item.empCode"
          >
            <template #name>
              <view>
                <text
                  >{{ item.postName }} ｜ {{ item.deptName }} ｜
                  {{ item.empName }}
                </text>
              </view>
            </template>
          </GlobalNutRadio>
        </nut-radio-group>

        <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
          >确定</view
        >
      </GlobalPopup>
    </view>
    <nut-popup
      v-model:visible="show"
      position="bottom"
      z-index="999999"
      :close-on-click-overlay="false"
    >
      <nut-date-picker
        v-model="timer"
        :min-date="min"
        :max-date="max"
        :three-dimensional="false"
        @confirm="timerConfirm"
        @cancel="show = false"
      ></nut-date-picker>
    </nut-popup>
  </div>
</template>
<script setup>
import { onMounted, reactive, ref } from "vue";
import Taro, { useDidHide, useDidShow } from "@tarojs/taro";
import dayjs from "dayjs";
import { subPage } from "../../api/area";
import { lanListApi } from "./../../api/finder";
import GlobalPopup from "../../pages/components/globalPopup/globalPopup.vue";
import TopSearch from "./../../pages/components/topSearch/topSearch.vue";
import GlobalNutRadio from "./../../pages/components/globalNutRadio/globalNutRadio.vue";
const initUserInfo = Taro.getStorageSync("initUserInfo");
const initHrInfo = Taro.getStorageSync("initHrInfo");
import { finderParams, positionEventParams } from "./finder";
import { Locationg3 } from "@nutui/icons-vue-taro";

const props = defineProps({
  finderAppId: {
    type: Number,
    required: true,
  },
});

const BMap = ref();

const state = reactive({
  radioChecked: "",
  cusOption: [],
  positionList: [],
  params: {
    postIdList: initHrInfo.postIdList,
    search: "",
    pageNum: 1,
    pageSize: 200,
  },
  flag: false,
  timer: null,
  total: 0,
  empName: initUserInfo.user?.nickName,
  dept: initUserInfo.user?.dept?.deptName,
  empCode: initUserInfo.user?.userName,
});

const pEventMap = ref({});

const show = ref(false);
const showMap = ref(true);
const min = new Date(2020, 0, 1);
const max = new Date(2025, 10, 1);
const timer = ref(new Date());

const markerList = ref([]);

const timerConfirm = ({ selectedValue }) => {
  console.log(selectedValue);
  show.value = false;
};

const popupRef = ref(null);
const debounce = (fn, wait) => {
  if (state.timer !== null) {
    clearTimeout(state.timer);
  }
  state.timer = setTimeout(fn, wait);
};
const onChange = (v) => {};
const search = (name) => {
  state.radioChecked = "";
  state.params.search = name;
  getCusOption();
};

const getCusOption = async () => {
  state.flag = true;
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  state.cusOption = [];
  const res = await subPage(state.params);
  Taro.hideLoading();
  // Taro.setStorageSync("bu", "");
  state.flag = false;
  state.cusOption = res.data.rows || [];
};

const getCusOptionMore = async () => {
  state.flag = true;
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  const res = await subPage(state.params);
  Taro.hideLoading();
  // Taro.setStorageSync("bu", "");
  state.flag = false;
  state.cusOption = [...state.cusOption, ...(res.data.rows || [])];
};

const loadMore = async () => {
  state.params.pageNum = state.params.pageNum + 1;
  await getCusOptionMore();
};

const confirm = async () => {
  // if (!state.radioChecked) return;

  const arr = state.cusOption.filter(
    (item) => item.empCode === state.radioChecked
  );
  // emit("select-user", arr[0]);
  // arr[0].user[0].value
  console.log(arr, "选择的姓名");
  if (arr[0]) {
    state.empName = arr[0].empName;
    state.dept = arr[0].deptName;
    state.empCode = arr[0].empCode;
  }
  Taro.showLoading();
  await getLanList();
  popupRef.value.close();
  state.radioChecked = "";
  Taro.hideLoading();
};
const open = (code) => {
  state.radioChecked = code;
  popupRef.value.open();
  state.params.search = "";
  getCusOption();
};

const position = () => {
  Taro.navigateTo({
    url: "/pages/trajectory/gdmap/index",
  });
};

const uniqueArray = (arr) =>
  arr.reduce(function (acc, obj) {
    const found = acc.find(function (item) {
      return (
        (item.GS_longitude === obj.GS_longitude &&
          item.GS_latitude === obj.GS_latitude) ||
        item.$event_time === obj.$event_time
      );
    });
    if (!found) {
      acc.push(obj);
    }
    return acc;
  }, []);

function groupBy(list, keyGetter) {
  try {
    const map = {};
    list.forEach((item) => {
      const key = keyGetter(item);
      const collection = map[key];
      if (!collection) {
        map[key] = [item];
      } else {
        collection.push(item);
      }
    });
    return map;
  } catch (e) {}
}

const getLanList = async () => {
  try {
    const query = {
      empName: state.empName || initUserInfo.user?.nickName,
      empCode: state.empCode || initUserInfo.user?.userName,
      startTime: dayjs(timer.value).startOf("day").unix(),
      endTime: dayjs(timer.value).endOf("day").unix(),
      finderAppId: props.finderAppId,
    };

    const str2 = await lanListApi({
      queryBody: JSON.stringify(positionEventParams(query)),
    });
    const res2 = JSON.parse(str2.data);
    const _pEventList = uniqueArray(
      ((res2 && res2.data && res2.data[0].data_item_list) || [])
        .map((d) => d.common_params)
        .filter((item) => item.GS_longitude && item.GS_latitude)
        .sort(
          (a, b) =>
            dayjs(a.$event_time).valueOf() - dayjs(b.$event_time).valueOf()
        )
    );
    state.positionList = _pEventList;
    await drawLine(state.positionList);
  } catch (e) {
    Taro.hideLoading();
  }
};

const getConvert = (longitude, latitude) =>
  new Promise((res) => {
    Taro.request({
      url: `https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${longitude},${latitude}&key=0446b950e065d1e0b98a9e97f08dfbff&coordsys=gps`,
      success: (result_before) => {
        const [lon, lat] = result_before.data.locations.split(",");
        res({ lon, lat });
      },
    });
  });

const drawLine = async (positionList) => {
  if (!positionList.length) {
    Taro.showModal({
      title: "",
      content: "当日无轨迹",
      success: function (res) {
        if (res.confirm) {
          console.log("用户点击确定");
        } else if (res.cancel) {
          console.log("用户点击取消");
        }
      },
    });
    BMap.value = new AMap.Map("container", {
      resizeEnable: true,
      zoom: 13,
    });
  }

  const gdPositionList = [];
  for (let item of positionList) {
    if (item.GS_longitude && item.GS_latitude) {
      const { lon, lat } = await getConvert(
        item.GS_longitude,
        item.GS_latitude
      );
      gdPositionList.push({
        ...item,
        lon,
        lat,
      });
    }
  }

  const hasCenter =
    [
      gdPositionList[0].lon || state.lon,
      gdPositionList[0].lat || state.lat,
    ].filter(Boolean).length === 2;
  let map = null;
  if (hasCenter) {
    BMap.value = new AMap.Map("container", {
      resizeEnable: true,
      center: [
        gdPositionList[0].lon || state.lon,
        gdPositionList[0].lat || state.lat,
      ],
      zoom: 13,
    });
  } else {
    BMap.value = new AMap.Map("container", {
      resizeEnable: true,
      zoom: 13,
    });
  }

  BMap.value.remove(markerList.value);
  BMap.value.setFitView();

  if (!gdPositionList.length) {
  } else {
    //多个点实例组成的数组
    markerList.value = gdPositionList.map((d, index) => {
      const range =
        pEventMap.value[`${d.GS_longitude},${d.GS_latitude}`] &&
        pEventMap.value[`${d.GS_longitude},${d.GS_latitude}`].sort(
          (a, b) =>
            dayjs(a.$event_time).valueOf() - dayjs(b.$event_time).valueOf()
        );
      const time1 = range && range[0] && range[0].$event_time;
      const time2 =
        range && range[range.length - 1] && range[range.length - 1].$event_time;
      let content = ``;
      if (time1 && time2 && time1 !== time2) {
        content = `<div class='info'>轨迹${
          index + 1
        }：<br />${time1} ~ ${time2}</div>`;
      } else {
        content = `<div class='info'>轨迹${index + 1}：${d.$event_time}</div>`;
      }
      return new AMap.Marker({
        position: new AMap.LngLat(d.lon, d.lat), //经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
        label: {
          content,
        },
      });
    });
    BMap.value.add(markerList.value);

    AMapUI.load(
      ["ui/misc/PathSimplifier", "lib/$"],
      function (PathSimplifier, $) {
        if (!PathSimplifier.supportCanvas) {
          alert("当前环境不支持 Canvas！");
          return;
        }

        const pathSimplifierIns = new PathSimplifier({
          zIndex: 100,
          //autoSetFitView:false,
          map: BMap.value, //所属的地图实例

          getPath: function (pathData, pathIndex) {
            var points = pathData.points,
              lnglatList = [];

            for (var i = 0, len = points.length; i < len; i++) {
              lnglatList.push(points[i].lnglat);
            }

            return lnglatList;
          },
          getHoverTitle: function (pathData, pathIndex, pointIndex) {
            if (pointIndex >= 0) {
              //point
              return pathData.name + "，" + pathData.points[pointIndex].name;
            }

            return pathData.name + "，点数量" + pathData.points.length;
          },
          renderOptions: {
            renderAllPointsIfNumberBelow: 100, //绘制路线节点，如不需要可设置为-1
          },
        });

        window.pathSimplifierIns = pathSimplifierIns;
        // [{
        //   name: "点a",
        //   lnglat: [118.356843532987,31.350090603299]
        // },{
        //   lnglat: [118.356604275174,31.350109320747]
        // },{
        //   lnglat: [118.356804470487,31.350087619358]
        // },{
        //   lnglat: [118.356976725261,31.350391167535]
        // }]
        //设置数据
        pathSimplifierIns.setData([
          {
            name: "路线0",
            points: gdPositionList.map((d) => ({
              lnglat: [d.lon, d.lat],
            })),
          },
        ]);

        //选中路线0
        pathSimplifierIns.setSelectedPathIndex(0);

        pathSimplifierIns.on("pointClick", function (e, info) {
          console.log("Click: " + info.pathData.points[info.pointIndex].name);
        });
      }
    );
  }
};

// 判断给定日期是否是今天的函数
const isToday = (date) => {
  const today = dayjs().startOf("day"); // 今天的开始，忽略时间
  return date.isSame(today, "day"); // 比较日期是否相同
};

const destroyMap = () => {
  //销毁地图，并清空地图容器
  BMap.value.destroy();
  //地图对象赋值为null
  BMap.value = null;
  //清除地图容器的 DOM 元素
  //   document.getElementById("container").remove(); //"container" 为指定 DOM 元素的id
  // showMap.value = false
};

useDidShow(() => {
  // showMap.value = true;
  const init = async () => {
    BMap.value = new AMap.Map("container", {
      resizeEnable: true,
      zoom: 13,
    });
    await getLanList();
  };
  init();
});

useDidHide(() => {
  destroyMap();
});
</script>

<style lang="scss">
#container {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 800px;
}
.custom-input-card {
  width: 18rem;
}

.custom-input-card .btn:last-child {
  margin-left: 1rem;
}

.content-window-card {
  position: relative;
  width: 23rem;
  padding: 0.75rem 0 0 1.25rem;
  box-shadow: none;
  bottom: 0;
  left: 0;
}

.content-window-card p {
  height: 2rem;
}

.filter {
  position: fixed;
  right: 20px;
  top: 20px;
  font-size: 16px;
  z-index: 999;
  background-color: #92a8f8;
  color: #fff;
  padding: 4px 8px;
}

.position-fix {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: 160px;
  font-size: 16px;
  z-index: 999;
  background-color: #2551f2;
  color: #fff;
  padding: 4px 12px;
  border-radius: 80px;
  display: flex;
  gap: 5px;
  align-items: center;
}
.user-card {
  position: fixed;
  z-index: 999;
  width: 80vw;
  background-color: #ffffff;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px;
  border-radius: 8px;

  .name,
  .dept {
    font-size: 12px;
    word-break: break-all;
  }
}

.userPopup {
  background: #f4f5f7;
  .timer-filter {
    padding: 16px 16px 0 16px;
    font-size: 16px;
  }
  .result-loading {
    color: #869199;
    font-size: 12px;
    text-align: center;
    margin: 16px 0;
  }
  .nut-radio-group {
    max-height: 400px;
    overflow: hidden;
    overflow-y: scroll;
    padding: 0 16px;
  }
  .nut-radio {
    overflow: hidden;
    overflow-y: scroll;
    background: #fff;
  }

  .footer-comfirm {
    height: 60px;
    padding: 16px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    padding-right: 0;
  }
}

.nut-overlay {
  z-index: 8888 !important;
}
</style>
