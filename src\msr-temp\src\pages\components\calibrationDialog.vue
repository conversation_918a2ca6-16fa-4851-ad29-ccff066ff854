<template>
  <view class="calibrationDialog">
    <GlobalPopup ref="popupRef" :style="{ height: '80%', }">
      <view style="margin: 0 16px;">
        <view class="row">
          <view class="label">
            省市区
          </view>
          <view class="result">
            {{ state.params.pcValue }}
          </view>
        </view>
        <nut-divider style="color: #f4f5f7;margin: 0;" />
        <view class="row">
          <view class="label">
            机构名称
          </view>
          <view class="result">
            {{ state.params.insName }}
          </view>
        </view>
        <nut-divider style="color: #f4f5f7;margin: 0;" />
      </view>
     
      <view class="address">
        <nut-radio-group v-model="state.radioChecked" text-position="left">
          <GlobalNutRadio v-for="item in state.pois" :value="item.id" :key="item.id">
            <template #name>
              <view @click="selectAdress(item)">
                <view :class="item.id === state.radioChecked ? 'color25' : ''">{{ item.name }}</view>
                <view style="font-size: 15px;" class="item-addres" :class="item.id === state.radioChecked ? 'color25' : ''">{{ item.address }}</view>
              </view>
            </template>
          </GlobalNutRadio>
        </nut-radio-group>
      </view>
      <view style="background:#F4F5F7;padding-top: 8px;">

        <view
          class="footer-button"
          @click="clickAddButton"
          style="z-index: 999999"
          >确定</view
        >
      </view>
     
    </GlobalPopup>

  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import Taro from "@tarojs/taro";
import GlobalPopup from "./globalPopup/globalPopup.vue";
import GlobalNutRadio from "./globalNutRadio/globalNutRadio.vue";
import FooterButton from "./footerButton/index.vue";
const emit = defineEmits(["select-location"]);
const state = reactive({
  params: {
    insName: '',
    province: '',
    city: '',
    district: '',
    pcValue: ''
  },
  info: {},
  radioChecked: '',
  pois: [],
});
const popupRef = ref(null);

const open = (info) => {
  state.params = {
    province: info.province,
    city: info.city,
    district: info.district,
    insName: info.insName || info.institutionName,
    pcValue: `${info.province || ''}/${info.city || ''}/${info.district || ''}`
  };
  popupRef.value.open();

  autoInput()
};

// // 获取搜索信息
const autoInput = () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  AMap.plugin("AMap.AutoComplete", function () {
    var autocomplete = new AMap.AutoComplete();
    console.log("查询定位参数", `${state.params.province || ''}${state.params.city || ''}${state.params.district || ''}${state.params.insName || ''}`)
    autocomplete.search(`${state.params.province || ''}${state.params.city || ''}${state.params.district || ''}${state.params.insName || ''}`, function (status, result) {
      if (status === "complete") {
        console.log("1111", result);
        state.pois = result?.tips;
        Taro.hideLoading();
      } else {
        Taro.hideLoading();
        Taro.showToast({
          title: result,
          icon: "none",
          duration: 2000,
        });
      }
    });
  });
};
const selectAdress = (item) => {

};
const clickAddButton = () => {
  const location = state.pois.filter(
    (item) => item.id === state.radioChecked
  )[0];
  emit('select-location', location)
  popupRef.value.close()
  state.radioChecked = ''
  state.pois = []
}
defineExpose({
  open,
});
</script>
<style lang="scss" scoped>
.calibrationDialog {

  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  padding: 14px 0;
    font-size: 16px;
  }

  .result {
    color: rgb(134, 145, 153);

  }

  .address {
    padding: 0 12px;
    background: #fff;
    height: 350px;
    overflow: hidden;
    overflow-y: scroll;
    .item-addres {
      color: rgb(134, 145, 153);
    }
    .color25 {
    color: #2551f2;
    }
  }
  .footer-button {
    color: #2551f2;
    background-color: #ffffff;
    height: 40px;
    padding-top: 14px;
    text-align: center;
    
  }
}
</style>
