<template>
  <view class="institutionalType">
    <view class="footerPopup">
      <view class="title" @click="clickTitle">
        <img :src="line" alt="" />
      </view>
      <view class="tags">
        <view
          v-for="item in state.footerPopupList"
          :key="item"
          @click="go(item)"
          :class="{
            lsIconTags: item.text === '临时拜访',
            planIconTags: item.text === '拜访计划',
          }"
        >
          <view class="tagContent">
            <view class="contentImg"><img :src="item.src" alt="" /></view>
            <view class="contentText">{{ item.text }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { reactive } from "vue";
import lsIcon from "@/images/Frame.png";
import planIcon from "@/images/Frame2.png";
import line from "@/images/Line38.png";
import cus_ins from "@/images/cus_ins.png";
import adress from "@/images/location_a.png";
const props = defineProps({
  source: {
    type: String,
    default: "",
  },
});
const state = reactive({
  footerPopupList: [
    {
      src: lsIcon,
      text: "临时拜访",
      icon: "arrow",
      url: "/pages/institutionalVisits/sign/signIn",
    },
    {
      src: planIcon,
      text: "拜访计划",
      icon: "arrow",
      url: "/pages/institutionalVisits/creacWaitplan/index",
    },
  ],
});
const emit = defineEmits(["clickTitle", "customer-router"]);
const go = (item) => {
  let url = item.url;
  if (props.source) {
    emit("customer-router", url);
  } else {
    Taro.navigateTo({ url });
  }

  emit("clickTitle", false);
};
</script>
<style lang="scss">
.institutionalType {
  .footerPopup {
    .title {
      text-align: center;

      img {
        width: 40px;
        display: inline-block;
        height: 3px;
      }
    }
  }

  .tags {
    height: 98px;
    padding: 16px;
    display: flex;

    .tagContent {
      display: flex;
      padding: 0 16px;
      margin-top: 16px;
      .contentImg {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.4);
        text-align: center;
        line-height: 40px;
        margin-right: 8px;
        img {
          display: inline-block;
          vertical-align: middle;
          width: 30px;
          height: 30px;
        }
      }

      .contentText {
        font-size: 16px;
        line-height: 45px;
      }
    }
    .lsIconTags {
      width: 164px;
      height: 72px;
      border-radius: 8px;
      background: rgba(255, 182, 55, 0.1);
      margin-right: 15px;
    }
    .planIconTags {
      width: 164px;
      height: 72px;
      border-radius: 8px;
      background: rgba(37, 81, 242, 0.1);
    }
  }
}
</style>
