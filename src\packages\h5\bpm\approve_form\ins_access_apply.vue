<template>
  <view class="page-container">
    <view class="content-wrapper">
      <!-- Header -->
      <view class="header">
        <text class="title">
          {{ props.formData.insName }} {{ props.formData.insMdmCode }}
        </text>
      </view>

      <view class="info-section">
        <!-- Product Info -->
        <view class="info-item">
          <text class="label">产品代码</text>
          <text class="value">{{ props.formData.productCode }}</text>
        </view>
        <view class="info-item">
          <text class="label">产品简称</text>
          <text class="value">{{ props.formData.productName }}</text>
        </view>
        <!-- Position Info -->
        <view class="info-item">
          <text class="label">岗位信息</text>
          <text class="value">{{ props.formData.postName }}</text>
        </view>
        <view class="info-item">
          <text class="label">部门信息</text>
          <text class="value">{{ props.formData.deptName }}</text>
        </view>
      </view>

      <!-- File Preview -->
      <view class="upload-section">
        <text class="upload-title">证明文件</text>
        <view class="upload">
          <view
            class="item-img"
            v-for="(item, index) in state.fileList"
            :key="index"
          >
            <img
              :src="item.src"
              style="width: 80px; height: 80px"
              @click="handlePreview(index)"
            />
          </view>
        </view>
        <nut-image-preview
          :autoplay="0"
          :show="showPreview"
          :images="state.fileList"
          @close="hideImgPreview"
          :init-no="imgPreviewIndex"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { imageUrl } from "@/bpm-temp/src/api/bpm";
import dayjs from "dayjs";

const props = defineProps({
  formData: {
    type: Object,
    default: {},
  },
  formType: {
    type: String,
    default: "",
  },
});

const state = reactive({
  fileList: [],
});
const showPreview = ref(false);
const imgPreviewIndex = ref(0);
const handlePreview = (index) => {
  imgPreviewIndex.value = index;
  showImgPreview();
};
const showImgPreview = () => {
  showPreview.value = true;
};

const hideImgPreview = () => {
  showPreview.value = false;
};

onMounted(() => {
  props.formData = props.formData || {};
  if (props.formData.fileList) {
    props.formData.ossIdList = [];
    props.formData.fileList = JSON.parse(props.formData.fileList);
    props.formData.fileList.forEach(async (i) => {
      const res = await imageUrl(i);
      state.fileList.push({ src: res.data?.rows?.[0]?.url });
      console.log(state.fileList, "state.fileList");
    });
  }
});
</script>

<style scoped>
.page-container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding: 12px;
}

.content-wrapper {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.applicant-info {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.info-text {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 8px;
}

.info-section {
  display: flex;
  flex-wrap: wrap;
  padding: 12px 16px;
  gap: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.info-item {
  width: calc(50% - 4px);
  margin-bottom: 12px;
}

.label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
  display: block;
}

.value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.certificate-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.description {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
  display: block;
  line-height: 1.5;
}

.notice-box {
  background-color: #f0f7ff;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.notice-title {
  font-size: 13px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
  display: block;
}

.condition-list {
  list-style-type: decimal;
  padding-left: 20px;
  margin: 0;
}

.condition-item {
  margin-bottom: 12px;
}

.condition-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.condition-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  display: block;
}

.upload-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.upload-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.image-uploader {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
}

.image-item {
  position: relative;
  width: calc(33.33% - 8px);
  height: 100px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.action-section {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.action-section :deep(.nut-button) {
  flex: 1;
  border-radius: 8px;
  font-size: 14px;
  height: 40px;
}
</style>
