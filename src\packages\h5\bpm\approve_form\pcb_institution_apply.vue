<template>
  <view class="bg-white px-[16px]">
    <view>
      <view class="text-[#1D212B] text-[14px] py-[10px]">基本信息</view>
      <view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >部门</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.ancestors
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >岗位</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.postName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >机构名称</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >机构别名</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insNameAlias
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >上级机构</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.superiorInsName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >机构等级</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insLevel
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >机构类型</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insType
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >统一社会信用代码</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.socialCreditCode
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >经济类型</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.economicType
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >省市区</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.district
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >地址</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.address
          }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
const props = defineProps(["formData"]);
</script>
<style lang="scss"></style>
