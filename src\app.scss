@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";
// 不使用 sass 就这么写
// @tailwind base;
// @tailwind components;
// @tailwind utilities;
* {
  animation-play-state: paused;
}
* {
  animation-play-state: paused;
}
.taro_router > .taro_page {
  background-color: #edeff5 !important;
}
.taro-model__btn.taro-model__confirm {
  color: #2551f2 !important;
}
.taro-modal__content {
  border-radius: 16px !important;
}
.taro-modal__text {
  font-size: 16px !important;
}
.taro_page {
  // overflow-y: hidden !important;
}
input::placeholder {
  color: #c9cdd0 !important;
}

// nut-ui var
:root {
  --nut-primary-color: #2551f2;
  --nut-primary-color-end: #2551f2;
}
