<template>
  <view class="creacWaitplan" style="background-color: #fff">
    <view class="select-cus">
      <CellClick
        @selectClick="selectCusClick"
        :value="state.params.customerName"
        :required="true"
        label="客户"
        placeholder="请选择"
        :phRight="true"
      />
      <nut-cell
        title="医疗机构"
        class="waitplan"
        :desc="state.params.institutionName"
      ></nut-cell>
      <nut-cell
        title="科室"
        class="waitplan"
        :desc="state.params.insDeptName"
      ></nut-cell>
      <CellClick
        @selectClick="openTime"
        :value="state.visitTimeText"
        :required="true"
        label="计划拜访时间"
        placeholder="请选择"
        :phRight="true"
      />
      <CellClick
        @selectClick="selectTypeClick"
        :value="state.visitTypeName"
        label="拜访类型"
        placeholder="请选择"
        :phRight="true"
      />
      <view>
        <view style="font-size: 15px; margin: 10px 0">计划拜访内容</view>
        <nut-textarea
          v-model="state.params.planVisitContent"
          limit-show
          max-length="200"
          placeholder="请输入计划拜访内容"
        />
      </view>
    </view>

    <VisitTypePopup
      @visit-type="visitTypeSelect"
      ref="visitTypePopupRef"
    ></VisitTypePopup>

    <FooterButton text="保存" @click-button="clickButton" />
  </view>
  <CusPopup ref="cusPopupRef" @select-cus="selectCus" />
  <TimePopup ref="timePopupRef" @confirm-time="confirmTime" />
</template>
<script setup>
import { onMounted, reactive, ref } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import FooterButton from "../../../pages/components/footerButton/index";
import Taro, { useRouter } from "@tarojs/taro";
import CellClick from "../../../pages/components/cellClick/index.vue";
import VisitTypePopup from "../components/visitTypePopup";
import CusPopup from "../components/cusPopup";
import { institutionAdd } from "../../../api/institutionalVisitsApi.js";

import TimePopup from "./components/timePopup";

const visitTypePopupRef = ref(null);
const cusPopupRef = ref(null);
const timePopupRef = ref(null);
const state = reactive({
  visitTypeName: "院内拜访",
  visitTimeText: "",
  params: {
    institutionName: "",
    institutionCode: "",
    customerName: "",
    customerCode: "",
    insDeptName: "",
    visitTypeCode: "0",
    planVisitContent: "",
    visitTime: "",
    completeTime: "",
    planFlag: "1", //表示计划拜访
    customerLevelCode: "",
    institutionTypeCode: "",
    insLat: null,
    insLon: null,
  },
});
const router = useRouter();
const query = router.params;
const selectCusClick = () => {
  cusPopupRef.value.open();
};
const selectCus = (obj) => {
  state.params.institutionCode = obj.insCode;
  state.params.institutionName = obj.insName;
  state.params.customerName = obj.customerName;
  state.params.customerCode = obj.customerCode;
  state.params.insDeptName = obj.insDeptName;
  state.params.customerLevelCode = obj.level;
  state.params.institutionTypeCode = obj.economicType;
  state.params.insLat = obj.latitude;
  state.params.insLon = obj.longitude;
};

const selectTypeClick = () => {
  visitTypePopupRef.value.open();
};
const visitTypeSelect = (name, code) => {
  console.log("name, code", name, code);
  state.visitTypeName = name;
  state.params.visitTypeCode = code;
};

const openTime = () => {
  timePopupRef.value.open(state.params.visitTime, state.params.completeTime);
};

const confirmTime = (start, end) => {
  state.visitTimeText =
    dayjs(start).format("YYYY-MM-DD HH:mm") +
    " - " +
    dayjs(end).format("HH:mm");
  state.params.visitTime = start;
  state.params.completeTime = end;
};
const clickButton = async () => {
  if (!state.params.customerCode) {
    Taro.showToast({
      title: "请选择拜访客户",
      icon: "none",
    });
    return;
  }
  if (!state.params.visitTime) {
    Taro.showToast({
      title: "请选择计划拜访时间",
      icon: "none",
    });
    return;
  }
  state.params.visitTime = dayjs(state.params.visitTime).format(
    "YYYY-MM-DD HH:mm:00"
  );
  state.params.completeTime = dayjs(state.params.completeTime).format(
    "YYYY-MM-DD HH:mm:00"
  );

  const res = await institutionAdd(state.params);
  Taro.navigateBack({
    delta: 1,
  });
};

onMounted(() => {
  Taro.setNavigationBarTitle({
    title: "新建拜访计划",
  });
  if (query.source) {
    const customerQuery = Taro.getStorageSync("customerQuery");
    state.params.institutionCode = customerQuery.insCode;
    state.params.institutionName = customerQuery.insName;
    state.params.customerName = customerQuery.customerName;
    state.params.customerCode = customerQuery.customerCode;
    state.params.insDeptName = customerQuery.insDeptName;
    state.params.customerLevelCode = customerQuery.level;
    state.params.institutionTypeCode = customerQuery.economicType;
    state.params.insLat = customerQuery.latitude;
    state.params.insLon = customerQuery.longitude;
    Taro.removeStorageSync("customerQuery");
  }
});
</script>
<style lang="scss">
.waitplan .nut-cell__value {
  font-size: 16px;
}
.creacWaitplan {
  .select-cus {
    padding: 16px;
    background: #fff;
    border-radius: 8px;
  }
}
</style>
