<template>
  <view class="addPharmacy">
    <view>
      <nut-form :model-value="state.params" ref="ruleForm">
        <view class="title">基本信息</view>
        <view class="section">
          <nut-form-item
            label="药店名称"
            prop="dsName"
            required
            :label-width="100"
            :rules="[{ required: true, message: '请填写药店名称' }]"
          >
            <nut-input
              v-model="state.params.dsName"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item label="药店别名" prop="dsNameAlias" :label-width="100">
            <nut-input
              v-model="state.params.dsNameAlias"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
            >
            </nut-input>
          </nut-form-item>

          <nut-form-item
            label="药店类型"
            prop="dsType"
            required
            :label-width="100"
            :rules="[{ required: true, message: '请选择药店类型' }]"
          >
            <nut-input
              v-model="state.params.dsType"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              :max-length="20"
              readonly
              @click="selectClick('pharmacyType')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item label="区域类型" prop="dsAreaType" :label-width="100">
            <nut-input
              v-model="state.params.dsAreaType"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              :max-length="20"
              readonly
              @click="selectClick('areaType')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="是否医保定点"
            prop="fixedPoint"
            :label-width="115"
          >
            <nut-input
              v-model="state.params.fixedPoint"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              :max-length="20"
              readonly
              @click="selectClick('fixedPoint')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="省市区"
            prop="districtValue"
            required
            :label-width="100"
            :rules="[{ required: true, message: '请选择省市区' }]"
          >
            <nut-input
              v-model="state.params.districtValue"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              readonly
              @click="selectClick('district')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="地址"
            prop="address"
            :label-width="100"
            :rules="[{ required: true, message: '请选择定位位置' }]"
            required
          >
            <nut-input
              v-model="state.params.address"
              clearable
              input-align="right"
              :border="false"
              placeholder=""
              :max-length="100"
            >
              <template #right>
                <img
                  :src="location_ins"
                  class="location_ins"
                  @click="addressName"
                />
              </template>
            </nut-input>
            <!-- <view
              v-else
              @click="addressName"
              style="font-size: 16px; text-align: right"
              >{{ state.params.address }}</view
            > -->
          </nut-form-item>

          <nut-form-item label="法人" prop="legalPerson" :label-width="100">
            <nut-input
              v-model="state.params.legalPerson"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="统一社会信用代码"
            prop="socialCreditCode"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.socialCreditCode"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="经营期限"
            prop="businessTerm"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.businessTerm"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              :max-length="20"
              readonly
              @click="selectClick('businessTerm')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="上级药店"
            prop="superiorDsName"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.superiorDsName"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              readonly
              @click="selectClick('superiorDsName')"
              v-if="!state.params.superiorDsName"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
            <view
              v-else
              @click="selectClick('superiorDsName')"
              style="font-size: 16px; text-align: right"
              >{{ state.params.superiorDsName }}</view
            >
          </nut-form-item>
          <nut-form-item
            label="上级药店编码"
            prop="superiorDsMdmCode"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.superiorDsMdmCode"
              clearable
              input-align="right"
              :border="false"
              readonly
            >
            </nut-input>
          </nut-form-item>

          <!-- <CellClick
            :value="state.params.district"
            label="省市区"
            placeholder="请选择"
            required
          /> -->
        </view>
        <view class="title">经营信息</view>
        <view class="section">
          <nut-form-item label="药店性质" prop="dsNature" :label-width="100">
            <nut-input
              v-model="state.params.dsNature"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              :max-length="20"
              readonly
              @click="selectClick('pharmacyNature')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="支付类型"
            prop="economicType"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.economicType"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              :max-length="20"
              readonly
              @click="selectClick('paymentType')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="经营范围"
            prop="businessScope"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.businessScope"
              clearable
              input-align="right"
              :border="false"
              placeholder="请选择"
              :max-length="20"
              readonly
              @click="selectClick('businessScope')"
            >
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="执药师数量"
            prop="docNum"
            :label-width="100"
            :rules="[{ validator: customValidator, message: '必须输入数字' }]"
          >
            <nut-input
              v-model="state.params.docNum"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="20"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item label="营业时间" prop="openingTime" :label-width="100">
            <nut-input
              v-model="state.params.openingTime"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="周六日(含节假日)营业时间"
            prop="weekOpeningTime"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.weekOpeningTime"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="100"
            >
            </nut-input>
          </nut-form-item>
          <nut-form-item
            label="药店电话"
            prop="dsContactTel"
            :label-width="100"
          >
            <nut-input
              v-model="state.params.dsContactTel"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="20"
            >
            </nut-input>
          </nut-form-item>

          <nut-form-item
            label="专用药师手机号"
            prop="docContactTel"
            :label-width="100"
            :rules="[{ validator: customValidator, message: '必须输入数字' }]"
          >
            <nut-input
              v-model="state.params.docContactTel"
              clearable
              input-align="right"
              :border="false"
              placeholder="请输入"
              :max-length="20"
            >
            </nut-input>
          </nut-form-item>
          <view class="photo">
            <nut-form-item
              label="照片(营业执照、经营许可证、门头照)"
              required
              prop="fileList"
              star-position="left"
              :rules="[{ validator: photoValidator, message: '请上传照片' }]"
            >
              <view class="upload">
                <view
                  class="item-img"
                  v-for="(item, index) in state.params.fileList"
                  :key="index"
                >
                  <img
                    :src="item.url || item.attUrl"
                    style="width: 80px; height: 80px"
                    @click="handlePreview(index)"
                  />
                  <img
                    :src="fileClose"
                    class="file-close"
                    @click="handleDelFile(item)"
                    v-show="!isCompleted"
                  />
                </view>
                <view @click="takePhoto()">
                  <img :src="CameraImg" class="camera-img" />
                </view>
              </view>
            </nut-form-item>
            <nut-image-preview
              :autoplay="0"
              :show="showPreview"
              :images="
                state.params.fileList.map((d) => ({ src: d.url || d.attUrl }))
              "
              @close="hideImgPreview"
              :init-no="imgPreviewIndex"
            />
          </view>
          <view style="font-size: 12px; color: #f32f29; margin: 4px"
            >注意：营业执照、经营许可证、门头照三个缺一不可，否则审批时会被拒绝</view
          >
        </view>
      </nut-form>
    </view>
    <FooterButton
      text="提交申请"
      @click-button="clickRight"
      :isLoading="state.hideLoading"
      style="z-index: 1000"
    />
    <PopupRadio ref="popupRadioRef" @radio-confirm="radioConfirm" />
    <DistrictPopup ref="districtPopupRef" @district-confirm="districtConfirm" />
    <DataPicker ref="dataPickerRef" @picker-confirm="pickerConfirm" />
    <SelectPharmacy ref="selectPharmacy" @confirm="phyConfirm" />
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
import CellClick from "../../../pages/components/cellClick/index.vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import PopupRadio from "../../../pages/components/popupRadio/popupRadio";
import { addDrugstore } from "../../../api/pharmacy.js";
import arrow from "../../../images/arrow.png";
import DistrictPopup from "../../../pages/components/districtPopup/index";
import DataPicker from "./dataPicker.vue";
import SelectPharmacy from "./selectPharmacy.vue";
import CameraImg from "../../../images/uploader.png";
import fileClose from "../../../images/file-close.png";
import location_ins from "../../../images/location_ins.png";
import { apiAuth } from "../../../utils/feishuAuth";

import { appCode, tenantId, h5Url, pcUrl } from "../../../utils/content";
import { baseApi } from "../../../utils/content";
import { getProcessIdApi } from "../../../utils/content";
const feishuNotice = ref({
  appLink: `${h5Url}`,
  pcLink: `${pcUrl}`,
});
const initHrInfo = Taro.getStorageSync("initHrInfo");
const orgList = initHrInfo.orgList.filter((item) => item.postType === "1")[0];
const router = useRouter();
const query = router.params;
const popupRadioRef = ref(null);
const ruleForm = ref(null);
const districtPopupRef = ref(null);
const dataPickerRef = ref(null);
const selectPharmacy = ref(null);
const imgPreviewIndex = ref(0);
const showPreview = ref(0);
const TokenKey = Taro.getStorageSync("access_token");

const customValidator = (val) => {
  if (/^\d+$/.test(val) || !val) {
    return Promise.resolve();
  } else {
    return Promise.reject("必须输入数字");
  }
};
const photoValidator = (val) => {
  if (val.length) {
    return Promise.resolve();
  } else {
    return Promise.reject("请上传照片");
  }
};

const state = reactive({
  rightText: "确定", //根据进来的情况判断
  info: {
    applicant: initHrInfo.empName,
    applicantCode: initHrInfo.empCode,
    postCode: orgList.code,
    postName: orgList.name,
    deptCode: orgList.deptCode,
    deptName: orgList.value,
    ancestors: orgList.ancestors,
    postIdList: initHrInfo.postIdList,
  },
  params: {
    dsName: "",
    dsType: "",
    dsAreaType: "",
    province: "",
    city: "",
    district: "",
    districtValue: "",
    address: "",
    longitude: "",
    latitude: "",
    superiorDsCode: "",
    superiorDsMdmCode: "",
    superiorDsName: "",
    dsNature: "",
    economicType: "",
    docNum: "",
    openingTime: "",
    weekOpeningTime: "",
    dsContactTel: "",
    docContactTel: "",
    businessScope: "",
    fileList: [],
    dsNameAlias: "",
  },
  masterValue: query.type === "add" ? "是" : "否",
  type: "",
  list: [
    { dictLabel: "是", dictValue: "是" },
    { dictLabel: "否", dictValue: "否" },
  ],
  sexValue: "",
  url: process.env.TARO_APP_API + `${baseApi}bpm/upload`,
  headers: {
    clientid: process.env.TARO_APP_CLIENT_ID,
    Authorization: `Bearer ${TokenKey}`,
  },
  hideLoading: false,
  processId: "",
});

const districtConfirm = (value, codeList) => {
  const list = value.split("/");
  state.params.districtValue = value;
  state.params.province = list[0] || "";
  state.params.city = list[1] || "";
  state.params.district = list[2] || "";
};
const clickRight = async () => {
  ruleForm.value.validate().then(async ({ valid, errors }) => {
    if (valid) {
      state.hideLoading = true;
      Taro.showLoading({
        mask: true,
        title: "提交中",
      });
      const params = {
        enableWorkflow: true,
        applyType: "4",
        insName: state.params.dsName,
        ...state.info,
        applyContent: {
          ...state.info,
          ...state.params,
        },
        appCode: appCode,
        tenantId: tenantId,
        channel: "2",
        processId: state.processId,
        ...feishuNotice.value,
      };

      const res = await addDrugstore(params);
      if (res.code === 200) {
        Taro.showToast({
          title: "成功",
          icon: "none",
          duration: 2000,
        });
        state.hideLoading = true;
        Taro.hideLoading();
        Taro.reLaunch({
          url: `/pages/pharmacy/index?index=1`,
        });
      } else {
        Taro.showToast({
          title: res.msg,
          icon: "none",
          duration: 2000,
        });
        Taro.hideLoading();
      }
    } else {
      console.log("error submit!!", errors);
    }
  });
};
const selectClick = (type) => {
  state.type = type;
  let options = [];
  if (type === "pharmacyType") {
    options = Taro.getStorageSync("pharmacyType");
    popupRadioRef.value.open(options, state.params.dsType);
  }
  if (type === "areaType") {
    options = Taro.getStorageSync("areaType");
    popupRadioRef.value.open(options, state.params.dsAreaType);
  }

  if (type === "fixedPoint") {
    options = state.list;
    popupRadioRef.value.open(options, state.params.fixedPoint);
  }
  if (type === "district") {
    options = Taro.getStorageSync("district");
    districtPopupRef.value.open(options, "pharmacy");
  }

  if (type === "businessTerm") {
    dataPickerRef.value.open(state.params.businessTerm);
  }

  if (type === "superiorDsName") {
    selectPharmacy.value.open();
  }

  if (type === "pharmacyNature") {
    options = Taro.getStorageSync("pharmacyNature");
    popupRadioRef.value.open(options, state.params.dsNature);
  }

  if (type === "paymentType") {
    options = Taro.getStorageSync("paymentType");
    popupRadioRef.value.open(options, state.params.economicType);
  }
  if (type === "businessScope") {
    options = Taro.getStorageSync("businessScope");
    popupRadioRef.value.open(options, state.params.businessScope);
  }
  if (type === "time") {
    popupRadioRef.value.open(options, state.params.paymentType);
  }
  if (type === "address") {
  }
};

const addressName = () => {
  Taro.navigateTo({
    url: `/pages/components/gdmap`,
  });
};
const pickerConfirm = (list) => {
  state.params.businessTerm = `${list[0]}-${list[1]}-${list[2]}`;
};
const phyConfirm = (ins) => {
  state.params.superiorDsName = ins.name;
  state.params.superiorDsCode = ins.code;
  state.params.superiorDsMdmCode = ins.value;
};

const radioConfirm = (val) => {
  if (state.type === "pharmacyType") {
    state.params.dsType = val;
  }
  if (state.type === "areaType") {
    state.params.dsAreaType = val;
  }
  if (state.type === "fixedPoint") {
    state.params.fixedPoint = val;
  }

  if (state.type === "pharmacyNature") {
    state.params.dsNature = val;
  }
  if (state.type === "paymentType") {
    state.params.economicType = val;
  }

  if (state.type === "businessScope") {
    state.params.businessScope = val;
  }
};

// const chooseImage = async () => {
//   Taro.chooseMedia({
//     count: 1, // 默认9
//     mediaType: ["image"],
//     sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有，在H5浏览器端支持使用 `user` 和 `environment`分别指定为前后摄像头
//     success: async function (res) {
//       console.log("res", res);
//       Taro.showLoading({
//         title: "上传中",
//       });
//       const tempFiles = res.tempFiles;
//       if (tempFiles[0].height > 700) {
//         const size = tempFiles[0].size / 1024 / 1024;
//         console.log(size, "size");
//         if (size > 20) {
//           Taro.hideLoading();
//           Taro.showToast({
//             icon: "error",
//             title: "请上传<20MB的图片",
//           });

//           return false;
//         } else {
//           Taro.uploadFile({
//             url: state.url,
//             filePath: tempFiles[0].tempFilePath,
//             name: "file",
//             header: state.headers,
//             success(res) {
//               Taro.hideLoading();
//               const data = JSON.parse(res.data);

//               state.params.fileList.push(Object.assign(data.data));
//             },
//             fail() {
//               Taro.hideLoading();
//               Taro.showToast({
//                 icon: "error",
//                 title: "上传失败",
//               });
//             },
//           });
//         }
//       } else {
//         Taro.uploadFile({
//           url: state.url,
//           filePath: tempFiles[0].tempFilePath,
//           name: "file",
//           header: state.headers,
//           success(res) {
//             Taro.hideLoading();
//             const data = JSON.parse(res.data);
//             console.log("data222", data);
//             state.params.fileList.push(Object.assign(data.data));
//           },
//           fail() {
//             Taro.hideLoading();
//             Taro.showToast({
//               icon: "error",
//               title: "上传失败",
//             });
//           },
//         });
//       }
//     },
//   });
// };
const chooseImage = () =>
  new Promise((res, rej) => {
    try {
      // tt.chooseImage({
      //   count: 1, // 默认为9，设置为1表示只能选择一张图片
      //   sizeType: ["compressed"],
      //   sourceType: ["album", "camera"],
      //   success: (data) => {
      //     console.log(data, "--data");
      //     const fileSystemManager = tt.getFileSystemManager();
      //     fileSystemManager.readFile({
      //       filePath: data.tempFilePaths?.[0],
      //       encoding: "base64",
      //       success(res_v1) {
      //         const imageType = "image/png";
      //         const base64ImageURL = `data:${imageType};base64,${res_v1?.data}`;
      //         res(base64ImageURL);
      //       },
      //       fail(res) {
      //         console.log(`readFile fail: ${JSON.stringify(res)}`);
      //       },
      //     });
      //   },
      //   fail: (err) => rej(err),
      // });
      Taro.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (data) => {
          console.log(data, "---dddd");
          res(data?.tempFilePaths?.[0]);
        },
        fail: (err) => rej(err),
      });
    } catch (err) {
      console.log("!ttt", err);
      Taro.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (data) => {
          console.log(data, "---dddd");
          res(data?.tempFilePaths?.[0]);
        },
        fail: (err) => rej(err),
      });
    }
  });
const takePhoto = async () => {
  try {
    const fileUrl = await chooseImage();
    Taro.showLoading({
      mask: true,
      title: "上传中",
    });

    Taro.uploadFile({
      url: state.url,
      header: {
        clientid: process.env.TARO_APP_CLIENT_ID,
        Authorization: `Bearer ${TokenKey}`,
      },
      filePath: fileUrl,
      name: "file",
      fail: () => {
        Taro.hideLoading();
        console.log("上传失败");
      },
      success: (result) => {
        Taro.hideLoading();
        try {
          const data = JSON.parse(result.data);
          state.params.fileList.push(Object.assign(data.data));
        } catch (e) {
          console.log(e);
        }
      },
    });
  } catch (err) {}
};
const handlePreview = (index) => {
  imgPreviewIndex.value = index;
  showImgPreview();
};
const showImgPreview = () => {
  showPreview.value = true;
};
const handleDelFile = (e) => {
  const index = state.params.fileList.findIndex((d) => d.ossId === e.ossId);
  if (index > -1) {
    state.params.fileList.splice(index, 1);
  }
};

const hideImgPreview = () => {
  showPreview.value = false;
};
const getKey = async () => {
  state.processId = await getProcessIdApi("drugstore_apply");
};
onMounted(() => {
  Taro.setNavigationBarTitle({ title: "新增药店申请" });
  state.params.dsName = decodeURIComponent(query.dsName);
  apiAuth();
  getKey();
}),
  useDidShow(() => {
    const location = Taro.getStorageSync("location");
    if (location) {
      state.params.latitude = location.location.lat;
      state.params.longitude = location.location.lng;
      state.params.address = location.name + location.address;
      Taro.removeStorageSync("location");
    }
  });
</script>
<style lang="scss">
.addPharmacy {
  padding-bottom: 90px;
  .title {
    height: 48px;
    line-height: 48px;
    font-size: 14px;
    padding: 0 16px;
    color: #869199;
  }
  .section {
    font-size: 14px;
    padding: 0 16px;
    background: #fff;
    padding-bottom: 4px;

    .label {
      color: #1d212b;
      &::before {
        content: "*";
        color: red;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }
  .location_ins {
    width: 24px;
    height: 24px;
  }
  .nut-cell__title {
    color: #1d212b;
  }
  .nut-input-left-box {
    margin-right: 10px;
  }
  .arrowIcon {
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }
  .nut-cell-group__wrap {
    margin-top: 0;
    background: none;
  }
  .nut-cell__title {
    font-size: 16px;
  }
  .photo {
    .nut-cell {
      display: block;
    }
    .nut-form-item__label.nut-cell__title {
      width: 100%;
    }
    .upload {
      margin-top: 8px;
      display: flex;

      .item-img {
        position: relative;
        margin-right: 8px;

        .file-close {
          width: 20px;
          height: 20px;
          position: absolute;
          top: 0;
          right: 0;
        }
      }
      .camera-img {
        width: 80px;
        height: 80px;
      }
    }
  }
  .input-text::placeholder {
    color: #c9cdd0;
  }
}
</style>
