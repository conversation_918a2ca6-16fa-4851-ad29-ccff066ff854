import httpRequest from "@/servers/http";

export const speakerListApi = (pageNum, pageSize, data) => {
  return httpRequest.post(
    `/msr/jur-customer/speaker?pageNum=${pageNum}&pageSize=${pageSize}`,
    data
  );
};

export const addSpeakerApi = (data) => {
  return httpRequest.post(`/mdm/speaker/add`, data);
};
export const speakerDetailApi = (params) => {
  return httpRequest.get(`/mdm/speaker/detail`, params);
};

export const speakeAddSearchApi = (params) => {
  return httpRequest.get(`/mdm/speaker/add-search`, params);
};
export const ocrCardApi = (data) => {
  return httpRequest.post(`/itg/v1/ocr/id-card`, data);
};

export const ocrCardV2Api = (data) => {
  return httpRequest.post(`/itg/v2/ocr`, data);
};

export const ocrBankApi = (data) => {
  return httpRequest.post(`/itg/v1/ocr/bank-card`, data);
};

export const academyLevelApi = () => {
  return httpRequest.get("/plt/dict/data/list?dictType=academy_level");
};

export const accessLevelApi = () => {
  return httpRequest.get("/plt/dict/data/list?dictType=access_level");
};

export const administrationLvlApi = () => {
  return httpRequest.get("/plt/dict/data/list?dictType=administration_lvl");
};

export const brandLevelApi = () => {
  return httpRequest.get("/plt/dict/data/list?dictType=brand_level");
};
export const retireStatusApi = () => {
  return httpRequest.get("/plt/dict/data/list?dictType=retire_status");
};

export const speakerTypeApi = () => {
  return httpRequest.get("/plt/dict/data/list?dictType=speaker_type");
};
export const antiAgingRatingApi = () => {
  return httpRequest.get("/plt/dict/data/list?dictType=anti_aging_rating");
};

export const therapyAreaApi = () => {
  return httpRequest.get("/plt/dict/data/list?dictType=therapy_area");
};

export const idCardTypeApi = () => {
  return httpRequest.get("/plt/dict/data/list?dictType=id_card_type");
};

export const sysUserSpeakerApi = () => {
  return httpRequest.get(`/plt/config/configKey/sys.user.speaker`);
};

export const sysUserInsApi = () => {
  return httpRequest.get(`/plt/config/configKey/sys.user.ins`);
};
