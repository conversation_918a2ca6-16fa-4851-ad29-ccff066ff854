import { createApp } from "vue";
import { Dialog } from "@nutui/nutui-taro";
import Vant from "vant";
import "vant/lib/index.css";
import "./app.scss";
import VConsole from "vconsole";
if (process.env.NODE_ENV === "development") {
  const vConsole = new VConsole({
    defaultPanel: "log", // 指定默认打开的面板（'network', 'element', 'log', 'system', 'console'）
    maxLogNumber: 1000, // 控制台日志的最大显示数量
    // ...其他配置项
  });
}
const App = createApp({
  // 可以使用所有的 Vue 生命周期方法
  mounted() {},

  // 对应 onShow
  onShow(options) {},
  // 对应 onHide
  onHide() {},

  // 入口组件不需要实现 render 方法，即使实现了也会被 taro 所覆盖
});
App.use(Dialog);
App.use(Vant);

export default App;
