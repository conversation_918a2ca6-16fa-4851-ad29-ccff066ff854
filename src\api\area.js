import httpRequest from "@/servers/http";

// 辖区下机构列表
export const institutionList = (pageNum, pageSize, params) => {
  return httpRequest.post(
    `/msr/jur/institution/app/list?pageNum=${pageNum}&pageSize=${pageSize}`,
    params
  );
};

//机构等级字典
export const institutionLevel = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=institution_level`);
};

// 辖区下机构详情
export const institutionDetail = (params) => {
  return httpRequest.get("/msr/jur/institution/app/detail", params);
};
// 辖区下客户列表

export const customerList = (pageNum, pageSize, params) => {
  return httpRequest.post(
    `/msr/jur-customer/app/list?pageNum=${pageNum}&pageSize=${pageSize}`,
    params
  );
};
// 辖区下客户详情
export const customerDetail = (params) => {
  return httpRequest.get("/msr/jur-customer/app/detail", params);
};
//省市区
export const district = (params) => {
  return httpRequest.get(`/mdm/hcd/district`, params);
};
export const institution = (params) => {
  return httpRequest.get(`/msr/institution/lov`, params);
};
//获取产品
export const products = (params) => {
  return httpRequest.get(`/msr/jur/products`, params);
};
// 行政级别字典
// export const adminisLevel = () => {
//   return httpRequest.get(`/plt/dict/data/list?dictType=adminis_level`);
// };
//职称
// export const professionTechTitle = () => {
//   return httpRequest.get(`/plt/dict/data/list?dictType=profession_tech_title`);
// };
// 科室
export const deptNameList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=department_label`);
};
// 查询产品

export const productQuery = (params) => {
  return httpRequest.get(`/msr/product/query`, params);
};
export const getApproval = (params) => {
  return httpRequest.get(`/msr/product/get-approval`, params);
};

//查询客户
export const custByins = (params) => {
  return httpRequest.get(`/msr/customer/by-institution`, params);
};
// 查询下属
export const subDetail = (params) => {
  return httpRequest.post(`/msr/sys/sub-detail`, params);
};
export const productDelete = (params) => {
  return httpRequest.post(`/msr/product/delete`, params);
};
export const claimInitiate = (params) => {
  return httpRequest.post(`/msr/jur/claim-initiate`, params);
};

export const approveInitiate = (params) => {
  return httpRequest.post(`/msr/jur/add`, params);
};

export const applyFormType = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=apply_form_type`);
};
export const approveList = (params) => {
  return httpRequest.get(`/msr/bpm/page`, params);
};

export const custSearch = (params) => {
  return httpRequest.post("/msr/customer/add-search", params);
};

export const initiateIns = (params) => {
  return httpRequest.post("/msr/institution/add", params);
};

export const initiateCust = (params) => {
  return httpRequest.post("/msr/customer/add", params);
};

export const isApplyList = (pageNum, pageSize, params) => {
  return httpRequest.post(
    `/msr/institution/is-apply-list?params=${pageNum}&pageSize=${pageSize}`,
    params
  );
};

export const institutionAllList = (params) => {
  return httpRequest.get(`/msr/institution/list`, params);
};
