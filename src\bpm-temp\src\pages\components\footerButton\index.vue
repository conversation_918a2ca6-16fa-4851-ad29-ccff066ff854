<template>
  <div class="footerButton">
    <nut-row type="flex" justify="center">
      <nut-col :span="23">
        <nut-button
          v-if="!props.isTextType"
          :class="{
            'border-none': !props.plain,
          }"
          :color="props.color"
          @click="buttonClcik"
          shape="square"
          :plain="props.plain"
          block
          :disabled="props.disabled"
          :style="{ color: props.textColor }"
          :loading="props.isLoading"
        >
          <template #icon>
            <slot name="icon"></slot>
          </template>
          {{ props.text }}
        </nut-button>
        <view v-else class="text-but" @click="buttonClcik">
          {{ props.text }}</view
        >
      </nut-col>
    </nut-row>
  </div>
</template>

<script setup>
import { defineProps, reactive } from "vue";
const props = defineProps({
  isTextType: {
    type: Object,
    default: false,
  },
  text: {
    type: String,
    default: "",
  },
  icon: {
    type: String,
    default: "",
  },
  plain: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  color: {
    type: String,
    default: "#2551F2",
  },
  textColor: {
    type: String,
    default: "#fff",
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["click-button"]);
const state = reactive({});
let timer = null;
const buttonClcik = () => {
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
  timer = setTimeout(() => {
    emit("click-button");
  }, 300);
};
</script>

<style lang="scss">
.text-but {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 400;
  text-align: center;
  color: #2551f2;
}
.footerButton {
  position: fixed;
  bottom: 0;
  background-color: #ffffff;
  width: 100%;
  padding-top: 8px;
  padding-bottom: 34px;
  .nut-button {
    border-radius: 4px;
  }
  .border-none {
    border: none;
  }
  .nut-button__wrap {
    img {
      width: 10px;
      height: 10px;
    }
  }
}
</style>
