<template>
  <view class="ins-sign-detail">
    <DetailTop
      :status="state.info.status"
      :visitTime="state.info.visitTime"
      :completeTime="state.info.completeTime"
      :planFlagTime="state.info.planFlag"
    />
    <view style="padding: 0px 8px">
      <SignTime
        :signInTime="state.info.signInTime"
        :signOutTime="state.info.signOutTime"
        :duration="state.info.duration"
        :visitUserName="state.info.visitUserName"
        :sysSignOutFlag="state.info.sysSignOutFlag"
        :status="state.info.status"
      />

      <!-- 拜访信息 -->
      <view style="margin-top: 8px" class="visit-info">
        <view class="info-title" @click="clickVisit">
          <view style="display: flex; align-items: center">
            <view> {{ state.info.institutionName }}</view>
            <view class="tag-s" v-if="state.info.visitTypeCode === '0'"
              >院内拜访
            </view>
            <view class="tag-s" v-else>院外拜访 </view>
          </view>
          <view>
            <img
              :src="!state.infoIconFlag ? arrowDown : arrowUp"
              alt=""
              style="width: 13px; height: 13px; padding-left: 10px"
            />
          </view>
        </view>

        <view v-show="state.infoIconFlag">
          <nut-divider dashed :style="{ color: '#E5E6EB', marginBottom: 0 }" />
          <view style="padding: 0 16px">
            <nut-cell title="机构地址">
              <template #desc>
                <span style="word-break: break-all">
                  {{ state.info.insAddress }}</span
                >
              </template>
            </nut-cell>
            <nut-cell title="签到地址">
              <template #desc>
                <span style="word-break: break-all">
                  {{ state.info.signInAddress }}</span
                >
              </template>
            </nut-cell>
            <nut-cell title="签退地址" v-if="state.info.status === '1'">
              <template #desc>
                <span style="word-break: break-all">
                  {{ state.info.signOutAddress }}</span
                >
              </template>
            </nut-cell>
            <UploadImage
              ref="uploadImageRef"
              :iconFlag="state.info.status === '1' ? false : true"
              :signInList="state.info.signInPhotoList || []"
              :status="state.info.status"
              :params="state.params"
              type="signOut"
            />
            <!-- 使用 form-create-mobile 组件 -->
            <form-create-mobile :rule="ruleSignInfo" v-model="formData" :option="options1" :disabled="true"/>
          </view>
        </view>
      </view>
      <!-- 使用 form-create-mobile 组件 -->
      <view style="margin-top: 8px; padding: 0 0.8rem;" class="visit-info">
        <form-create-mobile :rule="rule" v-model:api="fApi" v-model="formData" :option="options" :disabled="state.readOnly"/>
      </view>
    </view>
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import {reactive, onMounted, ref, computed} from "vue";
import SignTime from "./sign/components/signTime";
import arrowDown from "../../images/arrow-down.png";
import arrowUp from "../../images/up.png";
import SignInOut from "./sign/components/signInOut";
import {
  visitDistance,
  crmSignOutTime,
  getCurrentTime,
  filtering,
  visitInfoApi,
  lastCommunicationApi
} from "../../api/institutionalVisitsApi.js";
import { insSign, insSignDetail } from "../../api/insSign";
import { imageUrl } from "../../api/institutionalVisitsApi";

import DetailTop from "./components/detailTop";
import UploadImage from "../components/uploadImage/index.vue";
import { apiAuth } from "../../utils/feishuAuth";
import { currentTime } from "../../utils/content";
import {formListApi} from "../../api/bpm";
import formCreate from "@form-create/element-ui";

const router = useRouter();
const query = router.params;
const uploadImageRef = ref(null);


const fApi = ref({});
const formData = ref({
  visitPurpose: '润肤针入院'
});
const loading = ref(false);
const options1 = {
  onSubmit: async (formData) => {
  },
  labelWidth: '9rem',
  resetBtn: false, // 显示重置按钮
  submitBtn: false,
  labelPlacement: 'top'
};
const options = {
  onSubmit: async (formData) => {
    try {
      loading.value = true;
      Taro.showLoading({
        mask: true
      })
      // alert(JSON.stringify(formData)); // 提交表单时弹出表单数据
      const res = await visitInfoApi({
        visitInstitutionId: state.visitInstitutionId,
        extendInfo: JSON.stringify(formData)
      });
      if (res.code === 200) {
        Taro.showToast({
          title: "操作成功",
          icon: "none",
          duration: 2000,
        });
        loading.value = false;
        Taro.hideLoading()
        await Taro.navigateBack({
          delta: 1,
        });
      } else {
        if (res?.msg) {
          loading.value = false;
          Taro.hideLoading()
          Taro.showToast({
            title: res?.msg,
            icon: "none",
          });
        }
      }
    } catch (e) {
      /**/
      loading.value = false;
    }
  },
  labelWidth: '9rem',
  resetBtn: false, // 显示重置按钮
  // submitBtn: query.readOnly !== '1',
  submitBtn: {
    innerText: '保存',
    show: query.readOnly !== '1'
  },
  form: {
    labelPlacement: 'top'
  }
};
const option = ref({});
const rule = ref([]);
const ruleSignInfo = ref([]);

const state = reactive({
  readOnly: false,
  visitInstitutionId: "",
  info: {},
  signOutStatus: "",
  infoIconFlag: false,
  proFlag: false,
  leftTetx: "",
  scoresList: [], //评分的数组
  //评分
  //签退参数
  params: {
    signType: "1",
    signOutLat: "",
    signOutLon: "",
    signOutDistance: 0,
    signOutAddress: "",
    communicateContent: "",
  },
  insLonNoFlag: false,
  crmSignOutTimeInfo: {},
});

const getDetail = async () => {
  const params = {
    visitInstitutionId: state.visitInstitutionId,
  };
  const res = await insSignDetail(params);
  state.info = res?.data;

  formData.value = state.info.extendInfo && JSON.parse(state.info.extendInfo) || {}

  if (state.info.status === "2") {
    state.leftTetx = "拜访签退";
  }

  if (state.info.status === "1") {
    state.leftTetx = "拜访详情";
  }
  if (state.info.status === "2" && query.activeIndex != 1) {
    getLocation();
  }

  uploadImageRef.value.viewList(
    state.info?.signInPhotoList || [],
    state.info.signOutPhotoList || []
  );
};

const clickVisit = () => {
  state.infoIconFlag = !state.infoIconFlag;
};

const refreshLocation = () => {
  getLocation("refre");
};

const getDistance = async (type) => {
  if (state.info.insLon && state.info.insLon != "0.0") {
    let ins = "";
    if (state.info.insLon) {
      ins = `${state.info.insLon},${state.info.insLat}`;
    } else {
      ins = "";
    }
    const params = {
      origins: `${state.params.signOutLon},${state.params.signOutLat}`,
      destination: ins,
    };
    const res = await visitDistance(params);

    if (res.code === 200) {
      if (res.data[0]) {
        state.params.signOutDistance = Number(res.data[0]);
      } else {
        state.params.signOutDistance = 0;
      }
      state.signOutStatus = "1";
      if (type) {
        Taro.showToast({
          title: "刷新成功",
          icon: "none",
          duration: 2000,
        });
      }
    } else {
      state.signOutStatus = "1";
      Taro.showToast({
        title: "获取机构位置与当前位置距离失败",
        icon: "none",
        duration: 2000,
      });
    }
  } else {
    state.signOutStatus = "1";
    state.params.signOutDistance = 0;
    state.insLonNoFlag = true;
  }
};
const getConvert = (longitude, latitude) =>
  new Promise((res) => {
    Taro.request({
      url: `https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${longitude},${latitude}&key=0446b950e065d1e0b98a9e97f08dfbff&coordsys=gps`,
      success: (result_before) => {
        const [lon, lat] = result_before.data.locations.split(",");
        res({ lon, lat });
      },
    });
  });
const getLocation = (type) => {
  if ("geolocation" in navigator) {
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const latitude = position.coords.latitude; // 纬度
        const longitude = position.coords.longitude; // 经度
        const { lon, lat } = await getConvert(longitude, latitude);

        Taro.request({
          url: `https://restapi.amap.com/v3/geocode/regeo?location=${lon},${lat}&key=0446b950e065d1e0b98a9e97f08dfbff`,
          success: (result) => {
            state.params.signOutAddress =
              result.data.regeocode.formatted_address;
            state.params.signOutLat = latitude;
            state.params.signOutLon = longitude;
            getDistance(type);
            // const mapCtx = Taro.createMapContext("map-container");
            // mapCtx.moveToLocation();
          },
          fail: function (res) {
            Taro.showToast({
              title: "定位失败",
              icon: "none",
              duration: 2000,
            });
            state.signOutStatus = "0";
          },
        });
      },
      function (error) {
        Taro.showToast({
          title: "定位失败",
          icon: "none",
          duration: 2000,
        });
        state.signOutStatus = "0";
      },
      {
        // enableHighAccuracy: true,
        // timeout: 1000,
        // maximumAge: 0,
      }
    );
  } else {
    Taro.showToast({
      title: "请开启定位权限",
      icon: "none",
      duration: 2000,
    });
  }
};
const blurTextarea = async  () => {
const res = await filtering({txt: state.params.communicateContent})
if(res.data.length) {
  const content = `本次沟通内容包含如下敏感词：${res.data.join('、')}，请重新输入`
 Taro.showModal({
      content: content,
      confirmText: "确定",
      duration: 2000,
      showCancel: false,
    });
    return false
} else {
  return true
}
}
const signOut = async () => {
  const resFilter = await blurTextarea()
  if(!resFilter) return
  const { data } = await getCurrentTime();
  const currentTime_2 = new Date(data);
  const signInTime = new Date(state.info.signInTime);
  const timeDifference = currentTime_2.getTime() - signInTime.getTime();
  const differenceInMinutes = Math.floor(timeDifference / (1000 * 60));
  if (
    state.crmSignOutTimeInfo.configValue === "1" &&
    differenceInMinutes < 15
  ) {
    return Taro.showModal({
      content: state.crmSignOutTimeInfo.configName,
      confirmText: "确定",
      duration: 2000,
      showCancel: false,
    });
  }
  const infoParams = {
    visitInstitutionId: state.visitInstitutionId,
    institutionCode: state.info.institutionCode,
    institutionName: state.info.institutionName,
    insLat: state.info.insLat,
    insLon: state.info.insLon,
    isPlan: state.info.planFlag,
    id: state.info.visitSignId,
  };
  let signOutPhotoList = [];
  if (uploadImageRef.value) {
    signOutPhotoList = uploadImageRef.value.photoList.map((item) => {
      return {
        attUrl: item.originalUrl,
        ossId: item.ossId,
      };
    });
  }

  const params = {
    ...state.params,
    ...infoParams,
    signOutPhotoList,
  };
  const res = await insSign(params);
  if (res.code === 200) {
    Taro.showToast({
      title: "签退成功",
      icon: "none",
      duration: 2000,
    });
    uploadImageRef.value.setPhotoList();
    getDetail();
  } else {
    // Taro.showToast({
    //   title: res.msg,
    //   icon: "none",
    // });
  }
};
let timer = null;

const getCrmSignOutTime = async () => {
  const res = await crmSignOutTime();
  state.crmSignOutTimeInfo = res.rows[0] || {};
};

const getFormRule = async () => {
  try {
    const res = await formListApi({
      code: 'pk2'
    })
    const res1 = await formListApi({
      code: 'pk1'
    })
    const _ = res.rows && res.rows[0] || {};
    const _1 = res1.rows && res1.rows[0] || {};
    ruleSignInfo.value = _1.formContentH5 && formCreate.parseJson(_1.formContentH5).rule;
    rule.value = _.formContentH5 && formCreate.parseJson(_.formContentH5).rule;
  } catch (e) { /* empty */ }
}

const getLastCommunication = async () => {
  const res = await lastCommunicationApi({
    visitInstitutionId: state.visitInstitutionId,// 当前拜访ID
    institutionCode: state.info.institutionCode, //机构编码
    visitUserHrCode: state.info.visitUserHrCode,// 拜访人工号
  });
  formData.value.lastCommunicateContent = res.data || ''
}

onMounted(() => {
  Taro.setNavigationBarTitle({ title: "机构签到" });
  getFormRule();
  apiAuth();
});
useDidShow(() => {
  state.visitInstitutionId = query.id;
  state.readOnly = query.readOnly === '1';
  const init = async () => {
    await getDetail();
    await getCrmSignOutTime();
    await getLastCommunication()
  };
  init();
});
</script>
<style lang="scss">
.ins-sign-detail {
  font-size: 16px;
  // padding-top: 94px;
  padding-bottom: 50px;

  .van-field__label {
    width: 6.5rem;
    margin: 0;
    font-size: 0.8rem;
    padding: 0.85rem 0;
    border-bottom: 0.05rem solid #f4f5f7;
    text-align: left;
  }
  .van-cell__value .van-field__value {
    padding: 0.85rem 0;
    font-size: 0.8rem;
  }

  .van-cell__right-icon {
    margin-top: 18px;
  }
  .van-cell {
    padding-left: 0;
    padding-right: 0;
  }

  .sign-time {
    margin: 0;
  }

  .visit-address {
    margin-top: 8px;
    padding: 12px 16px 12px 16px;
    background: #fff;
    border-radius: 8px;
    .sexIcon {
      display: inline-block;
      margin-right: 5px;
      width: 18px;
      height: 18px;
      vertical-align: text-top;
    }
    .address {
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: text-bottom;
      margin-right: 4px;
    }
    .m_b {
      // margin-bottom: 8px;
    }
  }
  .visit-info {
    padding: 16px 0;
    border-radius: 8px;
    background: #fff;
    .info-title {
      position: relative;
      padding: 0 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .qutianxie {
        font-size: 14px;
        color: #2551f2;
        background: #e8f0ff;
        padding: 3px 10px;
        border-radius: 20px;
      }
    }
    .buttonIcons {
      position: absolute;
      top: -6px;
      left: 75%;
    }
    .nut-button__wrap {
      color: #2551f2;
    }
    .assistUserVOs {
      .nut-cell__title {
      }
    }
    .nut-button {
      height: 32px;
      line-height: 32px;
      font-size: 16px;
    }
  }

  // .tag-f {
  //   margin-left: 4px;
  //   color: #f77234;
  //   background: #fff3e8;
  //   max-width: fit-content;
  //   font-size: 12px;
  //   font-family: PingFang SC;
  //   font-weight: 600;
  //   border-radius: 4px;
  //   padding: 2px 8px;
  //   height: 20px;
  //   line-height: 20px;
  // }
  .tag-s {
    margin-left: 4px;
    color: #2551f2;
    background: #e8f0ff;
    min-width: 51px;
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: 600;
    border-radius: 4px;
    padding: 2px 8px;
    height: 20px;
    line-height: 20px;
  }
  .nut-cell__title {
    max-width: 100px;
    color: #869199 !important;
  }
  .nut-cell__value {
    font-size: 16px;
  }

  .nut-textarea__textarea::placeholder {
    color: #869199;
  }
}
</style>
