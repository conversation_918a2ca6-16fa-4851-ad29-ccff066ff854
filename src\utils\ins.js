import {
  insLevel,
  insProfessionType,
  economicType,
  insGrade,
} from "@/api/ins.js";
import Taro from "@tarojs/taro";

export async function getInsLevel() {
  const { rows } = await insLevel();
  Taro.setStorage({
    key: "insLevel",
    data: rows,
  });
}
export async function getInsGrade() {
  const { rows } = await insGrade();
  Taro.setStorage({
    key: "insGrade",
    data: rows,
  });
}

export async function getInsprofessionType() {
  const { rows } = await insProfessionType();
  Taro.setStorage({
    key: "insPprofessionType",
    data: rows,
  });
}
export async function getEconomicType() {
  const { rows } = await economicType();
  Taro.setStorage({
    key: "economicType",
    data: rows,
  });
}

export function getInsLevelColor(code) {
  if (code.includes("一级")) {
    return "#F77234";
  } else if (code.includes("二级")) {
    return "#00B578";
  } else if (code.includes("三级")) {
    return "#2551F2";
  } else {
    return "#C6CAD1";
  }
}

export function getInsLevelTextColor(code) {
  if (code.includes("一级")) {
    return "#F77234";
  } else if (code.includes("二级")) {
    return "#00B578";
  } else if (code.includes("三级")) {
    return "#2551F2";
  } else {
    return "#4E595E";
  }
}

export function getPostCode() {
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  if (initHrInfo.jurCodeList) {
    return initHrInfo.jurCodeList.map((item) => item).join(",");
  } else {
    return "";
  }
}
