<template>
  <view class="gdmap-position">
    <view id="container-position" />
    <view class="address">
      <nut-radio-group v-model="state.radioChecked" text-position="left">
        <GlobalNutRadio
          v-for="item in state.pois"
          :value="item.id"
          :key="item.id"
        >
          <template #name>
            <view @click="selectAdress(item)">
              <text>{{ item.name }}</text>
            </view>
          </template>
        </GlobalNutRadio>
      </nut-radio-group>
    </view>
  </view>
  <view>
    <view>
      <FooterButton text="确定" @click-button="clickAddButton">
        <template #icon>
          <Uploader color="#fff" />
        </template>
      </FooterButton>
    </view>
  </view>
</template>

<script setup>
import Taro, {useDidHide, useDidShow, useRouter} from "@tarojs/taro";
import { onMounted, reactive } from "vue";
import TopSearch from "../../components/topSearch/topSearch.vue";
import FooterButton from "../../components/footerButton";
import GlobalNutRadio from "../../components/globalNutRadio/globalNutRadio.vue";

const state = reactive({
  leftTetx: "地图",
  text: "确认",
  pois: [],
  value: "",
  tuningMap: null,
  currentMarker: null,
  position: null,
  radioChecked: "",
});

const clickAddButton = async () => {
  const location = state.pois.filter(
    (item) => item.id === state.radioChecked
  )[0];

  window.collectEvent('position_event', {
    GS_latitude: location.location.lat,
    GS_longitude: location.location.lng,
    GS_timer: (new Date()).getTime(),
    GS_empNo: localStorage.getItem('initHrInfo') && JSON.parse(localStorage.getItem('initHrInfo')) && JSON.parse(localStorage.getItem('initHrInfo')).data && JSON.parse(localStorage.getItem('initHrInfo')).data.empCode,
    GS_empName: localStorage.getItem('initHrInfo') && JSON.parse(localStorage.getItem('initHrInfo')) && JSON.parse(localStorage.getItem('initHrInfo')).data && JSON.parse(localStorage.getItem('initHrInfo')).data.empName,
    GS_customGap: 1,
    GS_customPosition: 1,
  })
  window.collectEvent('send');
  Taro.showToast({
    icon: 'none',
    title: '打卡成功',
    duration: 3000
  })
  setTimeout(() => {
    Taro.reLaunch({
      url: '/pages/trajectory/index',
    });
  }, 3000)
};

const selectAdress = (item) => {
  state.currentMarker.setPosition([item.location.lng, item.location.lat]);
  state.tuningMap.setCenter([item.location.lng, item.location.lat]);
};

useDidShow(() => {
  setTimeout(() => {
    const map = new AMap.Map("container-position", {
      zoom: 15,
      resizeEnable: true,
    });
    state.tuningMap = map;
    AMap.plugin(["AMap.Geolocation", "AMap.PlaceSearch"], () => {
      const geolocation = new AMap.Geolocation({
        enableHighAccuracy: true, // 是否使用高精度定位，默认:true
        timeout: 10000, // 超过10秒后停止定位，默认：5s
        position: "RB", // 定位按钮的停靠位置
        offset: [10, 20], // 定位按钮与设置的停靠位置的偏移量，默认：[10, 20]
        zoomToAccuracy: true, // 定位成功后是否自动调整地图视野到定位点
      });

      geolocation.getCurrentPosition((status, result) => {
        if (status === "complete") {
          const { position } = result;
          const { lng, lat } = position;
          map.setCenter([lng, lat]);

          const marker = new AMap.Marker({
            icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
            position: [lng, lat],
            anchor: "bottom-center",
          });
          state.currentMarker = marker;
          map.add(marker);
          map.setFitView();
          const placesearch = new AMap.PlaceSearch({
            pageSize: 10,
            pageIndex: 1,
            types: 120000,
          });
          placesearch.searchNearBy("", [lng, lat], 1000, (status, result) => {
            // console.log(status, result)
            if (status === "complete") {
              state.pois = result?.poiList.pois;
              // console.log('result.poiList.pois', result.poiList.pois)
            }
          });
        } else {
          console.error("定位失败");
        }
      });
    });
  }, 50)
});

useDidHide(() => {
  state.tuningMap?.destroy();
  state.tuningMap = null;
})
</script>

<style lang="scss">
.gdmap-position {
  padding-bottom: 65px;
  .address {
    padding: 0 12px;
    background: #fff;
    height: 230px;
    overflow: hidden;
    overflow-y: scroll;
  }
  .searchBar .nut-searchbar {
    width: 100%;
  }
  .searchBar .nut-searchbar__search-input {
    background: #fff;
  }
}

#container-position {
  width: 100%;
  height: 300px;
  margin-bottom: 8px;
}

#panel {
  background-color: white;
}
</style>
