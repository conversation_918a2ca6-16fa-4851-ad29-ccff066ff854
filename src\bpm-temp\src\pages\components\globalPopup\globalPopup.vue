<template>
  <view>
    <nut-popup
      v-model:visible="show"
      :style="props.style"
      :position="props.position"
      lock-scroll
      round
      safe-area-inset-bottom
      @close="close"
      @closed="close"
    >
      <slot></slot>
    </nut-popup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  style: {
    type: Object,
    default: {
      zIndex: 99999,
    },
  },
  position: {
    type: String,
    default: "bottom",
  },
});
const show = ref(false);
const open = () => {
  document.body.style.overflow = "hidden";
  const elements = document.getElementsByClassName("taro_page");
  for (let i = 0; i < elements.length; i++) {
    elements[i].style.overflowY = "hidden";
  }

  show.value = true;
};
const close = () => {
  console.log("close");
  document.body.style.overflow = "";
  const elements = document.getElementsByClassName("taro_page");
  for (let i = 0; i < elements.length; i++) {
    elements[i].style.overflowY = "";
  }
  document.body.style.overflow = "";

  show.value = false;
};

defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped></style>
