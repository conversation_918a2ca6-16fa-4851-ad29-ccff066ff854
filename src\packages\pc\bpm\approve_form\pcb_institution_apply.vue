<template>
  <div>
    <div class="text-[#1D212B] text-[14px] py-[10px]">基本信息</div>
    <div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">部门</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.ancestors
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">岗位</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.postName
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">机构名称</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.insName
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">机构别名</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.insNameAlias
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">上级机构</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.superiorInsName
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">机构等级</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.insLevel
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">机构类型</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.insType
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">统一社会信用代码</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.socialCreditCode
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">经济类型</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.economicType
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">省市区</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.district
          }}
        </div>
      </div>
      <div class="flex items-top mb-[8px] justify-between">
        <div class="text-[#869199] text-[14px] font-400 w-[200px]">地址</div>
        <div class="text-[#1D212B] text-[14px] font-400 text-right">
          {{
            props.formData.address
          }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["formData"]);
</script>
<style lang="scss"></style>
