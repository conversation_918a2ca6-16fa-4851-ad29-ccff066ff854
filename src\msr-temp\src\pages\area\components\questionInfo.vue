<template>
  <view class="cusDetailInfo">
    <view class="product-table">
      <view class="item-table-title" style="background: #f3f4f5; border-radius: 8px 8px 0 0">
        <view class="label">产品</view>
        <view class="value" style="color: #869199; padding-right: 12px;">观念问卷</view>
      </view>
      <view class="item-table">
        <view class="item-table-item" v-for="(item, index) in state.product" :key="index" :style="{
          'border-bottom':
            index == state.product.length - 1 ? '' : '1px solid #e5e6eb',
        }">
          <view class="label">{{ item.name }}</view>
          <view class="value" style="padding-right: 4px;" @tap="handleToSurvey(item.code, item.name)">{{
            currentSurvey(item.code)?.name
          }}</view>
        </view>
      </view>
    </view>

    <!-- 客户标签显示 -->
    <view class="insTabDetail-1" style="margin-top: 4px">
      <view class="cusDetail-item">
        <view class="cusDetail-item-title">
          <view class="cusDetail-item-value" style="
              display: flex;
              flex-direction: column;
              flex-wrap: wrap;
              justify-content: left;
            ">
            <view v-for="(value, key) in labelKey" :key="key" style="
                margin-bottom: 8px;
                color: #1d212b;
                font-size: 14px;
                word-break: break-all;
              ">
              <view style="color: #869199; flex-shrink: 0">{{ key }}：</view>
              <view style="flex: 1; word-wrap: break-word">{{
                props?.potentialData?.[key]
              }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { View } from "@tarojs/components";
import { reactive, ref, onMounted } from "vue";
import { getDetailProduct } from "../../../utils/area.js";
import { IconFont } from "@nutui/icons-vue-taro";
import { productQuery } from "../../../api/area";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { surveyListApi } from "@/msr-temp/src/api/customerAPI.js";

const router = useRouter();

const props = defineProps({
  potentialData: {
    type: Object,
    default: () => ({}),
  },
  potentialVisible: {
    type: Boolean,
    default: false,
  },
  customerDetail: {
    type: Object,
    default: () => ({}),
  },
});

const labelKey = {
  "院内任职": "院长",
  "全国任职": "中华医学会风湿病学分会主委|副主委|常委",
  "省内学术任职": "中华医学会风湿病学分会省级主委|副主委",
  "学术分级(是否为KOL100)": "是",
  "潜力分级": "A (TOP 500)",
  "月度诊疗痛风患者人次(预估)": "4",
  "月度难治性痛风患者人次(预估)": "3",
  "月度罕见病(成人Still等)患者人次(预估)": "1",
  "有使用意愿的人数": "10",
  "有治疗意愿的人数": "4"
}

const detailInfo = Taro.getStorageSync("detailInfo");
const initHrInfo = Taro.getStorageSync("initHrInfo");
/*
*
* insCode: INS0040111221AFC
jurCode: XZ000007
type: cust
customerCode: C0040111221B06
* */
const state = reactive({
  info: {},
  params: {
    customerCode: router.params.customerCode,
    jurCode: router.params.jurCode,
    insCode: router.params.insCode,
    type: "cust",
  },
});

const survey = ref({});

const setInfo = (info) => {
  state.info = info;
};

const currentSurvey = (productCode) => {
  return survey.value?.find((d) => d.productCode === productCode) || {};
};

const getProduct = async () => {
  const res = await productQuery(state.params);
  state.product = res.data.filter(
    (item) => item.claimStatus === "1" || item.claimStatus === "2"
  );
};

const getSurvey = async () => {
  const res = await surveyListApi({
    page: 1,
    pageSize: 20,
    status: "1",
  });
  // const res = surveyMock();
  survey.value = res.rows || [];
};

const handleToSurvey = async (productCode, productName) => {
  const _survey = currentSurvey(productCode);
  await Taro.setStorageSync("_survey", _survey);
  await Taro.navigateTo({
    url: `/pages/area/customer/survey${window.location.search}&surveyId=${_survey.id}&productCode=${productCode}&productName=${productName}&type=${props.potentialVisible ? '048' : ''}&customerCode=${props.customerDetail.customerCode}&customerName=${props.customerDetail.customerName}&insCode=${props.customerDetail.insCode}&insName=${props.customerDetail.insName}&deptName=${props.customerDetail.deptName}`,
  });
};

onMounted(() => {
  getProduct();
  getSurvey();
});

defineExpose({
  setInfo,
});
</script>
<style lang="scss">
.cusTabDetail::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}

.cusDetailInfo {
  font-size: 14px;
  color: #869199;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  overflow-y: scroll;

  .insTabDetail-1 {
    .cusDetail-item {
      margin-top: 8px;

      .cusDetail-item-title {
        .cusDetail-item-value {
          display: flex;
          flex-wrap: wrap;
          align-items: flex-start;
        }
      }
    }
  }

  .info-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .num {
      background: #e8f0ff;
      border-radius: 4px;
      height: 30px;
      line-height: 30px;
      padding: 0 8px;
    }
  }

  .product-table {
    margin-top: 10px;

    .item-table {
      border: 1px solid #e5e6eb;
      border-radius: 0 0 8px 8px;

      .item-table-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        // border-bottom: 1px solid #e5e6eb;

        .label {
          padding-left: 16px;
          width: 70%;
          color: #121d29;
          font-size: 12px;
          text-align: left;
        }

        .value {
          width: 30%;
          color: #2551f2;
          font-size: 14px;
          text-align: center;
        }
      }
    }

    .item-table-title {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border: 1px solid #e5e6eb;
      border-bottom: none;

      .label {
        padding-left: 16px;
        width: 70%;
        color: #869199;
        font-size: 14px;
        text-align: left;
      }

      .value {
        width: 30%;
        color: #869199;
        font-size: 14px;
        text-align: center;
      }
    }
  }
}
</style>
