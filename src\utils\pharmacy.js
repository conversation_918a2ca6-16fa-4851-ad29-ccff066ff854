import {
  pharmacyType,
  areaType,
  pharmacyNature,
  paymentType,
  businessScope,
  approveType,
  developType,
} from "@/api/pharmacy.js";
import Taro from "@tarojs/taro";

export async function getPharmacyType() {
  const { rows } = await pharmacyType();
  Taro.setStorage({
    key: "pharmacyType",
    data: rows,
  });
}
export async function getAreaType() {
  const { rows } = await areaType();
  Taro.setStorage({
    key: "areaType",
    data: rows,
  });
}
export async function getPharmacyNature() {
  const { rows } = await pharmacyNature();
  Taro.setStorage({
    key: "pharmacyNature",
    data: rows,
  });
}
export async function getPaymentType() {
  const { rows } = await paymentType();
  Taro.setStorage({
    key: "paymentType",
    data: rows,
  });
}
export async function getBusinessScope() {
  const { rows } = await businessScope();
  Taro.setStorage({
    key: "businessScope",
    data: rows,
  });
}

export async function getApproveType() {
  const { rows } = await approveType();
  Taro.setStorage({
    key: "approveType",
    data: rows,
  });
}
export async function getDevelopType() {
  const { rows } = await developType();
  Taro.setStorage({
    key: "developType",
    data: rows,
  });
}

export function getPostCode() {
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  if (initHrInfo.jurCodeList) {
    return initHrInfo.jurCodeList.map((item) => item).join(",");
  } else {
    return "";
  }
}

export function getDevelopTypeLabel(code) {
  const approveTypeList = Taro.getStorageSync("developType");
  if (code) {
    return approveTypeList.filter((item) => item.dictValue === code)[0]
      .dictLabel;
  } else {
    return "";
  }
}

export function getPostIdList() {
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  if (initHrInfo.postIdList) {
    return initHrInfo.postIdList.map((item) => item).join(",");
  } else {
    return "";
  }
}
//千分位
export function formatNumber(num) {
  let newNum = Number(num);
  newNum = Math.round(newNum);
  let parts = newNum.toString().split(".");
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  return parts.join(".");
}
//万单位
export function convertToTenThousand(amount) {
  let result = Math.round((amount / 10000) * 100) / 100;
  if (result % 1 === 0) {
    return parseInt(result, 10);
  }
  return result;
}
// 两位小数
export function formatNumber2(number) {
  const num = Number(number);
  return Math.round(num);
}
export function convertToWQYF(amount) {
  console.log(amount);
  const convertedAmount = amount / 10000;
  const roundedAmount = Math.round(convertedAmount);
  const w = Math.floor(roundedAmount);
  const q = Math.floor((roundedAmount - w) * 10);
  const y = Math.floor((roundedAmount - w) * 100 - q * 10);
  const f = Math.floor(((roundedAmount - w) * 1000 - q * 100 - y * 10) * 10);

  return { w, q, y, f };
}
