<template>
  <view class="representPopup">
    <GlobalPopup ref="popupRef">
      <TopSearch @top-search="search" placeholder="请输入代表姓名/工号搜索" />
      <nut-checkbox-group
        v-if="state?.cusOption?.length != 0"
        style="height: 60vh"
        v-model="state.checked"
      >
        <GlobalNutCheckbox
          v-for="item in state.cusOption"
          :value="item.id"
          :key="item.id"
          style="color: red"
        >
          <template #name>
            <view
              :class="{
                'color-label': state.checked.includes(item.id),
              }"
            >
              <text>{{ item.empName }}</text>
              <nut-divider direction="vertical" />
              <text>{{ item.empCode }}</text>
            </view>
          </template>
        </GlobalNutCheckbox>
      </nut-checkbox-group>
      <view class="null-text" v-else>{{
        ready ? "请输入连续两个字进行搜索" : "搜索无数据"
      }}</view>

      <view class="footer-comfirm-v1" style="color: #2551f2" @click="confirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { reactive, ref } from "vue";
import GlobalPopup from "../../../pages/components/globalPopup/globalPopup.vue";
import TopSearch from "../../../pages/components/topSearch/topSearch.vue";
import GlobalNutCheckbox from "../../../pages/components/globalNutCheckbox/globalNutCheckbox.vue";
import { getAssistUser } from "../../../api/institutionalVisitsApi";
const emit = defineEmits(["select-assist"]);
const state = reactive({
  checked: [],
  search: "",
  cusOption: [],
});
const popupRef = ref(null);
const ready = ref(true);
const search = (name) => {
  state.checked = [];
  if (name) {
    state.search = name;
    getCusOption();
  } else {
    state.cusOption = [];
  }
};

const getCusOption = async () => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  const params = {
    search: state.search,
  };
  const res = await getAssistUser(params);
  ready.value = false;
  Taro.hideLoading();

  state.cusOption = res.data;
};
const confirm = () => {
  const arr = state.cusOption.filter((item) => state.checked.includes(item.id));
  emit("select-assist", arr);
  popupRef.value.close();
  state.checked = [];
  state.cusOption = [];
};
const open = () => {
  popupRef.value.open();
};

defineExpose({
  open,
});
</script>
<style lang="scss">
.null-text {
  height: 60vh;
  text-align: center;
  color: #869199;
  font-size: 14px;
  padding-top: 30px;
}
.representPopup {
  padding-bottom: 60px;
  // background: #f4f5f7;
  .nut-checkbox {
    margin-right: 0;
  }
  .nut-checkbox-group {
    font-size: 16px;
    height: 500px;
    overflow: hidden;
    overflow-y: scroll;
    padding: 16px;
    justify-content: space-between;
  }
  .nut-checkbox--reverse .nut-checkbox__label {
    font-size: 16px;
  }
  .footer-comfirm-v1 {
    // height: 60px;
    padding: 16px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .color-label {
    color: #2551f2;
  }
}
</style>
