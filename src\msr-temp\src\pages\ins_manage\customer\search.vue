<template>
  <view class="addCustomer">
    <view class="section">
      <nut-form
        class="form"
        :label-width="200"
        :model-value="state.params"
        ref="ruleForm"
      >
        <FormItem
          ref="insRef"
          label="机构名称"
          loadingBrfore
          propValue="insName"
          required
          placeholder="请选择"
          v-model="state.params.insName"
          @search="searchIns"
          needSearch
          :popupOption="state.options.insName"
          :rules="state.rules.insName"
        />
        <FormItem
          label="客户名称"
          propValue="customerName"
          required
          v-model="state.params.customerName"
          :rules="state.rules.customerName"
        />
      </nut-form>
      <view class="footer-button">
        <nut-row type="flex" justify="center">
          <nut-col style="padding: 0 16px" :span="24">
            <nut-button
              color="#2551F2"
              @click="clickSearchButton"
              shape="square"
              :loading="state.buttonLoading"
              block
              >搜索</nut-button
            >
          </nut-col>
        </nut-row>
      </view>
    </view>
    <view class="textInfo" v-if="!(num === 0 || !state.params.insName)">
      <view class="titleTextInfo">查询结果如下</view>
      <view class="subTextInfo"
        >请选择客户进行新增与认领, 若检索不到该客户, 请判断</view
      >
      <view class="subTextInfo"
        >是否为其他机构同名客户多点执业, 如是, 请选择</view
      >
      <view class="subTextInfo">对应客户后添加多点执业信息</view>
    </view>
    <view class="result-text" style="font-size: 16px">
      <scroll-view
        style="height: 500px"
        :scroll-y="true"
        :scroll-top="state.scrollTop"
        @scrolltolower="onScrollToLower"
      >
        <view v-for="item in state.insList" :key="item.jurId" class="instItem">
          <SearchItem :item="item" @buttonChanged="buttonChanged" />
        </view>
        <view
          v-if="state.insList.length > 0 && state.insList.length == state.total"
          style="color: #869199; font-size: 16px; text-align: center"
          >已全部加载完毕</view
        >
      </scroll-view>
    </view>

    <FooterButton
      text="申请添加客户"
      :disabled="num === 0 || !state.params.insName"
      @click-button="clickAddButton"
      :buttonIcon="arrowRight"
    >
      <template #icon>
        <Uploader color="#fff" />
      </template>
    </FooterButton>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { onMounted, reactive, watch, ref, computed } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { Uploader } from "@nutui/icons-vue-taro";
import arrowRight from "../../../images/arrow-right.png";
import FormItem from "../../../pages/components/formItem/index.vue";
import { institutionList } from "../../../api/area.js";
import { customerDetail, custSearch } from "../../../api/area.js";
import SearchItem from "./searchItem.vue";
import {
  getPostCode,
  getInsLevelColor,
  getInsLevelTextColor,
} from "../../../utils/area.js";

const initHrInfo = Taro.getStorageSync("initHrInfo");
const router = useRouter();
const scrollMyFlag = ref(false);
const num = ref(0);
const ruleForm = ref(null);
const insRef = ref(null);
const query = router.params;
const state = reactive({
  buttonLoading: false,
  options: {
    insName: [],
  },
  rules: {
    customerName: [{ required: true, message: "必填" }],
    insName: [{ required: true, message: "必填" }],
  },
  footerButtontext: "搜索",
  insList: [],
  scrollTop: 0,
  total: 0,
  params: {
    insName: "",
    customerName: "",
    pageNum: 1,
    pageSize: 10,
    insId: "",
    insMdmCode: "",
    insCode: "",
    jurCodeList: getPostCode()?.split(","),
    // bu: initHrInfo?.bu,
  },
});
const intComputer = computed(() => ({
  insId: state.params.insId,
  insCode: state.params.insCode,
  insName: state.params.insName,
  insMdmCode: state.params.insMdmCode,
}));
const getinsList = () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  state.buttonLoading = true;

  custSearch(state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.data.total;
        if (!state.insList.length) {
          state.insList = res.data || [];
        } else {
          state.insList = [...state.insList, ...res.data];
        }
        Taro.hideLoading();
        scrollMyFlag.value = true;
        state.buttonLoading = false;
      } else {
        Taro.hideLoading();
        state.buttonLoading = false;
      }
    })
    .catch(() => {
      Taro.hideLoading();
    });
};
const clickButton = async (item) => {
  let params = {
    jurCode: item.jurCode,
    insCode: item.insCode,
    customerCode: item.customerCode,
    isClaim: "1",
  };
  const org = initHrInfo.orgList.filter((item) => item.postType === "1")[0];

  const res = await customerDetail(params);
  Taro.setStorage({
    key: "detailInfo",
    data: { ...intComputer.value, ...res.data },
  });

  Taro.navigateTo({
    url: `/pages/area/components/editProduct?type=cust`,
  });
};
const jumpClaimProduct = () => {
  Taro.navigateTo({
    url: `/pages/area/components/claimProduct?currentTab=1`,
  });
};
// 多点执业
const addCustomer = (item) => {
  console.log(22222, item);
  Taro.navigateTo({
    url: `/pages/area/customer/addCus?customerName=${
      state.params.customerName
    }&type=masterFlag&item=${JSON.stringify(item)}&ins=${JSON.stringify(
      intComputer.value
    )}`,
  });
};
const buttonChanged = (type, item) => {
  if (type == 1) gotoInst(item);
  if (type == 2) clickButton(item);
  if (type == 3) jumpClaimProduct();
  if (type == 4) addCustomer(item);
};
const searchIns = async (insName = "", callBack) => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  // if (insName.length != 0) {
  const res = await institutionList(1, 100, {
    insName,
    jurCodeList: getPostCode().split(","),
    querySub: 0,
  });
  Taro.hideLoading();

  state.options.insName = res?.data?.rows?.map((el) => ({
    ...el,
    dictLabel: el.insName,
    dictValue: el.insCode,
  }));
  insRef.value.selectSearch(state.options.insName);
  if (callBack) callBack();

  // }
};
const clickSearchButton = () => {
  ruleForm.value.validate().then(async ({ valid, errors }) => {
    if (valid) {
      const arr = state.options.insName.filter(
        (el) => el.insName == state.params.insName
      )[0];
      num.value++;
      state.params.pageNum = 1;
      state.params.insId = arr?.id;
      state.params.insCode = arr?.insCode;
      state.params.insName = arr?.insName;
      state.params.insMdmCode = arr?.insMdmCode;
      state.params.pageNum = 1;
      state.total = 0;
      state.insList = [];
      getinsList();
    } else {
      console.log("error submit!!", errors);
    }
  });
};
// 新增客户
const clickAddButton = () => {
  Taro.navigateTo({
    url: `/pages/area/customer/addCus?customerName=${
      state.params.customerName
    }&type=add&ins=${JSON.stringify(intComputer.value)}`,
  });
};
// 编辑客户
const gotoInst = (item) => {
  Taro.navigateTo({
    url: `/pages/area/customer/addCus?item=${JSON.stringify(
      item
    )}&type=update&ins=${JSON.stringify(intComputer.value)}`,
  });
};
const onScrollToLower = () => {
  if (scrollMyFlag.value && state.insList.length < state.total) {
    state.params.pageNum++;
    scrollMyFlag.value = false;
    getinsList();
  }
};

watch(
  () => state.params.insName,
  (newValue) => {
    if (!newValue) {
      num.value = 0;
      state.insList = [];
    }
  }
);
onMounted(() => {

  Taro.setNavigationBarTitle({ title: "新增客户查询" });
});
</script>
<style lang="scss">
.titleTextInfo {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #2551f2;
  line-height: 19px;
  margin-bottom: 9px;
  margin-top: 16px;
}
.subTextInfo {
  width: 100%;
  text-align: center;
  margin-top: 3px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #4e595e;
  line-height: 15px;
}
.textInfo {
  text-align: center;
  margin-bottom: 27px;
}
.nut-cell-group__wrap {
  margin-top: 0;
  box-shadow: none;
}
.addCustomer {
  padding-bottom: 80px;
  .section {
    background: #fff;
    font-size: 16px;
    // padding: 0 16px;
  }
  .result-text {
    padding-top: 16px;
    text-align: center;
    // background: #eff3f5;
  }
  .result {
    background: #fff;
    // padding: 16px 0 0 16px;
    max-height: 400px !important;
    overflow: auto;
  }
  .instItem {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    .insName {
      font-weight: 500;
      font-size: 18px;
      color: #1d212b;
      margin-left: 4px;
      max-width: 260px;
      display: -webkit-box; /* 对Webkit内核浏览器 */
      -webkit-line-clamp: 2; /* 显示的行数 */
      -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
      overflow: hidden; /* 隐藏超出的内容 */
      text-overflow: ellipsis;
      text-align: left;
    }
  }
  .label {
    &::before {
      content: "*";
      color: red;
      margin-top: 6px;
      margin-right: 5px;
      vertical-align: middle;
    }
  }
  .nut-radio-group {
    padding: 0 16px;
  }
  .footer-button {
    background-color: #ffffff;
    padding: 8px 0;
    width: 100%;

    .nut-button {
      border-radius: 4px;
    }
    .border-none {
      border: none;
    }
    .nut-button__wrap {
      img {
        width: 10px;
        height: 10px;
      }
    }
  }
}
</style>
