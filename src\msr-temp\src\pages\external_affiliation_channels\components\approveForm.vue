<template>
  <view class="ins-addIns">
    <view>
      <nut-form :model-value="state.params" ref="ruleForm">
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">基本信息</view>
        <view class="section">
          <nut-form-item label="姓名" prop="name" :label-width="100">
            <view class="text-right">{{state.params.name}}</view>
          </nut-form-item>
          <nut-form-item label="医疗机构" prop="insName" :label-width="100">
            <view class="text-right">{{state.params.insName}}</view>
          </nut-form-item>
          <nut-form-item label="讲者类型" prop="type" :label-width="100">
            <view class="text-right">{{state.params.type}}</view>
          </nut-form-item>

          <nut-form-item label="退休返聘" prop="isRehired" :label-width="100">
            <view class="text-right">{{state.params.isRehired}}</view>
          </nut-form-item>

          <nut-form-item label="行政级别" prop="job" :label-width="100">
            <view class="text-right">{{state.params.job}}</view>
          </nut-form-item>
          <nut-form-item label="省市区" prop="district" :label-width="100">
            <view class="text-right">{{state.params.district}}</view>
          </nut-form-item>


          <nut-form-item label="学术评级" prop="academicRating" :label-width="100">
            <view class="text-right">{{state.params.academicRating}}</view>
          </nut-form-item>


          <nut-form-item label="品牌评级" prop="brandRating" :label-width="100">
            <view class="text-right">{{state.params.brandRating}}</view>
          </nut-form-item>


          <nut-form-item label="准入评级" prop="accessRating" :label-width="100">
            <view class="text-right">{{state.params.accessRating}}</view>
          </nut-form-item>


          <nut-form-item label="抗衰评级" prop="antiAgingRating" :label-width="100">
            <view class="text-right">{{state.params.antiAgingRating}}</view>
          </nut-form-item>


          <nut-form-item label="治疗领域" prop="therapyArea" :label-width="100">
            <view class="text-right">{{state.params.therapyArea}}</view>
          </nut-form-item>

          <nut-form-item label="性别" prop="sex" :label-width="100" >
            <view class="text-right">{{state.params.sex}}</view>
          </nut-form-item>



          <nut-form-item label="电话" prop="phone" :label-width="100">
            <view class="text-right">{{state.params.phone}}</view>

          </nut-form-item>

          <nut-form-item label="邮件" prop="email" :label-width="100">
            <view class="text-right">{{state.params.email}}</view>
          </nut-form-item>
        </view>
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">补充-身份证</view>
        <view class="section">
          <view style="margin-left: 25%;">
            <view class="photos" v-if="state.cardImageSrc">
              <img :src="state.cardImageSrc" alt="Base64 Image" style="width: 140px;height: 90px;margin-top: 16px;" />

            </view>
          </view>
          <nut-form-item label="证件类型" prop="idCardName" :label-width="100">
            <view class="text-right">{{state.params.idCardName}}</view>
          </nut-form-item>
          <nut-form-item label="身份证号码" prop="idCardNum" :label-width="100">
            <view class="text-right">{{state.params.idCardNum}}</view>
          </nut-form-item>

          <nut-form-item label="地址" prop="idCardAddress" :label-width="100">
            <view class="text-right">{{state.params.idCardAddress}}</view>
          </nut-form-item>

        </view>
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">补充-银行卡</view>
        <view class="section">
          <view style="margin-left: 25%;">
            <view class="photos" v-if="state.bankImageSrc">
              <img :src="state.bankImageSrc" alt="Base64 Image" style="width: 140px;height: 90px;margin-top: 16px;" />
            </view>
          </view>
          <nut-form-item label="开户银行" prop="bankCityCode" :label-width="100">
            <view class="text-right">{{state.params.bankCityCode}}</view>
          </nut-form-item>
          <nut-form-item label="银行卡号 " prop="bankNum" :label-width="100">
            <view class="text-right">{{state.params.bankNum}}</view>
          </nut-form-item>

          <nut-form-item label="开户省市区/县" prop="bankDistrict" :label-width="100">
            <view class="text-right">{{state.params.bankDistrict}}</view>
          </nut-form-item>
          <view class="photo">
            <nut-form-item label="备注" prop="remark" star-position="left">

              <nut-textarea v-model="state.params.remark" limit-show max-length="200" readonly />
            </nut-form-item>
            <nut-form-item label="附件" prop="fileList" star-position="left">
              <view class="upload">
                <view class="item-img" v-for="(item, index) in state.params.fileList" :key="index">
                  <img :src="item.url || item.attUrl" style="width: 80px; height: 80px" @click="handlePreview(index)"/>
                  <img :src="fileClose" class="file-close"/>
                </view>
                <view>
                  <img :src="CameraImg" class="camera-img" />
                </view>
              </view>
            </nut-form-item>
            <nut-image-preview v-if="state.params.fileList" :autoplay="0" :show="showPreview" :images="state.params.fileList.map((d) => ({ src: d.url || d.attUrl }))
              " @close="hideImgPreview" :init-no="imgPreviewIndex" />
          </view>
        </view>
      </nut-form>
    </view>
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
import arrow from "../../../images/arrow.png";
import idcard from "../../../images/idcard.png";
import bank from "../../../images/bank.png";
import CameraImg from "../../../images/uploader.png";
import fileClose from "../../../images/file-close.png";
import {imageUrl} from "../../../api/institutionalVisitsApi";
const initHrInfo = Taro.getStorageSync("000010-initHrInfo");
const orgList = initHrInfo?.orgList?.filter((item) => item.postType === "1")[0] || {};
const isLoading = ref(false);
const selectInsRef = ref(null);
const router = useRouter();
const query = router.params;
const popupRadioRef = ref(null);
const gdmapRef = ref(null);
const ruleForm = ref(null);
const districtPopupRef = ref(null);
const TokenKey = Taro.getStorageSync("000010-access_token");
const imgPreviewIndex = ref(0);
const showPreview = ref(0);
const processId = ref('');

const props = defineProps(['formData'])
const state = reactive({
  rightText: "确定", //根据进来的情况判断

  info: {
    applicant: initHrInfo.empName,
    applicantCode: initHrInfo.empCode,
    postCode: orgList.code,
    postName: orgList.name,
    deptCode: orgList.deptCode,
    deptName: orgList.value,
    ancestors: orgList.ancestors,
    postIdList: initHrInfo.postIdList,
  },
  params: {
    name: '',
    insName: '',
    insCode: '',
    type: '',
    isRehired: '',
    provinceCode: '',
    cityCode: '',
    districtCode: '',
    district: '',
    academicRating: '',
    brandRating: '',
    accessRating: '',
    antiAgingRating: '',
    therapyArea: '',
    sex: '',
    sexName: '',
    phone: '',
    email: '',
    idCardType: '',
    idCardName:'',
    idCardNum: '',
    idCardAddress: '',
    bankCityCode: '',
    bankNum: '',
    bankProvinceCode: '',
    bankDistrictCode: '',
    bankDistrict: '',
    remark: '',
    fileList: []
  },
});

const handlePreview = (index) => {
  imgPreviewIndex.value = index;
  showImgPreview();
};

const hideImgPreview = () => {
  showPreview.value = false;
};

const showImgPreview = () => {
  showPreview.value = true;
};

const viewList = (list) => {
  list.forEach(async (item) => {
    const res = await imageUrl(item);
    state.params.fileList.push(res.data.rows[0]);
  });
};

onMounted(() => {
  if(props.formData) {
    state.params = props.formData;
    if(state.params.fileList) {
      viewList(state.params.fileList)
    }
  }
});

</script>
<style lang="scss">
.ins-addIns {
  padding-bottom: 90px;

  .section {
    font-size: 14px;
    padding: 0 16px;
    background: #fff;

    .label {
      color: #1d212b;

      &::before {
        content: "*";
        color: red;
        vertical-align: middle;
        margin-right: 5px;
      }
    }

    .photos {
      width: 164px;
      height: 122px;
      background-color: #F3F4F5;
      text-align: center;
    }

    .photo {
      .nut-cell {
        display: block;
      }

      .nut-form-item__label.nut-cell__title {
        width: 100%;
      }

      .upload {
        margin-top: 8px;
        display: flex;

        .item-img {
          position: relative;
          margin-right: 8px;

          .file-close {
            width: 20px;
            height: 20px;
            position: absolute;
            top: 0;
            right: 0;
          }
        }

        .camera-img {
          width: 80px;
          height: 80px;
        }
      }
    }
  }

  .nut-cell__title {
    color: #1d212b;
  }

  .nut-input-left-box {
    margin-right: 10px;
  }

  .arrowIcon {
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }

  .location_ins {
    width: 24px;
    height: 24px;
  }

  .nut-cell-group__wrap {
    margin-top: 0;
    background: none;
  }

  .nut-cell__title {
    font-size: 16px;
  }

  // .nut-input__inner {
  //   white-space: pre-wrap !important;
  // }
  // .nut-input {
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-form-item__body__slots {
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-cell__value {
  //   display: block;
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-cell__value .nut-form-item__body {
  //   display: block;
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
}
</style>
