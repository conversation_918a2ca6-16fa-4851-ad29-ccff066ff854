<template>
  <view class="globalNutCheckbox">
    <nut-checkbox :label="props.value" text-position="left">
      <slot name="name" />
      <template #icon>
        <img :src="icon" alt="" class="radioIcon" />
      </template>
      <template #checkedIcon>
        <img :src="checkedIcon" alt="" class="radioIcon" />
      </template>
    </nut-checkbox>
  </view>
</template>
<script setup>
import icon from "../../../images/radio-icon1.png";
import { defineEmits, defineProps, nextTick, onMounted, reactive } from "vue";
import checkedIcon from "../../../images/radio-icon2.png";

const props = defineProps({
  value: {
    type: String,
    default: "",
  },
});
const state = reactive({});
</script>
<style lang="scss">
.globalNutCheckbox {
  font-size: 16px;
  .radioIcon {
    width: 20px;
    height: 20px;
    display: block;
  }
}
</style>
