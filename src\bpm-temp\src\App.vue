<script setup lang="ts">
import { storeToRefs } from 'pinia'
import useAppStore from '@/stores/modules/app'
import useRouteCache from '@/stores/modules/routeCache'
import useAutoThemeSwitcher from '@/hooks/useAutoThemeSwitcher'
import { useRouter } from 'vue-router';
const router = useRouter();
import './styles/var.less';

useHead({
  title: 'Vue3 Vant Mobile',
  meta: [
    {
      name: 'description',
      content: 'Vue + Vite H5 Starter Template',
    },
    {
      name: 'theme-color',
      content: () => isDark.value ? '#00aba9' : '#ffffff',
    },
  ],
  link: [
    {
      rel: 'icon',
      type: 'image/svg+xml',
      href: () => preferredDark.value ? '/favicon-dark.svg' : '/favicon.svg',
    },
  ],
})

const themeVars = ref({
  primaryColor: '#2551f2',
  primaryColorEnd: '#2551f2'
})

const appStore = useAppStore()
const { mode } = storeToRefs(appStore)

const { initializeThemeSwitcher } = useAutoThemeSwitcher(appStore)

const keepAliveRouteNames = computed(() => {
  return useRouteCache().routeCaches as string[]
})

onMounted(() => {
  initializeThemeSwitcher()
  const urlParams = new URLSearchParams(window.location.search) as any;
  const params = {} as any;

  for (const [key, value] of urlParams) {
    params[key] = value;
  }
  const { clientId, token } = params;
  if(token && clientId) {
    sessionStorage.setItem('token', token);
    sessionStorage.setItem('clientId', clientId);
  }
})
</script>

<template>
  <VanConfigProvider :theme="mode" :theme-vars="themeVars">
<!--    <NavBar />-->
    <router-view v-slot="{ Component, route }">
      <section class="app-wrapper">
        <keep-alive :include="keepAliveRouteNames">
          <component :is="Component" :key="route.name" />
        </keep-alive>
      </section>
    </router-view>
    <TabBar />
  </VanConfigProvider>
</template>

<style scoped>
.app-wrapper {
  width: 100%;
  position: relative;
  /*padding: 16px;*/
}
</style>
