<template>
  <view class="insList">
    <view class="title">共{{ state.total }}家机构</view>
    <scroll-view
      style="height: 550px"
      :scroll-y="true"
      :scroll-top="state.scrollTop"
      @scrolltolower="onScrollToLower"
    >
      <view
        style="
          position: absolute;
          top: 28%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!state.insList.length && scrollMyFlag"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>
      <view v-for="item in state.insList" :key="item.jurId" class="instItem">
        <view
          @click="gotoInst(item)"
          style="
            display: flex;
            flex-direction: column;
            padding: 16px 16px 12px 16px;
          "
        >
          <view
            style="display: flex; flex-direction: row; align-items: flex-start"
          >
            <!-- <img :src="hospital" style="width: 24px; height: 24px" /> -->
            <view
              style="
                font-weight: 500;
                font-size: 18px;
                color: #1d212b;

                max-width: 260px;
                -webkit-line-clamp: 2; /* 显示的行数 */
                -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
                overflow: hidden; /* 隐藏超出的内容 */
                text-overflow: ellipsis;
              "
            >
              {{
                item.insName.length > 21
                  ? item.insName.substring(0, 21) + "..."
                  : item.insName
              }}
              <nut-tag
                plain
                color="#94BFFF"
                text-color="#2551F2"
                style="margin-top: -4px; vertical-align: middle"
              >
                {{ item.insMdmCode }}
              </nut-tag></view
            >

            <view style="margin-left: auto">
              <img :src="arrowRight" style="width: 15px; height: 17px"
            /></view>
          </view>
          <view
            style="
              display: flex;
              align-items: center;
              vertical-align: text-bottom;
              font-size: 14px;
              color: #869199;
            "
            v-if="item.empName"
          >
            <img :src="user" alt="" class="icons" />
            {{ item.empName }}
          </view>
        </view>
      </view>
      <view
        v-if="state.insList.length > 0 && state.insList.length == state.total"
        style="color: #869199; font-size: 16px; text-align: center"
        >已全部加载完毕</view
      >
    </scroll-view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { defineEmits, defineProps, ref, reactive, defineExpose } from "vue";
import arrow from "../../../images/arrow.png";
import arrowRight from "../../../images/arrow-right.png";
import address from "../../../images/location_a.png";
import hospital from "../../../images/iconhospital.png";
import { drugstoreInsList } from "../../../api/pharmacy.js";
import { Clock } from "@nutui/icons-vue-taro";
import location_a from "../../../images/location_a.png";
import user from "../../../images/user_3.png";
import { getPostIdList } from "../../../utils/pharmacy";
import noData from "../../../images/no-data.png";
const emit = defineEmits(["changePcdvalue"]);
const initHrInfo = Taro.getStorageSync("initHrInfo");
const scrollMyFlag = ref(false);
const props = defineProps({
  tab: {
    type: String,
    default: "",
  },
});

const state = reactive({
  scrollTop: 0,
  pageNum: 1,
  pageSize: 10,
  params: {
    insName: "",

    provinceCode: "",
    cityCode: "",
    districtCode: "",
    postIdStr: getPostIdList(),
    identity: initHrInfo.identity,
  },
  total: 0,
  pcdvalue: "",
  insList: [],
});

const getinsList = () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });

  drugstoreInsList(state.pageNum, state.pageSize, state.params)
    .then((res) => {
      if (res.code == 200) {
        state.total = res.total;
        if (!state.insList.length) {
          console.log("33333", res.rows);
          state.insList = res.rows || [];
        } else {
          state.insList = [...state.insList, ...res.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = true;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      Taro.hideLoading();
    });
};

const topSearch = (search) => {
  state.params.insName = search;
  state.total = 0;
  state.pageNum = 1;
  state.insList = [];
  getinsList();
};

const insFilters = () => {
  const info = {
    province: state.params.provinceCode,
    city: state.params.cityCode,
    district: state.params.districtCode,
    pcdvalue: state.pcdvalue,
  };
  Taro.navigateTo({
    url: `/pages/area/ins/insFilters?info=${JSON.stringify(info)}`,
  });
  // Taro.navigateTo({
  //   url: `/pages/area/ins/insFilters?provinceCode=${state.params.provinceCode}&cityCode=${state.params.cityCode}&districtCode=${state.params.districtCode}&pcdvalue=${state.pcdvalue}`,
  // });
};
const onScrollToLower = () => {
  if (scrollMyFlag.value && state.insList.length < state.total) {
    state.pageNum++;
    scrollMyFlag.value = false;
    getinsList();
  }
};

const initInsList = () => {
  const insFilters = Taro.getStorageSync("insFilters");
  console.log("insFilters", insFilters);
  if (insFilters) {
    state.params.provinceCode = insFilters.province;
    state.params.cityCode = insFilters.city;
    state.params.districtCode = insFilters.district;
    state.pcdvalue = insFilters.pcdvalue;
    Taro.removeStorageSync("insFilters");
  }
  state.total = 0;
  state.pageNum = 1;
  state.insList = [];
  getinsList();
  emit("change-pcdvalue", state.pcdvalue);
};

const gotoInst = (item) => {
  Taro.navigateTo({
    url: `/pages/association/detail/index?insCode=${item.insCode}&insName=${
      item.insName
    }&area=${item.area || ""}&jurCode=${item.jurCode}&insMdmCode=${
      item.insMdmCode
    }`,
  });
};
defineExpose({
  topSearch,
  insFilters,
  initInsList,
});
</script>
<style lang="scss">
.insList {
  padding: 0px 0px;
  width: 100vw;
  // height: 630px;
  font-family: PingFang SC, PingFang SC;
  .title {
    color: #869199;
    font-size: 14px;
    padding: 18px 12px 8px 12px;
  }
  .icons {
    width: 14px;
    height: 14px;
    display: block;
    vertical-align: middle;
    margin-right: 4px;
  }
  .instItem {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    margin-left: 14px;
    margin-right: 14px;
  }
  .address {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    color: #869199;
  }
}
</style>
