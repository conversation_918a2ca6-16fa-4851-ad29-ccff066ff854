<template>
  <div class="footerButtonTwo">
    <nut-row :gutter="10" type="flex" justify="center">
      <nut-col :span="11">
        <nut-button
          :plain="props.plainFlag"
          shape="square"
          :color="props.leftColor"
          block
          :disabled="props.disabledLeft"
          @click="leftClick"
        >
          <text :style="{ color: props.leftTextColor }">
            {{ props.leftText }}
          </text>
          <template #icon>
            <slot name="iconLeft" class="buttonIcon"></slot>
          </template>
        </nut-button>
      </nut-col>
      <nut-col :span="11">
        <nut-button
          shape="square"
          color="#2551F2"
          style="border: none"
          block
          :disabled="props.disabledRight"
          @click="rightClick"
          :loading="props.isLoading"
        >
          {{ props.rightText }}
          <template #icon>
            <slot name="iconRight" class="buttonIcon"></slot>
          </template>
        </nut-button>
      </nut-col>
    </nut-row>
  </div>
</template>

<script setup>
import { defineProps, reactive, watch } from "vue";
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
  leftText: {
    type: String,
    default: "",
  },
  rightText: {
    type: String,
    default: "",
  },
  plainFlag: {
    type: Boolean,
    default: true,
  },
  leftColor: {
    type: String,
    default: "#2551F2",
  },
  leftTextColor: {
    type: String,
    default: "#2551F2",
  },
  disabledLeft: {
    type: Boolean,
    default: false,
  },
  disabledRight: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["click-left", "click-right"]);
const state = reactive({});
const leftClick = () => {
  emit("click-left");
};
const rightClick = () => {
  emit("click-right");
};
watch(
  () => props.disabledLeft,
  () => {
    console.log("newValue", props.disabledLeft);
  }
);
</script>

<style lang="scss">
.footerButtonTwo {
  position: fixed;
  bottom: 0;
  background-color: #ffffff;
  width: 100%;
  padding-top: 8px;
  padding-bottom: 34px;
  .nut-button {
    border-radius: 4px;
  }
  .border-none {
    border: none;
  }
}
</style>
