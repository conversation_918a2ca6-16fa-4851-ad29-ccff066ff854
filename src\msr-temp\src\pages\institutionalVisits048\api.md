---
title: athena-cloud
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"
---

# athena-cloud

Base URLs:

# Authentication

# 金项链/标品/athena-tenant-ade/048 客户拜访

<a id="opIddetail_1"></a>

## POST 拜协访详情（客户）

POST /ade/api/visit/detail

拜协访详情（客户）

> Body 请求参数

```json
{
  "visitInstitutionId": 0,
  "assistId": 0
}
```

### 请求参数

| 名称          | 位置   | 类型                                  | 必选 | 说明 |
| ------------- | ------ | ------------------------------------- | ---- | ---- |
| Authorization | header | string                                | 否   | none |
| clientId      | header | string                                | 否   | none |
| body          | body   | [VisitDetailBo](#schemavisitdetailbo) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"createBy":0,"createTime":"2019-08-24T14:15:22Z","updateBy":0,"updateTime":"2019-08-24T14:15:22Z","params":{"property1":{},"property2":{}},"tenantId":"string","id":0,"visitUserHrCode":"string","visitDeptCode":"string","visitDeptName":"string","visitPostCode":"string","visitPostName":"string","parentEmpCode":"string","parentDeptCode":"string","parentDeptName":"string","parentPostCode":"string","parentPostName":"string","jurCode":"string","institutionCode":"string","institutionName":"string","insLat":"string","insLon":"string","visitTime":"2019-08-24T14:15:22Z","planVisitContent":"string","communicateContent":"string","completeTime":"2019-08-24T14:15:22Z","visitPurposeCode":"string","visitTypeCode":"string","cancelTime":"2019-08-24T14:15:22Z","planFlag":"string","visitAssistFlag":"string","status":"string","assistRequirement":"string","visitSignId":0,"delFlag":"string","extendInfo":"string","sex":"string","insDeptName":"string","lastCommunicateContent":"string","insAddress":"string","visitUserName":"string","avatar":"string","visitStatusName":"string","signInAddress":"string","signInTime":"2019-08-24T14:15:22Z","duration":"string","signOutAddress":"string","signOutTime":"2019-08-24T14:15:22Z","sysSignOutFlag":"string","signId":0,"signInDistance":0,"signOutDistance":0,"customerName":"string","customerCode":"string","assistRemark":"string","signInPhotoList":[0],"signOutPhotoList":[0],"assistUserVOs":[{}],"assistScoreVos":[{}],"assistEvaluateUsers":["string"],"assistDetail":{},"productForms":[{"productInfoList":[{"info":null,"cusProdRespCode":null,"remark":null}],"classification":["string"],"productCode":"string","productName":"string","jurCodeList":["string"],"subJurCodeList":["string"],"insCode":"string"}],"customerCommunicateContent":"string","province":"string","city":"string","district":"string","insLabel":"string"}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                    |
| ------ | ------------------------------------------------------- | ---- | --------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RVisitVo](#schemarvisitvo) |

<a id="opIdupdateVisitInfo"></a>

## POST 修改客户拜访信息

POST /ade/api/visit/customer/update

修改客户拜访信息

> Body 请求参数

```json
{
  "visitInstitutionId": 0,
  "visitPurposeCode": "string",
  "communicateContent": "string",
  "assistRequirement": "string",
  "assistForm": [{}],
  "productForms": [
    {
      "productInfoList": [
        {
          "info": "string",
          "cusProdRespCode": "string",
          "remark": {
            "property1": "string",
            "property2": "string"
          }
        }
      ],
      "classification": ["string"],
      "productCode": "string",
      "productName": "string",
      "jurCodeList": ["string"],
      "subJurCodeList": ["string"],
      "insCode": "string"
    }
  ],
  "extendInfo": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                              | 必选 | 说明 |
| ------------- | ------ | --------------------------------- | ---- | ---- |
| Authorization | header | string                            | 否   | none |
| clientId      | header | string                            | 否   | none |
| body          | body   | [VisitInfoBo](#schemavisitinfobo) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型      |
| ------ | ------------------------------------------------------- | ---- | ------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [R](#schemar) |

<a id="opIdaddVisitInfo_1"></a>

## POST 添加客户拜访信息

POST /ade/api/visit/customer/add

添加客户拜访信息

> Body 请求参数

```json
{
  "visitInfoBo": {
    "visitInstitutionId": 0,
    "visitPurposeCode": "string",
    "communicateContent": "string",
    "assistRequirement": "string",
    "assistForm": [{}],
    "productForms": [
      {
        "productInfoList": [
          {
            "info": null,
            "cusProdRespCode": null,
            "remark": null
          }
        ],
        "classification": ["string"],
        "productCode": "string",
        "productName": "string",
        "jurCodeList": ["string"],
        "subJurCodeList": ["string"],
        "insCode": "string"
      }
    ],
    "extendInfo": "string"
  },
  "visitSignBo": {
    "id": 0,
    "signType": "string",
    "communicateContent": "string",
    "visitInstitutionId": 0,
    "signInTime": "2019-08-24T14:15:22Z",
    "signOutTime": "2019-08-24T14:15:22Z",
    "signInLon": "string",
    "signInLat": "string",
    "signOutLon": "string",
    "signOutLat": "string",
    "signInAddress": "string",
    "signOutAddress": "string",
    "signInRemark": "string",
    "signOutRemark": "string",
    "signInDistance": "string",
    "signOutDistance": "string",
    "isSysSignOut": 0,
    "signInPhotoList": [
      {
        "attUrl": "string",
        "ossId": 0
      }
    ],
    "signOutPhotoList": [
      {
        "attUrl": "string",
        "ossId": 0
      }
    ],
    "institutionCode": "string",
    "institutionName": "string",
    "customerCode": "string",
    "customerName": "string",
    "customerLevelCode": "string",
    "insDeptName": "string",
    "visitTime": "2019-08-24T14:15:22Z",
    "completeTime": "2019-08-24T14:15:22Z",
    "visitTypeCode": "string",
    "insLat": "string",
    "insLon": "string",
    "isPlan": 0,
    "jurCode": "string",
    "extendInfo": "string"
  }
}
```

### 请求参数

| 名称          | 位置   | 类型                                    | 必选 | 说明 |
| ------------- | ------ | --------------------------------------- | ---- | ---- |
| Authorization | header | string                                  | 否   | none |
| clientId      | header | string                                  | 否   | none |
| body          | body   | [VisitContentBo](#schemavisitcontentbo) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型      |
| ------ | ------------------------------------------------------- | ---- | ------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [R](#schemar) |

<a id="opIdaddVisitInfo_2"></a>

## GET 获取机构/客户标签

GET /ade/api/label

获取机构/客户标签

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| type          | query  | string | 是   | none |
| code          | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"property1":"string","property2":"string"}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                    |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RMapStringString](#schemarmapstringstring) |

<a id="opIdcheckDept"></a>

## GET 048 部门校验

GET /ade/api/check-dept

048 部门校验

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| deptCode      | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                    |
| ------ | ------------------------------------------------------- | ---- | --------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RBoolean](#schemarboolean) |

# 数据模型

<h2 id="tocS_R">R</h2>

<a id="schemar"></a>
<a id="schema_R"></a>
<a id="tocSr"></a>
<a id="tocsr"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

响应信息主体

### 属性

| 名称 | 类型           | 必选  | 约束 | 中文名 | 说明       |
| ---- | -------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32) | false | none |        | 消息状态码 |
| msg  | string         | false | none |        | 消息内容   |
| data | object         | false | none |        | 数据对象   |

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

响应信息主体

### 属性

| 名称 | 类型           | 必选  | 约束 | 中文名 | 说明       |
| ---- | -------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32) | false | none |        | 消息状态码 |
| msg  | string         | false | none |        | 消息内容   |
| data | boolean        | false | none |        | 数据对象   |

<h2 id="tocS_VisitSignAttachmentBo">VisitSignAttachmentBo</h2>

<a id="schemavisitsignattachmentbo"></a>
<a id="schema_VisitSignAttachmentBo"></a>
<a id="tocSvisitsignattachmentbo"></a>
<a id="tocsvisitsignattachmentbo"></a>

```json
{
  "attUrl": "string",
  "ossId": 0
}
```

### 属性

| 名称   | 类型           | 必选  | 约束 | 中文名 | 说明        |
| ------ | -------------- | ----- | ---- | ------ | ----------- |
| attUrl | string         | false | none |        | 附件 URL    |
| ossId  | integer(int64) | false | none |        | 对象存储 id |

<h2 id="tocS_VisitSignBo">VisitSignBo</h2>

<a id="schemavisitsignbo"></a>
<a id="schema_VisitSignBo"></a>
<a id="tocSvisitsignbo"></a>
<a id="tocsvisitsignbo"></a>

```json
{
  "id": 0,
  "signType": "string",
  "communicateContent": "string",
  "visitInstitutionId": 0,
  "signInTime": "2019-08-24T14:15:22Z",
  "signOutTime": "2019-08-24T14:15:22Z",
  "signInLon": "string",
  "signInLat": "string",
  "signOutLon": "string",
  "signOutLat": "string",
  "signInAddress": "string",
  "signOutAddress": "string",
  "signInRemark": "string",
  "signOutRemark": "string",
  "signInDistance": "string",
  "signOutDistance": "string",
  "isSysSignOut": 0,
  "signInPhotoList": [
    {
      "attUrl": "string",
      "ossId": 0
    }
  ],
  "signOutPhotoList": [
    {
      "attUrl": "string",
      "ossId": 0
    }
  ],
  "institutionCode": "string",
  "institutionName": "string",
  "customerCode": "string",
  "customerName": "string",
  "customerLevelCode": "string",
  "insDeptName": "string",
  "visitTime": "2019-08-24T14:15:22Z",
  "completeTime": "2019-08-24T14:15:22Z",
  "visitTypeCode": "string",
  "insLat": "string",
  "insLon": "string",
  "isPlan": 0,
  "jurCode": "string",
  "extendInfo": "string"
}
```

签到/退表单

### 属性

| 名称               | 类型                                                    | 必选  | 约束 | 中文名 | 说明                                                                             |
| ------------------ | ------------------------------------------------------- | ----- | ---- | ------ | -------------------------------------------------------------------------------- |
| id                 | integer(int64)                                          | false | none |        | 主键                                                                             |
| signType           | string                                                  | false | none |        | 打卡类型:签到:0 签退:1                                                           |
| communicateContent | string                                                  | false | none |        | none                                                                             |
| visitInstitutionId | integer(int64)                                          | false | none |        | 拜访表主键,除了临时拜访签到其他情况必填                                          |
| signInTime         | string(date-time)                                       | false | none |        | 签到时间                                                                         |
| signOutTime        | string(date-time)                                       | false | none |        | 签退时间                                                                         |
| signInLon          | string                                                  | false | none |        | 签到经度                                                                         |
| signInLat          | string                                                  | false | none |        | 签到纬度                                                                         |
| signOutLon         | string                                                  | false | none |        | 签退经度                                                                         |
| signOutLat         | string                                                  | false | none |        | 签退纬度                                                                         |
| signInAddress      | string                                                  | false | none |        | 签到地址                                                                         |
| signOutAddress     | string                                                  | false | none |        | 签退地址                                                                         |
| signInRemark       | string                                                  | false | none |        | 签到补充说明                                                                     |
| signOutRemark      | string                                                  | false | none |        | 签退补充说明                                                                     |
| signInDistance     | string                                                  | false | none |        | 签到时偏差距离                                                                   |
| signOutDistance    | string                                                  | false | none |        | 签退时偏差距离                                                                   |
| isSysSignOut       | integer(int32)                                          | false | none |        | 是否系统签退                                                                     |
| signInPhotoList    | [[VisitSignAttachmentBo](#schemavisitsignattachmentbo)] | false | none |        | 签到照片 url 列表                                                                |
| signOutPhotoList   | [[VisitSignAttachmentBo](#schemavisitsignattachmentbo)] | false | none |        | 签退照片 url 列表                                                                |
| institutionCode    | string                                                  | false | none |        | 机构主数据编码                                                                   |
| institutionName    | string                                                  | false | none |        | 机构名称(临时拜访签到需填写)                                                     |
| customerCode       | string                                                  | false | none |        | 客户编码                                                                         |
| customerName       | string                                                  | false | none |        | 客户名称                                                                         |
| customerLevelCode  | string                                                  | false | none |        | 客户等级编码                                                                     |
| insDeptName        | string                                                  | false | none |        | 科室                                                                             |
| visitTime          | string(date-time)                                       | false | none |        | 拜访开始时间(临时拜访签到需填写)                                                 |
| completeTime       | string(date-time)                                       | false | none |        | 拜访结束时间                                                                     |
| visitTypeCode      | string                                                  | false | none |        | 拜访类型编码 [院内-0，院外-1]（系统字典+业务字典）                               |
| insLat             | string                                                  | false | none |        | 机构纬度                                                                         |
| insLon             | string                                                  | false | none |        | 机构经度                                                                         |
| isPlan             | integer(int32)                                          | false | none |        | 是否计划 [0,1]                                                                   |
| jurCode            | string                                                  | false | none |        | 辖区编码<br /> 考虑体系权限配置，可能存在不属于拜访人辖区 需要通过前端传 jurCode |
| extendInfo         | string                                                  | false | none |        | 拓展信息                                                                         |

<h2 id="tocS_VisitProductBo">VisitProductBo</h2>

<a id="schemavisitproductbo"></a>
<a id="schema_VisitProductBo"></a>
<a id="tocSvisitproductbo"></a>
<a id="tocsvisitproductbo"></a>

```json
{
  "productInfoList": [
    {
      "info": "string",
      "cusProdRespCode": "string",
      "remark": {
        "property1": "string",
        "property2": "string"
      }
    }
  ],
  "classification": ["string"],
  "productCode": "string",
  "productName": "string",
  "jurCodeList": ["string"],
  "subJurCodeList": ["string"],
  "insCode": "string"
}
```

拜访产品信息表单

### 属性

| 名称            | 类型                                              | 必选  | 约束 | 中文名 | 说明                 |
| --------------- | ------------------------------------------------- | ----- | ---- | ------ | -------------------- |
| productInfoList | [[VisitProductInfoBo](#schemavisitproductinfobo)] | false | none |        | 拜访产品推广信息编码 |
| classification  | [string]                                          | false | none |        | 产品分类             |
| productCode     | string                                            | false | none |        | 拜访产品编码         |
| productName     | string                                            | false | none |        | 拜访产品名称         |
| jurCodeList     | [string]                                          | false | none |        | 辖区编码             |
| subJurCodeList  | [string]                                          | false | none |        | 下属辖区编码         |
| insCode         | string                                            | false | none |        | 拜访机构编码         |

<h2 id="tocS_VisitProductInfoBo">VisitProductInfoBo</h2>

<a id="schemavisitproductinfobo"></a>
<a id="schema_VisitProductInfoBo"></a>
<a id="tocSvisitproductinfobo"></a>
<a id="tocsvisitproductinfobo"></a>

```json
{
  "info": "string",
  "cusProdRespCode": "string",
  "remark": {
    "property1": "string",
    "property2": "string"
  }
}
```

### 属性

| 名称                       | 类型   | 必选  | 约束 | 中文名 | 说明         |
| -------------------------- | ------ | ----- | ---- | ------ | ------------ |
| info                       | string | false | none |        | 产品推广信息 |
| cusProdRespCode            | string | false | none |        | 客户反馈     |
| remark                     | object | false | none |        | none         |
| » **additionalProperties** | string | false | none |        | none         |

<h2 id="tocS_VisitDetailBo">VisitDetailBo</h2>

<a id="schemavisitdetailbo"></a>
<a id="schema_VisitDetailBo"></a>
<a id="tocSvisitdetailbo"></a>
<a id="tocsvisitdetailbo"></a>

```json
{
  "visitInstitutionId": 0,
  "assistId": 0
}
```

拜协访详情:

### 属性

| 名称               | 类型           | 必选  | 约束 | 中文名 | 说明    |
| ------------------ | -------------- | ----- | ---- | ------ | ------- |
| visitInstitutionId | integer(int64) | false | none |        | 拜访 id |
| assistId           | integer(int64) | false | none |        | 协访 id |

<h2 id="tocS_VisitInfoBo">VisitInfoBo</h2>

<a id="schemavisitinfobo"></a>
<a id="schema_VisitInfoBo"></a>
<a id="tocSvisitinfobo"></a>
<a id="tocsvisitinfobo"></a>

```json
{
  "visitInstitutionId": 0,
  "visitPurposeCode": "string",
  "communicateContent": "string",
  "assistRequirement": "string",
  "assistForm": [{}],
  "productForms": [
    {
      "productInfoList": [
        {
          "info": "string",
          "cusProdRespCode": "string",
          "remark": {
            "property1": "string",
            "property2": "string"
          }
        }
      ],
      "classification": ["string"],
      "productCode": "string",
      "productName": "string",
      "jurCodeList": ["string"],
      "subJurCodeList": ["string"],
      "insCode": "string"
    }
  ],
  "extendInfo": "string"
}
```

拜访信息

### 属性

| 名称               | 类型                                      | 必选  | 约束 | 中文名 | 说明                              |
| ------------------ | ----------------------------------------- | ----- | ---- | ------ | --------------------------------- |
| visitInstitutionId | integer(int64)                            | false | none |        | 拜访机构表主键                    |
| visitPurposeCode   | string                                    | false | none |        | 拜访目的编码（系统字典+业务字典） |
| communicateContent | string                                    | false | none |        | 实际沟通内容                      |
| assistRequirement  | string                                    | false | none |        | 协访需求                          |
| assistForm         | [object]                                  | false | none |        | 选择协访人                        |
| productForms       | [[VisitProductBo](#schemavisitproductbo)] | false | none |        | 产品推广信息                      |
| extendInfo         | string                                    | false | none |        | 拓展信息                          |

<h2 id="tocS_RVisitVo">RVisitVo</h2>

<a id="schemarvisitvo"></a>
<a id="schema_RVisitVo"></a>
<a id="tocSrvisitvo"></a>
<a id="tocsrvisitvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "tenantId": "string",
    "id": 0,
    "visitUserHrCode": "string",
    "visitDeptCode": "string",
    "visitDeptName": "string",
    "visitPostCode": "string",
    "visitPostName": "string",
    "parentEmpCode": "string",
    "parentDeptCode": "string",
    "parentDeptName": "string",
    "parentPostCode": "string",
    "parentPostName": "string",
    "jurCode": "string",
    "institutionCode": "string",
    "institutionName": "string",
    "insLat": "string",
    "insLon": "string",
    "visitTime": "2019-08-24T14:15:22Z",
    "planVisitContent": "string",
    "communicateContent": "string",
    "completeTime": "2019-08-24T14:15:22Z",
    "visitPurposeCode": "string",
    "visitTypeCode": "string",
    "cancelTime": "2019-08-24T14:15:22Z",
    "planFlag": "string",
    "visitAssistFlag": "string",
    "status": "string",
    "assistRequirement": "string",
    "visitSignId": 0,
    "delFlag": "string",
    "extendInfo": "string",
    "sex": "string",
    "insDeptName": "string",
    "lastCommunicateContent": "string",
    "insAddress": "string",
    "visitUserName": "string",
    "avatar": "string",
    "visitStatusName": "string",
    "signInAddress": "string",
    "signInTime": "2019-08-24T14:15:22Z",
    "duration": "string",
    "signOutAddress": "string",
    "signOutTime": "2019-08-24T14:15:22Z",
    "sysSignOutFlag": "string",
    "signId": 0,
    "signInDistance": 0,
    "signOutDistance": 0,
    "customerName": "string",
    "customerCode": "string",
    "assistRemark": "string",
    "signInPhotoList": [0],
    "signOutPhotoList": [0],
    "assistUserVOs": [{}],
    "assistScoreVos": [{}],
    "assistEvaluateUsers": ["string"],
    "assistDetail": {},
    "productForms": [
      {
        "productInfoList": [
          {
            "info": null,
            "cusProdRespCode": null,
            "remark": null
          }
        ],
        "classification": ["string"],
        "productCode": "string",
        "productName": "string",
        "jurCodeList": ["string"],
        "subJurCodeList": ["string"],
        "insCode": "string"
      }
    ],
    "customerCommunicateContent": "string",
    "province": "string",
    "city": "string",
    "district": "string",
    "insLabel": "string"
  }
}
```

响应信息主体

### 属性

| 名称 | 类型                      | 必选  | 约束 | 中文名 | 说明         |
| ---- | ------------------------- | ----- | ---- | ------ | ------------ |
| code | integer(int32)            | false | none |        | 消息状态码   |
| msg  | string                    | false | none |        | 消息内容     |
| data | [VisitVo](#schemavisitvo) | false | none |        | 机构拜访列表 |

<h2 id="tocS_VisitVo">VisitVo</h2>

<a id="schemavisitvo"></a>
<a id="schema_VisitVo"></a>
<a id="tocSvisitvo"></a>
<a id="tocsvisitvo"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "visitUserHrCode": "string",
  "visitDeptCode": "string",
  "visitDeptName": "string",
  "visitPostCode": "string",
  "visitPostName": "string",
  "parentEmpCode": "string",
  "parentDeptCode": "string",
  "parentDeptName": "string",
  "parentPostCode": "string",
  "parentPostName": "string",
  "jurCode": "string",
  "institutionCode": "string",
  "institutionName": "string",
  "insLat": "string",
  "insLon": "string",
  "visitTime": "2019-08-24T14:15:22Z",
  "planVisitContent": "string",
  "communicateContent": "string",
  "completeTime": "2019-08-24T14:15:22Z",
  "visitPurposeCode": "string",
  "visitTypeCode": "string",
  "cancelTime": "2019-08-24T14:15:22Z",
  "planFlag": "string",
  "visitAssistFlag": "string",
  "status": "string",
  "assistRequirement": "string",
  "visitSignId": 0,
  "delFlag": "string",
  "extendInfo": "string",
  "sex": "string",
  "insDeptName": "string",
  "lastCommunicateContent": "string",
  "insAddress": "string",
  "visitUserName": "string",
  "avatar": "string",
  "visitStatusName": "string",
  "signInAddress": "string",
  "signInTime": "2019-08-24T14:15:22Z",
  "duration": "string",
  "signOutAddress": "string",
  "signOutTime": "2019-08-24T14:15:22Z",
  "sysSignOutFlag": "string",
  "signId": 0,
  "signInDistance": 0,
  "signOutDistance": 0,
  "customerName": "string",
  "customerCode": "string",
  "assistRemark": "string",
  "signInPhotoList": [0],
  "signOutPhotoList": [0],
  "assistUserVOs": [{}],
  "assistScoreVos": [{}],
  "assistEvaluateUsers": ["string"],
  "assistDetail": {},
  "productForms": [
    {
      "productInfoList": [
        {
          "info": "string",
          "cusProdRespCode": "string",
          "remark": {
            "property1": "string",
            "property2": "string"
          }
        }
      ],
      "classification": ["string"],
      "productCode": "string",
      "productName": "string",
      "jurCodeList": ["string"],
      "subJurCodeList": ["string"],
      "insCode": "string"
    }
  ],
  "customerCommunicateContent": "string",
  "province": "string",
  "city": "string",
  "district": "string",
  "insLabel": "string"
}
```

机构拜访列表

### 属性

| 名称                       | 类型                                      | 必选  | 约束 | 中文名 | 说明                                                                               |
| -------------------------- | ----------------------------------------- | ----- | ---- | ------ | ---------------------------------------------------------------------------------- |
| createBy                   | integer(int64)                            | false | none |        | 创建者                                                                             |
| createTime                 | string(date-time)                         | false | none |        | 创建时间                                                                           |
| updateBy                   | integer(int64)                            | false | none |        | 更新者                                                                             |
| updateTime                 | string(date-time)                         | false | none |        | 更新时间                                                                           |
| params                     | object                                    | false | none |        | 请求参数                                                                           |
| » **additionalProperties** | object                                    | false | none |        | none                                                                               |
| tenantId                   | string                                    | false | none |        | 租户编号                                                                           |
| id                         | integer(int64)                            | false | none |        | 主键                                                                               |
| visitUserHrCode            | string                                    | false | none |        | 拜访用户工号（主数据）                                                             |
| visitDeptCode              | string                                    | false | none |        | 拜访用户部门编码（主数据）                                                         |
| visitDeptName              | string                                    | false | none |        | 拜访用户部门名称(主数据)                                                           |
| visitPostCode              | string                                    | false | none |        | 拜访用户岗位编码（主数据）                                                         |
| visitPostName              | string                                    | false | none |        | 岗位名称                                                                           |
| parentEmpCode              | string                                    | false | none |        | 拜访用户上级工号                                                                   |
| parentDeptCode             | string                                    | false | none |        | 拜访用户上级部门编码                                                               |
| parentDeptName             | string                                    | false | none |        | 拜访用户上级部门名称                                                               |
| parentPostCode             | string                                    | false | none |        | 拜访用户上级岗位编码                                                               |
| parentPostName             | string                                    | false | none |        | 拜访用户上级岗位名称                                                               |
| jurCode                    | string                                    | false | none |        | 辖区编码                                                                           |
| institutionCode            | string                                    | false | none |        | 机构代码                                                                           |
| institutionName            | string                                    | false | none |        | 机构名称                                                                           |
| insLat                     | string                                    | false | none |        | 机构纬度                                                                           |
| insLon                     | string                                    | false | none |        | 机构经度                                                                           |
| visitTime                  | string(date-time)                         | false | none |        | 拜访时间                                                                           |
| planVisitContent           | string                                    | false | none |        | 计划拜访内容                                                                       |
| communicateContent         | string                                    | false | none |        | 本次沟通内容                                                                       |
| completeTime               | string(date-time)                         | false | none |        | 计划拜访结束时间                                                                   |
| visitPurposeCode           | string                                    | false | none |        | 拜访目的                                                                           |
| visitTypeCode              | string                                    | false | none |        | 拜访类型                                                                           |
| cancelTime                 | string(date-time)                         | false | none |        | 取消时间                                                                           |
| planFlag                   | string                                    | false | none |        | 是否计划 1-是,0-否                                                                 |
| visitAssistFlag            | string                                    | false | none |        | 是否协访 1-是,0-否                                                                 |
| status                     | string                                    | false | none |        | 拜访状态（系统字典+业务字典） 0-待拜访,1-已拜访,2-正在拜访,3-取消拜访,4-过期未拜访 |
| assistRequirement          | string                                    | false | none |        | 协访需求                                                                           |
| visitSignId                | integer(int64)                            | false | none |        | 拜访签到签退表主键                                                                 |
| delFlag                    | string                                    | false | none |        | 是否已删除 2-是,0-否                                                               |
| extendInfo                 | string                                    | false | none |        | 拓展信息                                                                           |
| sex                        | string                                    | false | none |        | 性别 0-男,1-女                                                                     |
| insDeptName                | string                                    | false | none |        | 科室                                                                               |
| lastCommunicateContent     | string                                    | false | none |        | 上次沟通内容                                                                       |
| insAddress                 | string                                    | false | none |        | 机构地址                                                                           |
| visitUserName              | string                                    | false | none |        | 拜访人姓名                                                                         |
| avatar                     | string                                    | false | none |        | 头像                                                                               |
| visitStatusName            | string                                    | false | none |        | 拜访状态名称                                                                       |
| signInAddress              | string                                    | false | none |        | 签到地址                                                                           |
| signInTime                 | string(date-time)                         | false | none |        | 签到时间                                                                           |
| duration                   | string                                    | false | none |        | 停留时间                                                                           |
| signOutAddress             | string                                    | false | none |        | 签退地址                                                                           |
| signOutTime                | string(date-time)                         | false | none |        | 签退时间                                                                           |
| sysSignOutFlag             | string                                    | false | none |        | 是否系统签退 1-系统签退, 0-手工签退                                                |
| signId                     | integer(int64)                            | false | none |        | none                                                                               |
| signInDistance             | integer(int32)                            | false | none |        | 签到偏差距离                                                                       |
| signOutDistance            | integer(int32)                            | false | none |        | 签退偏差距离                                                                       |
| customerName               | string                                    | false | none |        | 客户姓名                                                                           |
| customerCode               | string                                    | false | none |        | none                                                                               |
| assistRemark               | string                                    | false | none |        | 协访已评价 0 未评价 1 已评价                                                       |
| signInPhotoList            | [integer]                                 | false | none |        | 签到照片 url 列表                                                                  |
| signOutPhotoList           | [integer]                                 | false | none |        | 签退照片 url 列表                                                                  |
| assistUserVOs              | [object]                                  | false | none |        | 用来在编辑客户页面使用                                                             |
| assistScoreVos             | [object]                                  | false | none |        | 协访评价                                                                           |
| assistEvaluateUsers        | [string]                                  | false | none |        | 协访评价方案                                                                       |
| assistDetail               | object                                    | false | none |        | none                                                                               |
| productForms               | [[VisitProductBo](#schemavisitproductbo)] | false | none |        | 产品推广信息                                                                       |
| customerCommunicateContent | string                                    | false | none |        | 本次沟通内容（客户拜访）                                                           |
| province                   | string                                    | false | none |        | none                                                                               |
| city                       | string                                    | false | none |        | none                                                                               |
| district                   | string                                    | false | none |        | none                                                                               |
| insLabel                   | string                                    | false | none |        | none                                                                               |

<h2 id="tocS_AssistBtnVo">AssistBtnVo</h2>

<a id="schemaassistbtnvo"></a>
<a id="schema_AssistBtnVo"></a>
<a id="tocSassistbtnvo"></a>
<a id="tocsassistbtnvo"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "visitInstitutionId": 0,
  "assistUserHrCode": "string",
  "assistUserHrName": "string",
  "assistDeptCode": "string",
  "assistDeptName": "string",
  "assistPostCode": "string",
  "assistPostName": "string",
  "status": "string",
  "assistSuggestInfo": "string",
  "confirmInviteFlag": "string",
  "visitSignId": 0,
  "delFlag": "string",
  "isShowEvaluateBtn": 0
}
```

### 属性

| 名称                       | 类型              | 必选  | 约束 | 中文名 | 说明                                 |
| -------------------------- | ----------------- | ----- | ---- | ------ | ------------------------------------ |
| createBy                   | integer(int64)    | false | none |        | 创建者                               |
| createTime                 | string(date-time) | false | none |        | 创建时间                             |
| updateBy                   | integer(int64)    | false | none |        | 更新者                               |
| updateTime                 | string(date-time) | false | none |        | 更新时间                             |
| params                     | object            | false | none |        | 请求参数                             |
| » **additionalProperties** | object            | false | none |        | none                                 |
| tenantId                   | string            | false | none |        | 租户编号                             |
| id                         | integer(int64)    | false | none |        | 主键                                 |
| visitInstitutionId         | integer(int64)    | false | none |        | 拜访机构表主键                       |
| assistUserHrCode           | string            | false | none |        | 协访用户工号（主数据）               |
| assistUserHrName           | string            | false | none |        | 协访人姓名                           |
| assistDeptCode             | string            | false | none |        | 协访用户部门编码（主数据）           |
| assistDeptName             | string            | false | none |        | 协访用户部门名称(主数据)             |
| assistPostCode             | string            | false | none |        | 协访用户岗位编码                     |
| assistPostName             | string            | false | none |        | 协访用户岗位名称                     |
| status                     | string            | false | none |        | 协访状态                             |
| assistSuggestInfo          | string            | false | none |        | 协访建议信息                         |
| confirmInviteFlag          | string            | false | none |        | 是否接受邀请,0-拒绝,1-接受,空-未操作 |
| visitSignId                | integer(int64)    | false | none |        | 拜访签到签退表主键                   |
| delFlag                    | string            | false | none |        | 是否已删除 (2-是 0-否)               |
| isShowEvaluateBtn          | integer(int32)    | false | none |        | 是否显示去评价按钮: 1-展示           |

<h2 id="tocS_AssistScoreVo">AssistScoreVo</h2>

<a id="schemaassistscorevo"></a>
<a id="schema_AssistScoreVo"></a>
<a id="tocSassistscorevo"></a>
<a id="tocsassistscorevo"></a>

```json
{
  "assistUser": "string",
  "assistSuggestInfo": "string",
  "total": "string",
  "isLoginUser": 0,
  "isCanEdit": 0,
  "scores": [
    {
      "scoreItemCode": "string",
      "scoreItemName": "string",
      "score": "string",
      "visitAssistId": 0
    }
  ]
}
```

协访评价

### 属性

| 名称              | 类型                                            | 必选  | 约束 | 中文名 | 说明                         |
| ----------------- | ----------------------------------------------- | ----- | ---- | ------ | ---------------------------- |
| assistUser        | string                                          | false | none |        | 协访人用户                   |
| assistSuggestInfo | string                                          | false | none |        | 协访建议                     |
| total             | string                                          | false | none |        | 统合评分                     |
| isLoginUser       | integer(int32)                                  | false | none |        | 该评价是否是当前登录人: 1-是 |
| isCanEdit         | integer(int32)                                  | false | none |        | 协访评价是否可编辑: 1-可编辑 |
| scores            | [[AssistScoreItemVo](#schemaassistscoreitemvo)] | false | none |        | 协访评分项                   |

<h2 id="tocS_AssistScoreItemVo">AssistScoreItemVo</h2>

<a id="schemaassistscoreitemvo"></a>
<a id="schema_AssistScoreItemVo"></a>
<a id="tocSassistscoreitemvo"></a>
<a id="tocsassistscoreitemvo"></a>

```json
{
  "scoreItemCode": "string",
  "scoreItemName": "string",
  "score": "string",
  "visitAssistId": 0
}
```

协访评价项

### 属性

| 名称          | 类型           | 必选  | 约束 | 中文名 | 说明                 |
| ------------- | -------------- | ----- | ---- | ------ | -------------------- |
| scoreItemCode | string         | false | none |        | 评分项编码(系统字典) |
| scoreItemName | string         | false | none |        | 评分项名称           |
| score         | string         | false | none |        | 评分分数             |
| visitAssistId | integer(int64) | false | none |        | none                 |

<h2 id="tocS_VisitAssistUserVo">VisitAssistUserVo</h2>

<a id="schemavisitassistuservo"></a>
<a id="schema_VisitAssistUserVo"></a>
<a id="tocSvisitassistuservo"></a>
<a id="tocsvisitassistuservo"></a>

```json
{
  "id": 0,
  "assistAvatar": "string",
  "status": "string",
  "assistSuggestInfo": "string",
  "confirmInviteFlag": "string",
  "scores": [
    {
      "assistUser": "string",
      "assistSuggestInfo": "string",
      "total": "string",
      "isLoginUser": 0,
      "isCanEdit": 0,
      "scores": [
        {
          "scoreItemCode": "string",
          "scoreItemName": "string",
          "score": "string",
          "visitAssistId": 0
        }
      ]
    }
  ],
  "empName": "string",
  "empCode": "string",
  "deptName": "string",
  "deptCode": "string",
  "postCode": "string",
  "postName": "string"
}
```

协访人

### 属性

| 名称              | 类型                                    | 必选  | 约束      | 中文名 | 说明                          |
| ----------------- | --------------------------------------- | ----- | --------- | ------ | ----------------------------- |
| id                | integer(int64)                          | false | none      |        | 协访人主键 id                 |
| assistAvatar      | string                                  | false | none      |        | 头像                          |
| status            | string                                  | false | none      |        | 协访状态（系统字典+业务字典） |
| assistSuggestInfo | string                                  | false | none      |        | 协访建议信息                  |
| confirmInviteFlag | string                                  | false | none      |        | 是否确认邀请[0,1]             |
| scores            | [[AssistScoreVo](#schemaassistscorevo)] | false | none      |        | 协访评价                      |
| empName           | string                                  | false | read-only |        | 协访人姓名                    |
| empCode           | string                                  | false | read-only |        | 协访用户工号（主数据）        |
| deptName          | string                                  | false | read-only |        | 部门名称                      |
| deptCode          | string                                  | false | read-only |        | 协访用户部门编码（主数据）    |
| postCode          | string                                  | false | read-only |        | 协访用户岗位编码（主数据）    |
| postName          | string                                  | false | read-only |        | 岗位名称                      |

<h2 id="tocS_VisitAssistBo">VisitAssistBo</h2>

<a id="schemavisitassistbo"></a>
<a id="schema_VisitAssistBo"></a>
<a id="tocSvisitassistbo"></a>
<a id="tocsvisitassistbo"></a>

```json
{
  "id": 0,
  "visitInstitutionId": 0,
  "empCode": "string",
  "empName": "string",
  "deptCode": "string",
  "deptName": "string",
  "postCode": "string",
  "postName": "string",
  "assistStatusCode": "string",
  "isConfirmInvite": "string",
  "confirmInviteTime": "2019-08-24T14:15:22Z",
  "visitSignId": 0,
  "assistUserHrCode": "string",
  "assistUserHrName": "string",
  "assistDeptCode": "string",
  "assistDeptName": "string",
  "assistPostCode": "string",
  "assistPostName": "string"
}
```

机构协访表单

### 属性

| 名称               | 类型              | 必选  | 约束       | 中文名 | 说明                          |
| ------------------ | ----------------- | ----- | ---------- | ------ | ----------------------------- |
| id                 | integer(int64)    | false | none       |        | 主键                          |
| visitInstitutionId | integer(int64)    | false | none       |        | 拜访机构表主键                |
| empCode            | string            | false | write-only |        | 协访用户工号（主数据）        |
| empName            | string            | false | write-only |        | none                          |
| deptCode           | string            | false | write-only |        | none                          |
| deptName           | string            | false | write-only |        | none                          |
| postCode           | string            | false | write-only |        | none                          |
| postName           | string            | false | write-only |        | none                          |
| assistStatusCode   | string            | false | none       |        | 协访状态（系统字典+业务字典） |
| isConfirmInvite    | string            | false | none       |        | 是否确认邀请[0,1]             |
| confirmInviteTime  | string(date-time) | false | none       |        | 确认邀请时间                  |
| visitSignId        | integer(int64)    | false | none       |        | 拜访签到签退表主键            |
| assistUserHrCode   | string            | false | none       |        | none                          |
| assistUserHrName   | string            | false | none       |        | none                          |
| assistDeptCode     | string            | false | none       |        | 协访部门编码                  |
| assistDeptName     | string            | false | none       |        | 协访部门名称                  |
| assistPostCode     | string            | false | none       |        | 协访用户岗位编码（主数据）    |
| assistPostName     | string            | false | none       |        | 协访用户岗位名称（主数据）    |

<h2 id="tocS_VisitContentBo">VisitContentBo</h2>

<a id="schemavisitcontentbo"></a>
<a id="schema_VisitContentBo"></a>
<a id="tocSvisitcontentbo"></a>
<a id="tocsvisitcontentbo"></a>

```json
{
  "visitInfoBo": {
    "visitInstitutionId": 0,
    "visitPurposeCode": "string",
    "communicateContent": "string",
    "assistRequirement": "string",
    "assistForm": [{}],
    "productForms": [
      {
        "productInfoList": [
          {
            "info": null,
            "cusProdRespCode": null,
            "remark": null
          }
        ],
        "classification": ["string"],
        "productCode": "string",
        "productName": "string",
        "jurCodeList": ["string"],
        "subJurCodeList": ["string"],
        "insCode": "string"
      }
    ],
    "extendInfo": "string"
  },
  "visitSignBo": {
    "id": 0,
    "signType": "string",
    "communicateContent": "string",
    "visitInstitutionId": 0,
    "signInTime": "2019-08-24T14:15:22Z",
    "signOutTime": "2019-08-24T14:15:22Z",
    "signInLon": "string",
    "signInLat": "string",
    "signOutLon": "string",
    "signOutLat": "string",
    "signInAddress": "string",
    "signOutAddress": "string",
    "signInRemark": "string",
    "signOutRemark": "string",
    "signInDistance": "string",
    "signOutDistance": "string",
    "isSysSignOut": 0,
    "signInPhotoList": [
      {
        "attUrl": "string",
        "ossId": 0
      }
    ],
    "signOutPhotoList": [
      {
        "attUrl": "string",
        "ossId": 0
      }
    ],
    "institutionCode": "string",
    "institutionName": "string",
    "customerCode": "string",
    "customerName": "string",
    "customerLevelCode": "string",
    "insDeptName": "string",
    "visitTime": "2019-08-24T14:15:22Z",
    "completeTime": "2019-08-24T14:15:22Z",
    "visitTypeCode": "string",
    "insLat": "string",
    "insLon": "string",
    "isPlan": 0,
    "jurCode": "string",
    "extendInfo": "string"
  }
}
```

### 属性

| 名称        | 类型                              | 必选  | 约束 | 中文名 | 说明        |
| ----------- | --------------------------------- | ----- | ---- | ------ | ----------- |
| visitInfoBo | [VisitInfoBo](#schemavisitinfobo) | false | none |        | 拜访信息    |
| visitSignBo | [VisitSignBo](#schemavisitsignbo) | false | none |        | 签到/退表单 |

<h2 id="tocS_RMapStringString">RMapStringString</h2>

<a id="schemarmapstringstring"></a>
<a id="schema_RMapStringString"></a>
<a id="tocSrmapstringstring"></a>
<a id="tocsrmapstringstring"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "property1": "string",
    "property2": "string"
  }
}
```

响应信息主体

### 属性

| 名称                       | 类型           | 必选  | 约束 | 中文名 | 说明       |
| -------------------------- | -------------- | ----- | ---- | ------ | ---------- |
| code                       | integer(int32) | false | none |        | 消息状态码 |
| msg                        | string         | false | none |        | 消息内容   |
| data                       | object         | false | none |        | 数据对象   |
| » **additionalProperties** | string         | false | none |        | none       |
