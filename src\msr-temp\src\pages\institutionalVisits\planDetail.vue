<template>
  <view class="planDetail">
    <DetailTop
      :status="state.info.status"
      :visitTime="state.info.visitTime"
      :completeTime="state.info.completeTime"
      :planFlag="state.planFlag"
      :planFlagTime="state.info.planFlag"
    />
    <SignTime
      :visitUserName="state.info.visitUserName"
      :status="state.info.status"
      :visitTime="state.info.visitTime"
    />
    <view class="visit-address">
      <view style="display: flex; align-items: center">
        <view>
          <text style="font-size: 20px; font-weight: 500">{{
            state.info.customerName
          }}</text>
          <img
            v-if="state.info.sex === '1' || state.info.sex === '0'"
            :src="
              state.info.sex === '1'
                ? vector
                : state.info.sex === '0'
                ? man
                : ''
            "
            alt=""
            class="sexIcon"
          />
          <!-- <nut-tag
          color="#E8F0FF"
          text-color="#2551F2"
          v-if="state.info.planFlag === '1'"
          style="vertical-align: text-bottom"
          >计划拜访
        </nut-tag>
        <nut-tag
          color="#FFF3E8"
          text-color="#F77234"
          v-if="state.info.planFlag === '0'"
          style="vertical-align: text-bottom"
          >临时拜访
        </nut-tag> -->
        </view>
        <view class="tag-s" v-if="state.info.planFlag === '1'">计划拜访 </view>
        <view class="tag-f" v-if="state.info.planFlag === '0'">临时拜访 </view>
      </view>
      <view style="font-size: 14px; color: #869199; margin-top: 10px">
        <view class="m_b">
          <img :src="cus_ins" alt="" class="address" />
          <text>{{ state.info.institutionName }}</text>
          <nut-divider direction="vertical" />
          <text>{{ state.info.insDeptName }}</text>
          <nut-divider direction="vertical" />
          <text>{{
            state.info.visitTypeCode === "0" ? "院内拜访" : "院外拜访"
          }}</text>
        </view>
        <view class="m_b">
          <img :src="adress" alt="" class="address" />{{
            state.info.insAddress
          }}
        </view>
      </view>
      <view class="photograph" v-if="state.info.status === '0'">
        <view style="padding: 16px 0">拍照</view>
        <view class="photo-list">
          <view
            class="photo"
            v-for="(photo, index) in photoList"
            :key="photo.ossId"
          >
            <img
              :src="photo.url"
              alt=""
              style="width: 80px; height: 80px"
              @click="lookImage(index)"
            />
            <view class="del" @tap="delPhoto(index)">X</view>
          </view>
          <view @tap="takePhoto()" class="photo">
            <img :src="uploaderImg" alt="" style="width: 80px; height: 80px" />
          </view>
        </view>
      </view>
    </view>
    <SignInOut
      type="signIn"
      :signStatus="state.status"
      @refresh-location="refreshLocation"
      @sign="signIn"
      :signAddress="state.params.signInAddress"
      :distance="state.params.signInDistance"
      :institutionName="state.info.institutionName"
      v-if="state.info.status === '0' && query.activeIndex == 0"
      :insLonNoFlag="state.insLonNoFlag"
      @calibration="calibration"
    />
    <view style="padding: 20px" v-if="query.activeIndex == 0">
      <nut-button
        block
        type="primary"
        shape="square"
        color=""
        plain
        style="background: #edeff5; color: #4e595e"
        v-if="state.info.status === '0'"
        @click="cancelPlan"
        >取消拜访</nut-button
      >
    </view>
    <view style="width: 80px; height: 80px; overflow: hidden">
      <nut-image-preview
        :show="state.showPreview"
        :images="state.imgData"
        :init-no="initNo"
        @close="state.showPreview = false"
        style="width: 100%; height: 100%"
      />
    </view>
    <CalibrationDialog ref="calibrationDialogRef"  @selectLocation="selectLocation"/>
  </view>
</template>

<script setup>
import { onMounted, reactive, ref, onUnmounted, onDeactivated } from "vue";
import Taro, { useDidShow, useRouter, useLoad, useDidHide } from "@tarojs/taro";
import SignTime from "./sign/components/signTime.vue";
import CellClick from "../../pages/components/cellClick/index.vue";
import SignInOut from "./sign/components/signInOut";
import {
  validIsNotSignOut,
  visitDistance,
  sign,
  institutionalVisitsDetail,
  institutioCancel,
  crmSinInM,
} from "../../api/institutionalVisitsApi";
import { currentTime } from "../../utils/content.js";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import vector from "../../images/woman.png";
import man from "../../images/man.png";
import cus_ins from "../../images/hospital_v3.png";
import adress from "../../images/location.png";
import { apiAuth } from "../../utils/feishuAuth";

import { Photograph } from "@nutui/icons-vue-taro";
import uploaderImg from "../../images/uploader.png";
import DetailTop from "./components/detailTop";
import { baseApi } from "../../utils/content";
import CalibrationDialog from "../components/calibrationDialog.vue"
const router = useRouter();
const query = router.params;
const initUserInfo = Taro.getStorageSync("initUserInfo").user;
const TokenKey = Taro.getStorageSync("access_token");
const initNo = ref(0);
const calibrationDialogRef = ref(null)

const state = reactive({
  id: null,
  status: "", // 0 定位失败 1定位成功
  params: {
    isPlan: "1",
    signType: "0",
    signInLat: "",
    signInLon: "",
    signInDistance: 0,
    signInAddress: "",
  },
  info: {},
  footerButtontext: "取消拜访",
  insLonNoFlag: false,
  planFlag: false,
  showPreview: false,
  imgData: [],
  crmSinInMInfo: {},
  calibrationData: {}
});

const photoList = ref([]);

const delPhoto = (index) => {
  photoList.value.splice(index, 1);
  state.imgData = photoList.value.map((item) => {
    return {
      src: item.url,
    };
  });
};

const cancelPlan = () => {
  Taro.showModal({
    content: "是否取消该计划",
    success: async function (res) {
      if (res.confirm) {
        const res = await institutioCancel({ id: state.id });
        if (res.code === 200) {
          Taro.reLaunch({
            url: "/pages/index/index",
          });
          Taro.showToast({
            title: "已取消",
            icon: "none",
            duration: 2000,
          });
          Taro.reLaunch({
            url: "/pages/institutionalVisits/index",
          });
        }
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};

const refreshLocation = () => {
  getLocation("refre");
};
const lookImage = (i) => {
  initNo.value = i;
  state.showPreview = true;
};
const getDistance = (type) => {
  if (state.info.insLon && state.info.insLon != "0.0") {
    let ins = "";
    if (state.info.insLon) {
      ins = `${state.info.insLon},${state.info.insLat}`;
    } else {
      ins = "";
    }
    const params = {
      origins: `${state.params.signInLon},${state.params.signInLat}`,
      destination: ins,
    };
    visitDistance(params)
      .then((res) => {
        if (res.code === 200) {
          state.params.signInDistance = !!(res.data && res.data[0])
            ? res.data[0]
            : 0;
          state.status = "1";
          if (type) {
            Taro.showToast({
              title: "刷新成功",
              icon: "none",
              duration: 2000,
            });
          }
        } else {
          state.status = "1";
          Taro.showToast({
            title: "获取机构位置与当前位置距离失败",
            icon: "none",
            duration: 2000,
          });
        }
      })
      .catch((res) => {
        state.status = "1";
        Taro.showToast({
          title: "获取机构位置与当前位置距离失败",
          icon: "none",
          duration: 2000,
        });
      });
  } else {
    state.status = "1";
    state.params.signInDistance = 0;
    state.insLonNoFlag = true;
  }
};
const getConvert = (longitude, latitude) =>
  new Promise((res) => {
    console.log("转换前的经纬度", longitude, latitude);
    Taro.request({
      url: `https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${longitude},${latitude}&key=0446b950e065d1e0b98a9e97f08dfbff&coordsys=gps`,
      success: (result_before) => {
        console.log(result_before, "-!result_before");
        console.log("转换后的经度", result_before.data.locations);
        const [lon, lat] = result_before.data.locations.split(",");
        res({ lon, lat });
        console.log("转换后的经度params", lon, lat);
      },
    });
  });
const getLocation = (type = "", isSign = true) => {
  if ("geolocation" in navigator) {
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const latitude = position.coords.latitude; // 纬度
        const longitude = position.coords.longitude; // 经度
        const { lon, lat } = await getConvert(longitude, latitude);

        Taro.request({
          url: `https://restapi.amap.com/v3/geocode/regeo?location=${lon},${lat}&key=0446b950e065d1e0b98a9e97f08dfbff`,
          success: (result) => {
            state.params.signInAddress =
              result.data.regeocode.formatted_address;
            state.params.signInLat = latitude;
            state.params.signInLon = longitude;
            if (isSign) getDistance(type);
          },
          fail: function (res) {
            Taro.showToast({
              title: "定位失败",
              icon: "none",
              duration: 2000,
            });
            state.status = "0";
          },
        });
      },
      function (error) {
        Taro.showToast({
          title: "定位失败",
          icon: "none",
          duration: 2000,
        });
        state.status = "0";
      },
      {}
    );
  } else {
    Taro.showToast({
      title: "请开启定位权限",
      icon: "none",
      duration: 2000,
    });
  }
};

const signIn = async () => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  })
  if (
    state.crmSinInMInfo.remark === "1" &&
    Number(state.params.signInDistance) >
      Number(state.crmSinInMInfo.configValue)
  ) {
    Taro.hideLoading();
    return Taro.showModal({
      content: state.crmSinInMInfo.configName,
      confirmText: "确定",
      duration: 2000,
      showCancel: false,
    });
  }
  const parmas = {
     visitUserHrCode: initUserInfo.userName,
    visitLevel: '1'
   };
  const { data } = await validIsNotSignOut(parmas);
  if (data) {
    Taro.hideLoading();
    return Taro.showModal({
      content: `您有正进行的拜访，请先结束拜访`,
      confirmText: "去结束",
      success: async function (res) {
        if (res.confirm) {
          Taro.navigateTo({
            url: `/pages/institutionalVisits/visiting?id=${data}`,
          });
        } else if (res.cancel) {
          console.log("用户点击取消");
        }
      },
    });
  }
  const infoParams = {
    customerCode: state.info.customerCode,
    customerName: state.info.customerName,
    institutionCode: state.info.institutionCode,
    institutionName: state.info.institutionName,
    insLat: state.info.insLat,
    insLon: state.info.insLon,
    visitInstitutionId: state.info.id,
  };
  const signInPhotoList = photoList.value.map((item) => {
    return {
      attUrl: item.originalUrl,
      ossId: item.ossId,
    };
  });
  const params = {
    ...state.params,
    ...infoParams,
    signInPhotoList,
  };
  const res = await sign(params);
  if (res.code === 200) {
    Taro.showToast({
      title: "签到成功",
      icon: "none",
      duration: 2000,
    });
    Taro.redirectTo({
      url: `/pages/institutionalVisits/visiting?id=${state.id}`,
    });
 
  } else {
    Taro.hideLoading();
    Taro.showToast({
      title: res.msg,
      icon: "none",
    });
  }

};
const getDetail = async () => {
  try {
    const params = {
      visitInstitutionId: query.id,
    };
    const res = await institutionalVisitsDetail(params);
    state.info = res?.data;
    const newDate = currentTime();
    const date1 = dayjs(state.info.visitTime).format("YYYY-MM-DD");
    const date2 = dayjs(newDate).format("YYYY-MM-DD");

    if (
    date1 === date2 &&
    query.activeIndex == 0 &&
    state.info.status === "0"
    ) {
      getLocation();
    }
    if (date1 !== date2 && state.info.status === "0") {
    state.planFlag = true;
    }
  } catch (e) {
    // do sth
    console.log(e);
  }
};
const chooseImage = () =>
  new Promise((res, rej) => {
    try {
      tt.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (data) => {
          console.log(data, "--data");
          const fileSystemManager = tt.getFileSystemManager();
          fileSystemManager.readFile({
            filePath: data.tempFilePaths?.[0],
            encoding: "base64",
            success(res_v1) {
              const imageType = "image/png";
              const base64ImageURL = `data:${imageType};base64,${res_v1?.data}`;
              res({ url: base64ImageURL, type: "FS" });
            },
            fail(res) {
              console.log(`readFile fail: ${JSON.stringify(res)}`);
            },
          });
        },
        fail: (err) => rej(err),
      });
    } catch (err) {
      console.log("!ttt");
      Taro.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (data) => {
          console.log(data, "---dddd");
          res({ url: data?.tempFilePaths?.[0], type: "H5" });
        },
        fail: (err) => rej(err),
      });
    }
  });
const addWatermark = (imageSrc, type = "") => {
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      canvas.width = image.width;
      canvas.height = image.height;
      ctx.drawImage(image, 0, 0);
      ctx.font = type == "FS" ? "30px Arial" : "80px Arial";
      ctx.fillStyle = "white";
      ctx.fillText(dayTime().Y, canvas.width - (type == "FS" ? 200 : 500), 200);
      ctx.fillText(dayTime().H, canvas.width - (type == "FS" ? 200 : 500), 300);
      try {
        console.log(initUserInfo, "--initUserInfo");
        console.log(initUserInfo?.nickName, initUserInfo?.userName);
        console.log(dayTime().Y, "-y");
        console.log(dayTime().H, "-h");

        console.log(state.params.signInAddress, "-state.params.signAddress");
        if (initUserInfo?.nickName && initUserInfo?.userName)
          ctx.fillText(
            initUserInfo.nickName + "-" + initUserInfo.userName,
            20,
            canvas.height - 300
          );
        if (state.params?.signInAddress)
          ctx.fillText(state.params.signInAddress, 20, canvas.height - 200);
      } catch (err) {}
      // const base64Image = canvas.toDataURL("image/jpeg");
      // resolve(dataURLtoFileUrl(base64Image));
      canvas.toBlob(
        function (newBlob) {
          resolve(URL.createObjectURL(newBlob));
        },
        "image/jpeg",
        0.5
      );
    };
    image.onerror = reject;
    image.src = imageSrc;
  });
};
const dataURLToBlob = (dataURL) => {
  // 将base64数据转换为二进制字符串
  var base64 = dataURL.split(",")[1];
  var mimeString = dataURL.split(",")[0].split(":")[1].split(";")[0];
  var binaryString = atob(base64);
  var len = binaryString.length;
  var bytes = new Uint8Array(len);
  for (var i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // 创建Blob对象
  return new Blob([bytes], { type: mimeString });
};
const dataURLtoFileUrl = (dataURL) => {
  // 创建一个Blob对象
  var blob = dataURLToBlob(dataURL);
  return URL.createObjectURL(blob);
};
const takePhoto = async () => {
  const { url, type } = await chooseImage();

  Taro.showLoading({
    mask: true,
    title: "上传中",
  });

  addWatermark(url, type).then((files) => {
    console.log(files, "--addWatermark!!!");

    Taro.uploadFile({
      url: process.env.TARO_APP_API + `${baseApi}oss-file/upload`,
      header: {
        clientid: process.env.TARO_APP_CLIENT_ID,
        Authorization: `Bearer ${TokenKey}`,
      },
      filePath: files,
      name: "file",
      fail: () => {
        Taro.hideLoading();

        console.log("上传失败");
      },
      success: (result) => {
        Taro.hideLoading();

        try {
          console.log("上传成功：", JSON.parse(result.data).data);
          photoList.value.push(JSON.parse(result.data).data);
          state.imgData = photoList.value.map((item) => {
            return {
              src: item.url,
            };
          });
        } catch (e) {
          console.log(e);
        }
      },
    });
  });
};

const dayTime = () => {
  const now = new Date();

  // 格式化日期和时间
  const year = now.getFullYear(); // 年
  const month = now.getMonth() + 1; // 月，getMonth() 返回的月份是从 0 开始的
  const day = now.getDate(); // 日
  const hours = now.getHours(); // 时
  const minutes = now.getMinutes(); // 分
  const seconds = now.getSeconds(); // 秒

  // 将月、日、时、分、秒转换为两位数格式
  const formattedMonth = month < 10 ? `0${month}` : month;
  const formattedDay = day < 10 ? `0${day}` : day;
  const formattedHours = hours < 10 ? `0${hours}` : hours;
  const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;

  // 组合成 "年-月-日 时：分：秒" 格式的字符串
  return {
    Y: `${year}/${formattedMonth}/${formattedDay}`,
    H: `${formattedHours}:${formattedMinutes}`,
  };
};
useLoad(() => {
  Taro.setNavigationBarTitle({ title: "拜访签到" });
  state.id = query.id;
  getDetail();
});
let timer = null;

const getCrmSinInM = async () => {
  const res = await crmSinInM();
  state.crmSinInMInfo = res.rows[0] || {};
};
const calibration = () => {
  calibrationDialogRef.value.open(state.info)
}

const selectLocation = (location) => {
  console.log("定位校准",location)
  state.insLonNoFlag = false;
  state.info.insLat = location.location.lat.toString();
  state.info.insLon = location.location.lng.toString();
  getDistance();
}
useDidShow(() => {
  getLocation("", false);
  apiAuth();
  getCrmSinInM();

  // timer = setTimeout(() => {
  //   getLocation("", false);
  // }, 500);

  // const photos = Taro.getStorageSync("photos");
  // if (photos) {
  // photoList.value = photos;
  state.imgData = photoList.value.map((item) => {
    return {
      src: item.url,
    };
  });
  // }
});
</script>

<style lang="scss">
.tag-f {
  margin-left: 4px;
  color: #f77234;
  background: #fff3e8;
  min-width: 48px;

  font-size: 12px;

  font-family: PingFang SC;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
  height: 20px;
  line-height: 20px;
}
.tag-s {
  margin-left: 4px;
  color: #2551f2;
  background: #e8f0ff;
  min-width: 48px;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
  height: 20px;
  line-height: 20px;
}
.planDetail {
  color: #1d212b;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC-Regular;

  .sign-select {
    margin: 12px;
    padding: 16px;
    margin-top: 8px;
    background: #fff;
    border-radius: 8px;
  }
  .nut-button--plain.nut-button--primary {
    border-color: #e5e6eb;
  }
  .visit-address {
    margin: 12px;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    .sexIcon {
      display: inline-block;
      margin-right: 5px;
      width: 18px;
      height: 18px;
      vertical-align: text-top;
    }
    .address {
      display: inline-block;
      width: 18px;
      height: 18px;
      vertical-align: middle;
      margin-right: 8px;
    }
    .m_b {
      // margin-bottom: 8px;
    }

    .photograph {
      //display: flex;
      //justify-content: space-between;
      .photo-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .photo {
          width: 80px;
          height: 80px;
          position: relative;
          overflow: hidden;
          .del {
            position: absolute;
            font-size: 12px;
            right: -12px;
            top: -10px;
            width: 24px;
            height: 24px;
            background: black;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            color: #fff;
            padding-top: 7px;
            padding-right: 7px;
          }
          img {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
