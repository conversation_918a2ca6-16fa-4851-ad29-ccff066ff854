<template>
  <div class="">
    <div class="flex justify-between items-center bg-white">
      <van-search
        v-model="value"
        show-action
        action-text="搜索"
        placeholder="请输入搜索关键词"
        @search="onSearch"
        @cancel="onCancel"
        style="width: calc(100% - 55px);"
      />
      <div class="w-[36px] h-[36px] text-center mx-[8px] rounded-[50%] bg-[#F3F4F5] leading-[36px] text-center">
        <van-icon name="filter-o" />
      </div>
    </div>
    <div>
      <van-tabs v-model:active="active" @change="statusSelect">
        <van-tab title="待办" name="1">
          <TaskList :list="state.taskList" @onLoad="statusSelect" :total="state.queryParams.total" :active="active"/>
        </van-tab>
        <van-tab title="已办" name="2">
          <TaskList :list="state.taskList" @onLoad="statusSelect" :total="state.queryParams.total" :active="active"  />
        </van-tab>
        <van-tab title="抄送我" name="3">
          <TaskList :list="state.taskList" @onLoad="statusSelect" :total="state.queryParams.total" :active="active"  />
        </van-tab>
        <van-tab title="已发送" name="4">
          <TaskList :list="state.taskList" @onLoad="statusSelect" :total="state.queryParams.total" :active="active"  />
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { showToast, showLoadingToast, closeToast } from 'vant';

import TaskList from './components/taskList.vue'
import { todoTaskApi, doneListApi, ccTaskListApi, taskApplyListApi } from '@/api/bpm'

const value = ref('');
const active = ref('1');
const onSearch = async (val) => {
  state.queryParams.pageNum = 1;
  state.queryParams.pageSize = 20;
  await statusSelect(active.value);
};
const onCancel = async () => {
  state.queryParams.pageNum = 1;
  state.queryParams.pageSize = 20;
  await statusSelect(active.value);
};

const state = reactive({
  queryParams: {
    name: '',
    pageSize: 10,
    pageNum: 1
  },
  taskList: []
})

// 选择审批状态
const statusSelect = async (e, isLoadMore) => {
  try {
    let api = () => {};
    if(e === '1') {
      api = todoTaskApi
    }else if(e === '2') {
      api = doneListApi
    }else if(e === '3') {
      api = ccTaskListApi
    }else if(e === '4') {
      api = taskApplyListApi
    }
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0
    });
    if(isLoadMore) {
      state.queryParams.pageSize = state.queryParams.pageSize + 10
    }
    const res = await api({
      pageSize: state.queryParams.pageSize,
      pageNum: state.queryParams.pageNum,
      processName: value.value
    });
    state.taskList = res.rows || [];
    state.queryParams.total = res.total || 0
  } catch (e) {

  } finally {
    closeToast()
  }
}

onMounted(() => {
  statusSelect('1')
})
</script>
<style lang="less">
.van-search {
  border: 1px solid;
  border-radius: 80px;
  padding: 2px 16px;
  margin-top: 6px;
  margin-left: 16px;
}
.van-search__content{
  background: #fff;
}
.van-search__action {
  color: #2551F2;
}
</style>