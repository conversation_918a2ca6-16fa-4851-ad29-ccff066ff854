<template>
  <view class="detail-top">
    <view
      :class="getBacColor(props.assistStatus).className"
      style="padding: 16px 16px 16px 32px"
    >
      <view style="display: flex; align-items: center">
        <img
          v-if="getBacColor(props.assistStatus).imgSrc"
          class="await-img"
          :src="getBacColor(props.assistStatus).imgSrc"
        />
        <view style="font-size: 18px; font-weight: 500">
          <text> {{ getAssisStatusName(props.assistStatus) }}</text>
        </view>
      </view>

      <view
        style="font-size: 12px; margin-top: 4px"
        :class="getTimeColor(props.assistStatus)"
        v-if="getTime() || props.activeIndex === '1'"
      >
        <text style="margin-right: 5px">{{ getTime() }}</text>
        <text v-if="props.activeIndex === '1'"
          >代表：{{ props.assistUserHrName }}</text
        >
      </view>
    </view>
  </view>
</template>
<script setup>
import { reactive, defineProps } from "vue";
import awaitBlue from "../../../images/awaitBlue.png";
import awaitGreen from "../../../images/awaitGreen.png";
import awaitYellow from "../../../images/awaitYellow.png";
import {
  getAssisStatusName,
  getColor,
  currentTime,
} from "../../../utils/content.js";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
const props = defineProps({
  status: {
    type: String,
    default: "",
  },
  visitTime: {
    type: String,
    default: "",
  },
  completeTime: {
    type: String,
    default: "",
  },
  signInTime: {
    type: String,
    default: "",
  },
  signOutTime: {
    type: String,
    default: "",
  },
  assistStatus: {
    type: String,
    default: "",
  },
  planFlagTime: {
    type: String,
    default: "",
  },
  assistUserHrName: {
    type: String,
    default: "",
  },
  activeIndex: {
    type: String,
    default: "",
  },
});
const state = reactive({});
const getTime = () => {
  if (props.planFlagTime === "1") {
    const time1 = dayjs(props.visitTime).format("YYYY/MM/DD");
    const time2 = dayjs(props.visitTime).format("HH:mm");
    const time3 = dayjs(props.completeTime).format("HH:mm");
    return `${time1} (${time2}-${time3})`;
  }
};
const getBacColor = (code) => {
  if (code === "5") {
    return { className: "color-1", imgSrc: awaitGreen };
  } else if (code === "0") {
    return { className: "color-0", imgSrc: awaitBlue };
  } else {
    return { className: "color-3", imgSrc: false };
  }
};

const getTimeColor = (code) => {
  if (code === "3" || code === "4") {
    return "color-time";
  }
};
</script>
<style lang="scss">
.await-img {
  width: 24px;
  height: 24px;
  margin-right: 4px;
}
.detail-top {
  // background: linear-gradient(180deg, #d2e7ff 0%, #edeff5 52%, #edeff5 100%);

  .color-0 {
    color: #2551f2;
    // background: linear-gradient(180deg, #d2e7ff 0%, #edeff5 100%);
  }

  .color-1 {
    color: #00b578;
    // background: linear-gradient(180deg, #ccf6e8 0%, #edeff5 100%);
  }

  .color-2 {
    color: #ffb637;
    // background: linear-gradient(180deg, #ffefc6 0%, #edeff5 100%);
  }
  .color-time {
    color: #869199;
  }
}
</style>
