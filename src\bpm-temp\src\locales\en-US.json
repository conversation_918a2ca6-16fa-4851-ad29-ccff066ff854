{"menus": {"home": "Home", "profile": "My Center", "darkMode": "🌗 Dark Mode", "mockGuide": "💿 Mock Guide", "language": "📚 Language", "404Demo": "🙅 Page 404 Demo", "echartsDemo": "📊 Echarts Demo", "persistPiniaState": "🍍 Persistent Pinia state", "unocssExample": "🎨 Unocss example", "keepAlive": "🧡 KeepAlive <PERSON>mo", "login": "🔒 Login", "register": "Register", "settings": "Settings", "basicSettings": "Basic Settings", "exampleComponents": "Example components", "forgot-password": "Forgot Password"}, "mock": {"fromAsyncData": "Data from asynchronous requests", "noData": "No data", "pull": "<PERSON><PERSON>", "reset": "Reset"}, "charts": {"January": "Jan", "February": "Feb", "March": "Mar", "April": "Apr", "May": "May", "June": "Jun"}, "layouts": {"home": "HOME", "profile": "PROFILE"}, "profile": {"login": "<PERSON><PERSON>", "settings": "Settings"}, "keepAlive": {"label": "The current component will be cached"}, "login": {"login": "Sign In", "logout": "Sign Out", "email": "Email", "password": "Password", "pleaseEnterEmail": "Please enter email", "pleaseEnterPassword": "Please enter password", "sign-up": "Click to sign up", "forgot-password": "Forgot password?"}, "forgot-password": {"email": "Email", "code": "Code", "password": "Password", "comfirmPassword": "Password again", "pleaseEnterEmail": "Please enter email", "pleaseEnterCode": "Please enter code", "pleaseEnterPassword": "Please enter password", "pleaseEnterConfirmPassword": "Please enter password again", "passwordsDoNotMatch": "Passwords do not match", "confirm": "Confirm", "backToLogin": "Back to login", "getCode": "Get code", "gettingCode": "Getting code", "sendCodeSuccess": "Sent, the code is", "passwordResetSuccess": "Password reset succeeded"}, "register": {"email": "Email", "code": "Code", "nickname": "Nickname", "password": "Password", "comfirmPassword": "Password again", "pleaseEnterEmail": "Please enter email", "pleaseEnterCode": "Please enter code", "pleaseEnterNickname": "Please enter nickname", "pleaseEnterPassword": "Please enter password", "pleaseEnterConfirmPassword": "Please enter password again", "passwordsDoNotMatch": "Passwords do not match", "confirm": "Confirm", "backToLogin": "Back to login", "getCode": "Get code", "gettingCode": "Getting code", "sendCodeSuccess": "Sent, the code is", "registerSuccess": "Register succeeded"}, "settings": {"logout": "Sign Out", "currentVersion": "Current Version", "comfirmTitle": "Confirm Exit?"}}