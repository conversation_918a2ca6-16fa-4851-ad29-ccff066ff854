<template>
  <view class="area-approve">
    <view class="top-filter">
      <view class="flex justify-between">
        <view class="overflow-x-auto">
          <view class="flex gap-[8px] flex-nowrap" style="width: 115%">
            <view
              class="px-[20px] h-[30px] leading-[30px] text-[#4E595E] text-[14px] bg-white rounded-[88px] font-400"
              :style="
                item.key === state.params.bpmType
                  ? {
                      border: '1px solid #92A8F8',
                      color: '#2551F2',
                      fontWeight: 500,
                    }
                  : { border: '1px solid #fff' }
              "
              v-for="item in state.bpmTypes"
              :key="item.key"
              @tap="handleFilter(item.key)"
              >{{ item.text }}</view
            >
          </view>
        </view>
        <view class="min-w-[22px] text-center text-center pl-[16px]">
          <img
            :src="filterIcon"
            class="w-[20px] h-[28px]"
            @click="showFilter = true"
          />
        </view>
      </view>
    </view>
    <scroll-view
      style="height: 528px"
      :scroll-y="true"
      :scroll-top="state.scrollTop"
      @scrolltolower="onScrollToLower"
    >
      <view
        style="
          position: absolute;
          top: 28%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!state.insList.length && !scrollMyFlag"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>
      <view class="px-[10px]">
        <view
          v-for="item in state.insList"
          :key="item.processId"
          class="relative rounded-[8px] bg-white p-[12px] mb-[8px]"
          @tap="handleToApproveDetail(item)"
        >
          <view class="absolute top-0 right-0">
            <ProcessStatusTag :instanceState="item.instanceState" />
          </view>
          <view class="header flex gap-[4px] flex-wrap items-center">
            <view class="name text-[18px] text-[#1D212B] pt-[6px]">
              {{
                getFormData(item.formData)?.[
                  getExtConfig(item.flowProcess.extConfig)?.mainTitle?.field
                ]
              }}
            </view>
            <view
              class="px-[6px] text-[12px] text-[#2551F2] h-[20px] leading-[20px]"
              style="border: 1px solid #2551f2; font-size: 12px"
              >{{ item.processName }}</view
            >
          </view>
          <view
            class="text-[#869199] leading-[22px]"
            style="font-size: 13px"
            v-if="getExtConfig(item.flowProcess.extConfig)?.oneSubtitle"
          >
            {{
              getExtConfig(item.flowProcess.extConfig)?.oneSubtitle?.title
            }}：{{
              getFormData(item.formData)[
                getExtConfig(item.flowProcess.extConfig)?.oneSubtitle?.field
              ]
            }}
          </view>
          <view
            class="text-[#869199] leading-[22px]"
            style="font-size: 13px"
            v-if="getExtConfig(item.flowProcess.extConfig)?.twoSubtitle"
          >
            {{
              getExtConfig(item.flowProcess.extConfig)?.twoSubtitle?.title
            }}：{{
              getFormData(item.formData)[
                getExtConfig(item.flowProcess.extConfig)?.twoSubtitle?.field
              ]
            }}
          </view>
          <view
            class="text-[#869199] leading-[22px]"
            style="font-size: 13px"
            v-if="getExtConfig(item.flowProcess.extConfig)?.threeSubtitle"
          >
            {{
              getExtConfig(item.flowProcess.extConfig)?.threeSubtitle?.title
            }}：{{
              getFormData(item.formData)[
                getExtConfig(item.flowProcess.extConfig)?.threeSubtitle?.field
              ]
            }}
          </view>
          <view class="text-[#869199] leading-[22px]" style="font-size: 13px">
            申请单号：{{ item.instanceId }}
          </view>
          <view class="flex justify-between items-center mt-[10px]">
            <view class="flex items-center">
              <view
                class="w-[25px] h-[25px] rounded-[50%] bg-[#92A8F8] text-center leading-[25px] text-[12px] text-white"
              >
                {{ item?.startUser?.name?.slice(-1) }}
              </view>
              <view class="text-[14px] text-[#1D212B] pl-[8px]">
                申请人：{{ item?.startUser?.name }}
              </view>
            </view>
            <view class="flex">
              <view class="text-[#869199] text-[14px]">{{
                item.startTime
              }}</view>
            </view>
          </view>
        </view>
      </view>
      <view
        v-if="state.insList.length > 0 && state.insList.length == state.total"
        style="color: #869199; font-size: 16px; text-align: center"
        >已全部加载完毕</view
      >
      <PopupRadio ref="popupRadioRef" @radio-confirm="radioConfirm" />
    </scroll-view>

    <nut-popup v-model:visible="showFilter" position="bottom" round>
      <view>
        <view class="max-h-[456px] min-h-[200px] overflow-y-auto p-[16px]">
          <view class="grid grid-cols-2 gap-[12px]">
            <view
              :class="
                state.params.sorter === '1'
                  ? checkedFilterBtnClass
                  : defaultFilterBtnClass
              "
              @tap="sorterFilter('1')"
              >最新到达优先</view
            >
            <view
              :class="
                state.params.sorter === '2'
                  ? checkedFilterBtnClass
                  : defaultFilterBtnClass
              "
              @tap="sorterFilter('2')"
              >最久等待优先</view
            >
          </view>
          <view
            class="text-[13px] text-[#869199] h-[18px] leading-[18px] pt-[16px] pb-[12px]"
            >审批类型</view
          >
          <view class="grid grid-cols-2 gap-[12px]">
            <view
              :class="
                item.processName === state.params.processName
                  ? checkedFilterBtnClass
                  : defaultFilterBtnClass
              "
              @tap="handleProcessFilter(item)"
              :key="item.id"
              v-for="item in processList"
              >{{ item.processName }}</view
            >
          </view>
        </view>
        <view class="flex justify-between px-[16px] py-[8px] gap-[16px]">
          <view
            class="w-[50%] h-[44px] leading-[44px] text-center text-[#2551F2] text-[15px] bg-[#E8F0FF] rounded-[2px]"
            @tap="resetSearch()"
            >重置</view
          >
          <view
            class="w-[50%] h-[44px] leading-[44px] text-center text-[#fff] text-[15px] bg-[#2551F2] rounded-[2px]"
            @tap="search()"
            >确定</view
          >
        </view>
      </view>
    </nut-popup>
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { defineEmits, ref, reactive, defineExpose } from "vue";
import filterIcon from "./../../assets/images/filter.svg";
import {
  todoTaskApi,
  doneListApi,
  ccTaskListApi,
  taskApplyListApi,
  processListApi,
} from "./../../api/bpm.js";
import ProcessStatusTag from "./../approve_msr/processStatusTag.vue";

import { IconFont } from "@nutui/icons-vue-taro";
import PopupRadio from "./../../components/popupRadio/popupRadio.vue";
import noData from "./../../assets/images/no-data.png";
const router = useRouter();
const query = router.params;
const defaultFilterBtnClass =
  "h-[36px] leading-[36px] text-center text-[#4E595E] text-[14px] bg-[#F3F4F5] text-[#4E595E] truncate";
const checkedFilterBtnClass = `!text-[#2551F2] !bg-[#E8F0FF] ${defaultFilterBtnClass}`;

const props = defineProps({
  processType: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["change-filter", "processFilter"]);
const scrollMyFlag = ref(false);
const initHrInfo = Taro.getStorageSync("initHrInfo") || {};
const applyFormTypeList = [];
const popupRadioRef = ref(null);
const showFilter = ref(false);

const state = reactive({
  scrollTop: 0,
  search: [],
  params: {
    pageNum: 1,
    pageSize: 20,
    applyType: "",
    status: "1",
    bpmType: "1",
    insName: "",
    sorter: "",
    processName: "",
    processType: props.processType,
  },
  total: 0,
  pcdvalue: "",
  insList: [],
  list: [
    { text: "待审批", key: "1", active: false },
    { text: "已审批", key: "2", active: false },
    { text: "我发起", key: "3", active: false },
  ],
  bpmTypes: [
    { text: "待办", key: "1" },
    { text: "已办", key: "2" },
    { text: "已发送", key: "4" },
    { text: "抄送我", key: "3" },
  ],
  show1: false,
  statusList: [
    {
      dictLabel: "待办",
      dictValue: "1",
    },
    {
      dictLabel: "已办",
      dictValue: "2",
    },
    {
      dictLabel: "抄送我",
      dictValue: "3",
    },
    {
      dictLabel: "已发送",
      dictValue: "4",
    },
  ],
  statusType: [
    {
      dictLabel: "关联药店（普通）",
      // dictValue: 'gs_speaker_mdm_apply',
      dictValue: "关联药店（普通）",
    },

    {
      dictLabel: "关联药店（特批）",
      // dictValue: 'gs_speaker_update_mdm_apply',
      dictValue: "关联药店（特批）",
    },
  ],
  topType: "",
});

const processList = ref([]);

const sorterFilter = (sorter) => {
  state.params.sorter = sorter;
};

const handleProcessFilter = (process) => {
  state.params.processName = process.processName;
};

const getFormData = (str) => {
  if (!str) return {};
  try {
    return JSON.parse(str);
  } catch (e) {
    return {};
  }
};

const getExtConfig = (str) => {
  if (!str) return {};
  try {
    return JSON.parse(str).keyWords;
  } catch (e) {
    return {};
  }
};

const handleFilter = async (bpmType) => {
  state.params.bpmType = bpmType;
  state.params.status = bpmType;
  state.params.pageSize = 20;
  state.params.pageNum = 1;
  state.insList = [];
  await getList();
};

const getList = () => {
  scrollMyFlag.value = true;
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  let api = () => {};
  console.log("state.params.statu", state.params.status);
  if (state.params.status === "1") {
    api = todoTaskApi;
  } else if (state.params.status === "2") {
    api = doneListApi;
  } else if (state.params.status === "3") {
    api = ccTaskListApi;
  } else if (state.params.status === "4") {
    api = taskApplyListApi;
  }

  api({
    pageNum: state.params.pageNum,
    pageSize: state.params.pageSize,
    processName: state.params.processName,
    processType: state.params.processType,
    keyWord: state.params.keyWord,
    processType: props.processType,
  })
    .then((res) => {
      if (res.code == 200) {
        state.total = res.total;
        if (!state.insList.length) {
          console.log("res.data.rows", res.rows);
          state.insList = res.rows || [];
        } else {
          state.insList = [...state.insList, ...res.rows];
        }
        Taro.hideLoading();
        scrollMyFlag.value = false;
      } else {
        Taro.hideLoading();
      }
    })
    .catch((e) => {
      scrollMyFlag.value = false;
      Taro.hideLoading();
      console.log(e, "eee");
    });
};

const search = () => {
  getList();
  state.params.pageNum = 1;
  state.params.pageSize = 20;
  state.insList = [];
  showFilter.value = false;
};

const resetSearch = () => {
  state.params.sorter = "";
  state.params.processName = "";
};

const topSearch = (search) => {
  state.params.keyWord = search;
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getList();
};

const approveFilters = () => {
  const info = {
    startDate: state.params.startDate,
    endDate: state.params.endDate,
  };
  Taro.navigateTo({
    url: `/pages/pharmacy/approve/approveFiltes?info=${JSON.stringify(info)}`,
  });
};
const onScrollToLower = () => {
  if (!scrollMyFlag.value && state.insList.length < state.total) {
    state.params.pageNum++;
    getList();
  }
};

const statusText = (i) => {
  if (!i) return;
  const list =
    state.statusList.filter((item) => item.dictValue === i.status) || [];
  console;
  return list.length ? list[0].dictLabel : "";
};
const gotoInst = (item) => {
  if (item.status === "-1") {
    if (item.rejectType === "1") {
      window.location.href = `${process.env.TARO_APP_APPROVE_URL}/ae/automation/inbox?usingInstanceId=${item.aeFlowId}&hideAction=false&hideForm=false&hideProcess=false&hideTitle=false`;
    } else {
      return;
    }
  } else {
    const list =
      state.statusList.filter((v) => v.dictValue === item.status) || [];
    if (list[0].jump === "true") {
      return (window.location.href = `${process.env.TARO_APP_APPROVE_URL}/ae/automation/inbox?usingInstanceId=${item.aeFlowId}&hideAction=false&hideForm=false&hideProcess=false&hideTitle=false`);
    } else {
      return false;
    }
  }
};

const approveStatus = () => {
  state.topType = "status";
  popupRadioRef.value.open(state.statusList, state.params.status);
};
const approveType = () => {
  state.topType = "type";
  popupRadioRef.value.open(state.statusType, state.params.applyType);
};
const radioConfirm = (val) => {
  if (state.topType === "type") {
    state.params.applyType = val;
    // state.params.status = "";
    // const list = state.statusType.filter(
    //   (item) => item.dictValue === state.params.applyType
    // )[0].remark;
    // state.statusList = JSON.parse(list).data;
  } else {
    state.params.status = val;
  }
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getList();
};

const getApproveType = () => {
  const list = state.statusType.filter(
    (item) => item.dictValue === state.params.applyType
  );
  return (list[0] && list[0].dictLabel) || "";
};

const getApproveTypeItem = (type) => {
  const list = state.statusType.filter((item) => item.dictValue === type);
  return (list[0] && list[0].dictLabel) || "";
};
const getApproveStatus = () => {
  if (!state.params.status && state.statusList.length) return;
  const list = state.statusList.filter(
    (item) => item.dictValue === state.params.status
  );
  return (list[0] && list[0].dictLabel) || "";
};

const approveDetail = (item) => {
  if (item.status === "-1") {
    if (item.rejectType === "1") {
      return true;
    } else {
      return false;
    }
  } else {
    const list =
      state.statusList.filter((v) => v.dictValue === item.status) || [];
    if (list[0].jump === "true") {
      return true;
    } else {
      return false;
    }
  }
};

const initList = (status) => {
  console.log("status", status);
  if (status) {
    state.params.status = query.status;
    state.params.bpmType = query.status;
  }
  state.total = 0;
  state.params.pageNum = 1;
  state.insList = [];
  getProcessList();
  getList();
};

const statusColor = (i) => {
  if (!i) return;
  const list =
    state.statusList.filter((item) => item.dictValue === i.status) || [];
  return list.length ? list[0].color : "";
};

const statusBgcolor = (i) => {
  if (!i) return;
  const list =
    state.statusList.filter((item) => item.dictValue === i.status) || [];
  return list.length ? list[0].bgcColor : "";
};

const handleToApproveDetail = (item) => {
  if (item.processType === "bpm_ins_apply") {
    Taro.navigateTo({
      url: `/pages/ins_approve/index?processId=${item.processId}&instanceId=${item.instanceId}`,
    });
  } else {
    Taro.navigateTo({
      url: `/bpm-temp/src/pages/approve_detail/index?processId=${item.processId}&instanceId=${item.instanceId}`,
    });
  }
};

const getProcessList = async () => {
  const res = await processListApi({
    pageSize: 10,
    pageNum: 1,
    processState: 1,
    processType: props.processType,
  });

  processList.value = res.rows || [];
};

defineExpose({
  topSearch,
  approveFilters,
  initList,
});

// useDidShow(() => {
//   setTimeout(() => {

//     initList();
//   }, 1000);
// });
</script>
<style lang="scss">
.area-approve {
  padding: 0px 0px;
  width: 100vw;
  position: relative;
  // height: 630px;
  font-family: PingFang SC, PingFang SC;

  .top-filter {
    padding: 8px 16px;
    display: flex;
    align-items: center;

    .filter-item {
      height: 27px;
      padding: 0px 9px;
      border-radius: 16px;
      background: #fff;
      font-size: 14px;
      line-height: 27px;
      margin-right: 8px;
      display: inline-block;

      color: #4e595e;
      border: 1px solid #fff;

      .title {
        margin-right: 4px;
      }
    }
  }

  .content {
    background-color: #ffffff;
    margin-bottom: 10px;
    border-radius: 8px;
    margin-left: 12px;
    margin-right: 12px;
    position: relative;

    .content-item {
      padding: 16px 16px 12px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item-top {
        font-size: 18px;
        margin-bottom: -5px;
        font-weight: 500;
      }
    }

    .item-time {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #869199;
    }

    .item-type {
      font-size: 14px;
      color: #869199;
    }
  }

  .approve {
    color: #ff7d00;
    background: #fff3e8;
  }

  .agree {
    color: #00b578;
    background: #e6f8f2;
  }

  .reject {
    color: #f32f29;
    background: #feeaea;
  }

  .back {
    color: #4e595e;
    background: #f3f4f5;
  }

  .approvalStatus {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 12px;
    text-align: center;
    height: 20px;
    padding: 0 7px;
    line-height: 20px;
    border-radius: 2px 8px 0 10px;
  }

  .nut-divider.nut-divider-vertical {
    top: 0;
  }

  .add-pharmacy {
    position: absolute;
    right: 16px;
    top: 53%;
  }

  .nut-overlay {
    position: absolute !important;
  }

  .nut-popup {
    position: absolute !important;
  }

  .popupRadio .footer-button {
    padding-bottom: 20px;
  }
}
</style>
