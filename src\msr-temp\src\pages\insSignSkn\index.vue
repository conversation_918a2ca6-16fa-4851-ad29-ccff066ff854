<template>
  <view class="insSign">
    <nav-bar @click-back="clickBack" navBarStyle="padding-top:10px">
      <template #left> <div></div></template>
      <template #titleContent>
        <nav-title
          :activeIndex="state.activeIndex"
          leftText="签到"
          rightText="签到"
          @setActiveIndex="setActiveIndex"
        ></nav-title>
      </template>
    </nav-bar>

    <VisitCalendar
      @chage-time="chageTime"
      :visitTime="state.visitTime"
      :visitEndTime="state.visitEndTime"
      :selectFlag="state.activeIndex"
      @sub-user-select="subUsereSelect"
      :subEmpCode="state.subParams.subEmpCode"
    />

    <view style="background: #edeff5">
      <institution-list
        :institutionList="state.institutionList"
        @scrolltolower="scrolltolower"
        :activeIndex="state.activeIndex"
        :total="state.total"
      ></institution-list>
    </view>

    <view class="footerFiexd">
      <view class="drag" @click="dragClick" v-if="state.activeIndex === 0">
        <img :src="plus" alt="" class="add" />
      </view>
    </view>
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import NavBar from "../../pages/components/navBar/index.vue";

import { reactive, watch } from "vue";
import navTitle from "../../pages/components/navTitle/navTitle.vue";
import institutionList from "./components/institutionList.vue";
import { insSignList } from "../../api/insSign";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import plus from "../../images/plus.png";
import VisitCalendar from "./components/visitCalendar";

const initHrInfo = Taro.getStorageSync("initHrInfo");
const router = useRouter();
const state = reactive({
  activeIndex: 0,
  visitTime: dayjs().format("YYYY-MM-DD"),
  pageNum: 1,
  pageSize: 10,
  institutionList: [],
  showFooterPopupVisible: false,
  showPopupVisible: false,
  visitEndTime: dayjs().format("YYYY-MM-DD"),
  dateList: [
    { color: "#b3b3b3", date: [] },
    { color: "#fff", type: "dot", date: [] },
  ],
  status: [],
  selectFlag: false,
  // 下属
  subParams: {
    subEmpCode: initHrInfo.subUserCodeList,
    subVisit: "1",
  },
  scrollFlag: false,
  total: 0,
});
const clickBack = () => {
  Taro.reLaunch({
    url: "/pages/index/index",
  });
};
//切换标题
const setActiveIndex = (v) => {
  if (v === state.activeIndex) return;
  state.pageNum = 1;
  state.institutionList = [];
  if (v === 1) {
    state.selectFlag = true;
  } else {
    state.selectFlag = false;
  }
  state.activeIndex = v;
  getinList();
};

// 下属筛选出来的代表
const subUsereSelect = (list) => {
  if (!list.length) {
    state.subParams.subEmpCode = initHrInfo.subUserCodeList;
  } else {
    const arr = list.map((item) => item.empCode);
    state.subParams.subEmpCode = arr;
  }
  state.total = 0;
  state.pageNum = 1;
  state.institutionList = [];
  getinList();
};
const chageTime = (start, end) => {
  state.visitTime = start;
  state.visitEndTime = end;
  state.pageNum = 1;
  state.total = 0;
  state.institutionList = [];
  getinList();
};

// 底部弹出层
const dragClick = () => {
  Taro.navigateTo({ url: "/pages/insSignSkn/sign/signIn" });
};

const getinList = async () => {
  state.scrollFlag = true;
  let parmas = {
    visitTime: state.visitTime,
    pageNum: state.pageNum,
    pageSize: state.pageSize,
    status: state.status.join(","),
    subVisit: "0",
    visitEndTime: state.visitEndTime,
    visitEndTime: state.visitEndTime,
    visitLevel: '0'
  };
  //下属参数
  if (state.activeIndex == 1) {
    parmas.subEmpCode = state.subParams.subEmpCode.join(",");
    parmas.subVisit = "1";
  }
  try {
    Taro.showLoading({
      title: "加载中",
      icon: "loading",
    });
  } catch (err) {}
  const res = await insSignList(parmas);
  Taro.hideLoading();
  if (!state.institutionList.length) {
    state.institutionList = res.data.rows;
  } else {
    state.institutionList = [...state.institutionList, ...res.data.rows];
  }

  state.total = res.data.total;
  state.scrollFlag = false;
};

// 滚动加载
const scrolltolower = () => {
  if (!state.scrollFlag && state.institutionList.length < state.total) {
    state.pageNum++;
    getinList();
  }
};

watch(
  () => {
    state.status;
  },
  () => {
    state.institutionList = [];
  }
);
useDidShow(() => {
  Taro.setNavigationBarTitle({ title: "机构签到" });
  state.pageNum = 1;
  state.total = 0;
  state.institutionList = [];
  getinList();
});
</script>
<style lang="scss" scoped>
.insSign {
  font-size: 16px;
  padding-top: 60px;
  .footerFiexd {
    margin-left: 80%;
    position: fixed;
    bottom: 80px;
    right: 20px;
  }
  .drag {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    background-color: #597dff;
    box-shadow: 0px 4px 4px 0px rgba(23, 49, 229, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    .add {
      display: inline-block;
      width: 24px;
      height: 24px;
    }
  }
  .nut-navbar__title {
    margin-left: 66px;
  }
}
</style>
