// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    NutButton: typeof import('@nutui/nutui-taro')['Button']
    NutCalendar: typeof import('@nutui/nutui-taro')['Calendar']
    NutCascader: typeof import('@nutui/nutui-taro')['Cascader']
    NutCell: typeof import('@nutui/nutui-taro')['Cell']
    NutCheckbox: typeof import('@nutui/nutui-taro')['Checkbox']
    NutCheckboxGroup: typeof import('@nutui/nutui-taro')['CheckboxGroup']
    NutCol: typeof import('@nutui/nutui-taro')['Col']
    NutDatePicker: typeof import('@nutui/nutui-taro')['DatePicker']
    NutDivider: typeof import('@nutui/nutui-taro')['Divider']
    NutEllipsis: typeof import('@nutui/nutui-taro')['Ellipsis']
    NutForm: typeof import('@nutui/nutui-taro')['Form']
    NutFormItem: typeof import('@nutui/nutui-taro')['FormItem']
    NutImagePreview: typeof import('@nutui/nutui-taro')['ImagePreview']
    NutInput: typeof import('@nutui/nutui-taro')['Input']
    NutNavbar: typeof import('@nutui/nutui-taro')['Navbar']
    NutPicker: typeof import('@nutui/nutui-taro')['Picker']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
    NutRadio: typeof import('@nutui/nutui-taro')['Radio']
    NutRadioGroup: typeof import('@nutui/nutui-taro')['RadioGroup']
    NutRate: typeof import('@nutui/nutui-taro')['Rate']
    NutRow: typeof import('@nutui/nutui-taro')['Row']
    NutSearchbar: typeof import('@nutui/nutui-taro')['Searchbar']
    NutSwitch: typeof import('@nutui/nutui-taro')['Switch']
    NutTabPane: typeof import('@nutui/nutui-taro')['TabPane']
    NutTabs: typeof import('@nutui/nutui-taro')['Tabs']
    NutTag: typeof import('@nutui/nutui-taro')['Tag']
    NutTextarea: typeof import('@nutui/nutui-taro')['Textarea']
    NutUploader: typeof import('@nutui/nutui-taro')['Uploader']
  }
}
