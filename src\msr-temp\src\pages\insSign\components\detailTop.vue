<template>
  <view class="detail-top">
    <view
      :class="getBacColor(props.status).className"
      style="padding: 16px 16px 16px 32px"
    >
      <view style="display: flex; align-items: center">
        <img
          v-if="getBacColor(props.status).imgSrc"
          class="await-img"
          :src="getBacColor(props.status).imgSrc"
        />
        <view style="font-size: 18px; font-weight: 500">
          <text> {{ getVisitStatusName(props.status) }}</text>
          <text style="font-size: 14px" v-if="props.planFlag"
            >(未到拜访当天)</text
          >
        </view>
      </view>
      <view style="font-size: 12px; margin-top: 4px" class="color-time">{{
        getTime()
      }}</view>
    </view>
  </view>
</template>
<script setup>
import { reactive, defineProps } from "vue";
import awaitBlue from "../../../images/awaitBlue.png";

import awaitGreen from "../../../images/awaitGreen.png";
import awaitYellow from "../../../images/awaitYellow.png";
import {
  getVisitStatusName,
  getColor,
  currentTime,
} from "../../../utils/content.js";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
const props = defineProps({
  status: {
    type: String,
    default: "",
  },
  visitTime: {
    type: String,
    default: "",
  },
  completeTime: {
    type: String,
    default: "",
  },
  planFlag: {
    type: Boolean,
    default: false,
  },
  planFlagTime: {
    type: String,
    default: "",
  },
});
const state = reactive({});

const getTime = () => {
  if (props.planFlagTime === "1") {
    const time1 = dayjs(props.visitTime).format("YYYY/MM/DD");
    const time2 = dayjs(props.visitTime).format("HH:mm");
    const time3 = dayjs(props.completeTime).format("HH:mm");
    return `${time1} (${time2}-${time3})`;
  }
};
const getBacColor = (code) => {
  if (code === "0") {
    return {
      className: "color-0",
      imgSrc: awaitBlue,
    };
  } else if (code === "1") {
    return { className: "color-1", imgSrc: awaitGreen };
  } else if (code === "2") {
    return { className: "color-2", imgSrc: awaitYellow };
  } else {
    return { className: "color-3", imgSrc: false };
  }
};

const getTimeColor = (code) => {
  if (code === "3" || code === "4") {
    return "color-time";
  }
};
</script>
<style lang="scss">
.await-img {
  width: 24px;
  height: 24px;
  margin-right: 4px;
}
.detail-top {
  // background: linear-gradient(180deg, #d2e7ff 0%, #edeff5 52%, #edeff5 100%);

  .color-0 {
    color: #2551f2;
    // background: linear-gradient(180deg, #d2e7ff 0%, #edeff5 100%);
  }

  .color-1 {
    color: #00b578;
    // background: linear-gradient(180deg, #ccf6e8 0%, #edeff5 100%);
  }

  .color-2 {
    color: #ffb637;
    // background: linear-gradient(180deg, #ffefc6 0%, #edeff5 100%);
  }
  .color-time {
    color: #869199;
  }
}
</style>
