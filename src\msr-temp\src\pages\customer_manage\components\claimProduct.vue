<template>
  <view>
    <view class="claimProduct">
      <CellClick
        @selectClick="selectClick('name')"
        :value="state.params.name"
        label="代表姓名"
        placeholder="请选择"
        required
      />
      <CellClick
        @selectClick="selectClick('insName')"
        :value="state.params.insName"
        label="机构名称"
        placeholder="请选择"
        required
      />
      <CellClick
        v-if="router.params.currentTab === '1'"
        @selectClick="selectClick('customerName')"
        :value="state.params.customerName"
        label="客户名称"
        placeholder="请选择"
        required
      />
      <CellClick
        @selectClick="selectClick('productName')"
        :value="state.params.productName"
        label="产品名称"
        placeholder="请选择"
        required
      />
    </view>

    <FooterButtonTwo
      rightText="提交"
      @click-left="clickButtonLeft"
      @click-right="clickButtonRight"
      :disabledRight="state.submitlag"
      leftText="取消"
      :plainFlag="false"
      leftColor=""
      leftTextColor="#4E595E"
    />
    <UserPopup ref="userPopupRef" @select-user="selectUser" />
    <SelectIns ref="selectInsRef" @ins-confirm="insConfirm" />
    <CustomerPopup ref="customerPopupRef" @select-cus="selectCustomer" />
    <AddProduct ref="addProductRef" @select-pro="selectPro" />
  </view>
</template>
<script setup>
import { onMounted, reactive, ref } from "vue";
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import CellClick from "../../../pages/components/cellClick/index.vue";
import FooterButtonTwo from "../../../pages/components/footerButtonTwo/footerButtonTwo";
import SelectIns from "../components/selectIns.vue";
import UserPopup from "./userPopup.vue";
import CustomerPopup from "./customerPopup.vue";
import AddProduct from "./addProduct";
import { productQuery, approveInitiate,checkUnique } from "../../../api/area";
import { appCode, tenantId } from "../../../utils/content";
const initHrInfo = Taro.getStorageSync("initHrInfo");
const selectInsRef = ref(null);
const userPopupRef = ref(null);
const addProductRef = ref(null);
const customerPopupRef = ref(null);
const router = useRouter();
const query = router.params;
const state = reactive({
  type: "",
  params: {
    name: "",
    insName: "",
    insCode: "",
    customerCode: "",
    customerName: "",
    jurCode: "",
    postCode: "",
    products: [],
    productName: "",
    ancestors: "",
    deptCode: "",
    deptName: "",
    postCode: "",
    insDeptName: "",
  },
  submitlag: false,
});

const selectClick = (type) => {
  state.type = type;
  if (type === "name") {
    userPopupRef.value.open(state.params.postCode);
  }

  if (type === "customerName") {
    if (!state.params.insCode) {
      return Taro.showToast({
        title: "请先选择机构",
        icon: "none",
        duration: 2000,
      });
    }
    customerPopupRef.value.open(
      state.params.insCode,
      state.params.customerCode,
      state.params.jurCode
    );
  }

  if (type === "insName") {
    if (!state.params.name) {
      return Taro.showToast({
        title: "请先选择代表",
        icon: "none",
        duration: 2000,
      });
    }
    const type = router.params.currentTab === "1" ? "2" : "1";
    console.log("22222", state.params.insCode, state.params.jurCode, type);
    selectInsRef.value.open(
      "add",
      state.params.insCode,
      state.params.jurCode,
      type
    );
  }

  if (type === "productName") {
    // 0 是机构
    let params = {};
    if (query.currentTab === "0") {
      if (!state.params.jurCode || !state.params.insCode) {
        return Taro.showToast({
          title: "请先选择代表和机构",
          icon: "none",
          duration: 2000,
        });
      }
      params = {
        type: "ins",
        jurCode: state.params.jurCode,
        insCode: state.params.insCode,
      };
    } else {
      if (
        !state.params.jurCode ||
        !state.params.insCode ||
        !state.params.customerName
      ) {
        return Taro.showToast({
          title: "请先选择代表和机构和客户",
          icon: "none",
          duration: 2000,
        });
      }
      params = {
        type: "cust",
        jurCode: state.params.jurCode,
        customerCode: state.params.customerCode,
        insCode: state.params.insCode,
      };
    }

    const checked = state.params.products.map((item) => item.code);
    addProductRef.value.open("all", params, checked);
  }
};
const insConfirm = (ins) => {
  state.params.insName = ins.value;
  state.params.insCode = ins.code;
  state.params.customerCode = "";
  state.params.customerName = "";
  state.params.insDeptName = "";
  state.params.products = [];
  state.params.productName = "";
};

const selectUser = (item) => {
  if (!item.user.length) {
    state.params.name = item.deptName;
  } else {
    state.params.name = item.user.map((item) => item.value).join(",");
  }
  state.params.jurCode = item.jurCode;
  state.params.ancestors = item.ancestors;
  state.params.deptName = item.deptName;
  state.params.deptCode = item.deptCode;
  state.params.postCode = item.postCode;
  state.params.postName = item.postName;
  state.params.mgr = item.user;
  state.params.insName = "";
  state.params.insCode = "";
  state.params.customerCode = "";
  state.params.customerName = "";
  state.params.insDeptName = "";
  state.params.products = [];
  state.params.productName = "";
};

const selectCustomer = (cus) => {
  state.params.customerCode = cus.customerCode;
  state.params.customerName = cus.customerName;
  state.params.insDeptName = cus.insDeptName;
};

const selectPro = (arr) => {
  state.params.products = arr;
  state.params.productName = arr.map((item) => item.name).join(",");
};

const clickButtonLeft = () => {
  Taro.navigateBack({
    delta: 1,
  });
};
const getCheckUnique = async (data) => {
  const params = {
    customerName: data.customerName,
    insName: data.insName,
    jurCode: data.jurCode,
    jurCustomerCode: data.customerCode,
    jurInsCode: data.insCode,
    productList:  data.applyContent.products
  }
  const res = await checkUnique(params)
  if(res.data) {
    state.submitlag = false;
    const obj = res.data
    const productName = obj.productList.map(item => item.name).join('；')
    const text1 = `【${obj.insName} ${obj.insMdmCode} ${obj.customerName} ${productName}】已被他人认领，请联系助理或运营确认，是否可调整客户架构关系`
    Taro.showModal({
      content: text1,
      confirmText: "确定",
      duration: 2000,
      showCancel: false,
    });
     return true

   } else {
    return false
  }


}
const clickButtonRight = async () => {
  if (!state.params.name) {
    return Taro.showToast({
      title: "请先选择代表",
      icon: "none",
      duration: 2000,
    });
  }
  if (!state.params.insName) {
    return Taro.showToast({
      title: "请先选择机构",
      icon: "none",
      duration: 2000,
    });
  }
  if (!state.params.customerName && router.params.currentTab === "1") {
    return Taro.showToast({
      title: "请先选择客户",
      icon: "none",
      duration: 2000,
    });
  }
  if (!state.params.productName) {
    return Taro.showToast({
      title: "请先选择产品",
      icon: "none",
      duration: 2000,
    });
  }

 
  state.params.applicantCode = initHrInfo.empCode;
  state.params.applicant = initHrInfo.empName;
  state.params.postIdList = initHrInfo.postIdList;
  state.params.type = router.params.currentTab === "1" ? "cust" : "ins";
  state.submitlag = true;
  const applicantInfo = {
    applicant: state.params.applicant,
    applicantCode: state.params.applicantCode,
    postCode: state.params.postCode,
    postName: state.params.postName,
    deptCode: state.params.deptCode,
    deptName: state.params.deptName,
    ancestors: state.params.ancestors,
    postIdList: state.params.postIdList,
  };
  const params = {
    applyContent: {
      ...state.params,
    },
    enableWorkflow: true,
    insCode: state.params.insCode,
    insName: state.params.insName,
    customerCode: state.params.customerCode,
    customerName: state.params.customerName,
    appCode: appCode(),
    tenantId: tenantId,
    applyType: "6", //目前只有客户认领
    jurCode: state.params.jurCode,
    ...applicantInfo,
  };

  const key = Taro.getStorageSync('jurCustomerUnique')
    if(key === '1') {
      if(await getCheckUnique(params)) return 
    }
  const res = await approveInitiate(params);
  if (res.code === 200) {
    Taro.reLaunch({
      url: `/pages/area/index?currentTab=${query.currentTab || 0}`,
    });
    Taro.showToast({
      title: "添加成功",
      icon: "none",
      duration: 2000,
    });

    state.submitlag = false;
  } else {
    state.submitlag = false;
  }
};

onMounted(() => {
  if (query.currentTab === "0") {
    Taro.setNavigationBarTitle({ title: "机构认领申请" });
  } else {
    Taro.setNavigationBarTitle({ title: "客户认领申请" });
  }
});
</script>
<style lang="scss">
.claimProduct {
  padding: 16px;
  background: #fff;
}
</style>
