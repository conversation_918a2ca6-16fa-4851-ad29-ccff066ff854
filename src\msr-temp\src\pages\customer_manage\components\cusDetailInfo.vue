<template>
  <view class="cusDetailInfo">
    <view class="info-title">
      <view  style="font-size: 16px; color: #1d212b"> 月门诊量</view>
        <view class="num" style="display: flex; align-items: center" @click="props.handleNumShow">
          <view style="font-size: 16px; color: #2551f2;">{{state.info.monthlyOutpatient}}</view>
          <view style="margin-left: 4px; color: #2551f2;">  <IconFont name="arrow-right" size="12px" style="vertical-align: middle;"></IconFont></view>
        </view>
    </view>
    <view class="product-table">
      <view class="item-table-title" style="background: #f3f4f5; border-radius: 8px 8px 0 0">
        <view class="label">产品</view>
        <view class="value" style="color: #869199">产品等级</view>
      </view>
      <view class="item-table">
        <view class="item-table-item" v-for="(item, index) in state.info.customerProductLevelList" :key="index" :style="{
          'border-bottom':
            index == state.info.customerProductLevelList.length - 1
              ? ''
              : '1px solid #e5e6eb',
        }">
          <view class="label">{{ item.name }}</view>
          <view class="value">{{ item.value }}</view>
        </view>
      </view>
    </view>
    <view style="font-size: 12px; color: #F32F29;margin-top: 4px;">*此为当前等级，更新数值需要审核通过后，展示修改后等级</view>
  </view>
</template>
<script setup>
import { View } from "@tarojs/components";
import { reactive } from "vue";
import { getDetailProduct } from "../../../utils/area.js";
import { IconFont } from '@nutui/icons-vue-taro'
const  props = defineProps({
  handleNumShow: {
    type: Object,
    default: () =>{}
  }
})
const state = reactive({
  info: {},
 
});

const setInfo = (info) => {
  state.info = info;
};

defineExpose({
  setInfo,
});
</script>
<style lang="scss">
.cusTabDetail::-webkit-scrollbar {
  /* 隐藏 WebKit 内核（如 Chrome、Safari）的滚动条 */
  display: none;
}

.cusDetailInfo {
  font-size: 14px;
  color: #869199;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  overflow-y: scroll;
.info-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .num {
           
            background: #e8f0ff;
            border-radius: 4px;
            height: 30px;
            line-height: 30px;
            padding: 0 8px;
          }
}
  .product-table {
    margin-top: 10px;

    .item-table {
      border: 1px solid #e5e6eb;
      border-radius: 0 0 8px 8px;

      .item-table-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        // border-bottom: 1px solid #e5e6eb;

        .label {
          padding-left: 16px;
          width: 70%;
          color: #121d29;
          font-size: 12px;
          text-align: left;
        }

        .value {
          width: 30%;
          color: #2551f2;
          font-size: 14px;
          text-align: center;
        }
      }
    }

    .item-table-title {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border: 1px solid #e5e6eb;
      border-bottom: none;

      .label {
        padding-left: 16px;
        width: 70%;
        color: #869199;
        font-size: 14px;
        text-align: left;
      }

      .value {
        width: 30%;
        color: #869199;
        font-size: 14px;
        text-align: center;
      }
    }

  }

}
</style>
