<template>
  <view class="collaborativevisit">
    <nav-bar @click-back="clickBack" navBarStyle="padding-top:10px">
      <template #left> <div></div></template>
      <template #titleContent>
        <nav-title
          :activeIndex="state.activeIndex"
          leftText="协访"
          rightText="协访"
          @setActiveIndex="setActiveIndex"
        ></nav-title>
      </template>
    </nav-bar>
    <VisitCalendar
      @chage-time="chageTime"
      :visitTime="state.visitTime"
      :visitEndTime="state.visitEndTime"
    />
    <!-- 日历 -->

    <!-- 头部筛选  -->
    <TopSearch
      :list="state.topSearchList"
      @activeItem="activeItem"
      :selectFlag="state.selectFlag"
      @sub-user-select="subUsereSelect"
      :subEmpCode="state.subParams.subEmpCode"
      :scrollFlag="state.scrollFlag"
    />

    <view>
      <AssistListPage
        :assistData="state.assistData"
        @scrolltolower="scrolltolower"
        :activeIndex="state.activeIndex"
        :total="state.total"
      ></AssistListPage>
    </view>
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import NavBar from "../../pages/components/navBar/index.vue";
import { onMounted, reactive, watch } from "vue";
import navTitle from "../../pages/components/navTitle/navTitle.vue";
import calendar from "../../pages/components/calendar/index.vue";
import AssistListPage from "./components/assistList.vue";
import VisitCalendar from "../institutionalVisits/components/visitCalendar";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import plus from "../../images/plus.png";
import {
  assistList,
  subordinateList,
  assistCalendarList,
} from "../../api/collaborativevisit.js";
import TopSearch from "./components/topSearch";

const initHrInfo = Taro.getStorageSync("initHrInfo");
const router = useRouter();
const state = reactive({
  activeIndex: 0,
  visitTime: dayjs().format("YYYY-MM-DD"),
  pageNum: 1,
  pageSize: 10,
  assistData: [],
  showFooterPopupVisible: false,
  showPopupVisible: false,
  dateList: [
    { color: "#b3b3b3", date: [] },
    { color: "#fff", type: "dot", date: [] },
  ],
  status: [],
  selectFlag: false,
  // 下属
  subParams: {
    subEmpCode: initHrInfo.subUserCodeList,
    subVisit: "1",
  },
  scrollFlag: false,
  total: 0,
  visitEndTime: dayjs().format("YYYY-MM-DD"),
});
const clickBack = () => {
  Taro.reLaunch({
    url: "/pages/index/index",
  });
};
//切换标题

const setActiveIndex = (v) => {
  if (v === state.activeIndex) return;
  state.pageNum = 1;
  state.assistData = [];
  if (v === 1) {
    state.selectFlag = true;
  } else {
    state.selectFlag = false;
  }
  state.activeIndex = v;
  getinList();
};
// 下属筛选出来的代表
const subUsereSelect = (list) => {
  if (!list.length) {
    state.subParams.subEmpCode = initHrInfo.subUserCodeList;
  } else {
    const arr = list.map((item) => item.empCode);
    state.subParams.subEmpCode = arr;
  }

  state.total = 0;
  state.pageNum = 1;
  state.assistData = [];
  getinList();
};
const chageTime = (start, end) => {
  state.visitTime = start;
  state.visitEndTime = end;
  state.pageNum = 1;
  state.total = 0;
  state.assistData = [];
  getinList();
};

const getinList = () => {
  try {
    Taro.showLoading({
      mask: true,
      title: "加载中",
    });
  } catch (err) {}
  state.scrollFlag = true;
  let parmas = {
    visitTime: state.visitTime,
    pageNum: state.pageNum,
    pageSize: 10,
    status: state.status.join(","),
    subVisit: "0",
    visitEndTime: state.visitEndTime,
  };
  //下属参数
  if (state.activeIndex == 1) {
    parmas.subEmpCode = state.subParams.subEmpCode.join(",");
    parmas.subVisit = "1";
  }
  assistList(parmas)
    .then((res) => {
      Taro.hideLoading();
      if (!state.assistData.length) {
        state.assistData = res.rows;
      } else {
        state.assistData = [...state.assistData, ...res.rows];
      }
      state.total = res.total;
      state.scrollFlag = false;
    })
    .catch(() => {
      Taro.hideLoading();
    });
};

// 滚动加载
const scrolltolower = () => {
  if (!state.scrollFlag && state.assistData.length < state.total) {
    state.pageNum++;
    getinList();
  }
};
const activeItem = (keys) => {
  if (!state.scrollFlag) {
    // 筛选出来的状态
    state.status = keys;
    state.total = 0;
    state.pageNum = 1;
    state.assistData = [];
    getinList();
  } else {
    state.assistData = [];
  }
};
watch(
  () => {
    state.status;
  },
  () => {
    state.institutionList = [];
  }
);
useDidShow(() => {
  state.total = 0;
  state.pageNum = 1;
  state.assistData = [];
  getinList();
});
</script>
<style lang="scss">
.collaborativevisit {
  font-size: 16px;
  padding-top: 60px;
  .footerFiexd {
    margin-left: 80%;
    position: fixed;
    bottom: 20px;
    right: 20px;
  }
  .drag {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    background-color: #597dff;
    box-shadow: 0px 4px 4px 0px rgba(23, 49, 229, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    .add {
      display: inline-block;
      width: 24px;
      height: 24px;
    }
  }
  .nut-navbar__title {
    margin-left: 66px;
  }
}
</style>
