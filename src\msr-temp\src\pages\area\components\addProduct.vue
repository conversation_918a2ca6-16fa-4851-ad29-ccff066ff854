<template>
  <view class="addProduct">
    <GlobalPopup ref="popupRef">
      <TopSearch
        @top-search="search"
        placeholder="请输入产品名称搜索"
        ref="topSearchRef"
      />
      <nut-checkbox-group v-model="state.checked">
        <GlobalNutCheckbox
          v-for="item in state.proOption"
          :value="item.name"
          :key="item.name"

        >
          <template #name>
            <view
              :class="{
                      'color-label': state.checked.includes(item.name),
              }"
            >
              <text>{{ item.name }}</text>
            </view>
          </template>
        </GlobalNutCheckbox>
      </nut-checkbox-group>
      <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { reactive, ref, watch } from "vue";
import GlobalPopup from "../../../pages/components/globalPopup/globalPopup.vue";
import TopSearch from "../../../pages/components/topSearch/topSearch.vue";
import GlobalNutCheckbox from "../../../pages/components/globalNutCheckbox/globalNutCheckbox.vue";
import { productQuery } from "../../../api/area";

const topSearchRef = ref(null);
const emit = defineEmits(["select-pre"]);
const state = reactive({
  checked: [],
  search: "",
  proOption: [],
  params: {},
  type: "",
});
const popupRef = ref(null);

const getProducts = async () => {
  const res = await productQuery(state.params);

  if (state.type === "edit") {
    state.proOption = res.data.filter((item) => item.claimStatus === "3");
  } else {
    state.proOption = res.data;
  }
  console.log(state.proOption);
};
const search = (name) => {
  state.params.productName = name;
  getProducts();
};

const confirm = () => {
  const arr = state.proOption.filter((item) =>
  state.checked.includes(item.name)
  );
  emit("select-pro", arr);
  state.search = "";
  state.checked = [];
  state.proOption = [];
  popupRef.value.close();
};

const open = (type, params, checked, list) => {
  state.list = list;
  state.type = type;
  state.checked = checked;
  state.params = params;
  popupRef.value.open();

  getProducts();
};

defineExpose({
  open,
});
</script>
<style lang="scss">
.addProduct {
  padding-bottom: 60px;
  // background: #f4f5f7;
  .nut-checkbox {
    margin-right: 0;
  }
  .nut-checkbox-group {
    font-size: 16px;
    height: 400px;
    overflow: hidden;
    overflow-y: scroll;
    padding: 16px;
    justify-content: space-between;
  }
  .nut-checkbox--reverse .nut-checkbox__label {
    font-size: 16px;
  }
  .footer-comfirm {
    height: 60px;
    padding: 16px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .color-label {
    color: #2551f2;
  }
}
</style>
