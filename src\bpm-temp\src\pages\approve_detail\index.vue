<template>
  <div class="approve-detail">
    <div class="container">
      <div class="scrollable-content">
        <div class="content-wrapper p-[12px]">
          <!-- 上部分滚动内容 -->
          <div class="top mb-[8px]">
            <div class="header mb-[10px]">
              <div class="name w-[75%]">
                {{
                  getFormData(flowProcess.formData)?.[
                    getExtConfig(flowProcess?.flowProcess?.extConfig)?.mainTitle
                      ?.field
                  ]
                }}
              </div>
              <ProcessStatusTag :instanceState="`${data.instanceState}`" />
            </div>
            <div
              class="px-[6px] text-[12px] text-[#2551F2] h-[20px] leading-[20px] mr-[8px] inline-block mb-[8px]"
              style="border: 1px solid #2551f2; font-size: 12px"
            >
              {{ flowProcess?.flowProcess?.processName }}
            </div>
            <div class="mb-[10px] flex items-center">
              <div class="nameAvatar">{{ data.creator?.slice(-1) }}</div>
              <span class="user-name" style="color: #1d212b">{{
                data?.creator
              }}</span>
            </div>
            <!-- <div class='flex justify-between'> -->
            <div class="time">
              提交于 {{ dayjs(data.createTime).format("M月D日 HH:mm") }}
            </div>
            <div class="time" style="margin-top: 6px" v-if="options.formData">
              申请单号：{{ options.formData.bpmId || data.id }}
            </div>
            <!-- <div class="time" v-if="options.formData">申请单号：{{ data.id }}</div> -->
            <!-- </div> -->
          </div>
          <div class="rounded-[8px] overflow-hidden">
            <nut-tabs v-model="state.activekeys" @change="changeTab">
              <nut-tab-pane title="审批信息">
                <!-- 使用 form-create-mobile 组件 -->
                <!-- <FormCreate v-model:api="formApi" :rule="options.form || []" :disabled="true" :option="options" v-if="options.formData" />-->
                <ExternalAffiliationChannelsForm
                  :formData="options.formData"
                  v-if="formType === 'external_affiliation_channels'"
                  :formType="formType"
                />
                <SpecUnbinding
                  :formData="options.formData"
                  v-if="
                    formType === 'gs_ins_ds_spec_shelves_apply' ||
                    formType === 'gs_ins_ds_spec_unbinding_apply'
                  "
                  :formType="formType"
                />

                <TumValidDay
                  :formData="options.formData"
                  v-if="formType === 'valid_day'"
                />

                <InstitutionApply
                  :formData="options.formData"
                  v-if="formType == 'institution_apply' && options.formData"
                />
                <CustomerApply
                  :formData="options.formData"
                  v-if="formType == 'customer_apply' && options.formData"
                />
                <DrugstoreApply
                  :formData="options.formData"
                  v-if="formType == 'drugstore_apply' && options.formData"
                />
                <JurInstitutionApply
                  :formData="options.formData"
                  v-if="formType == 'jur_institution_apply' && options.formData"
                />
                <JurCustomerApply
                  :formData="options.formData"
                  v-if="formType == 'jur_customer_apply' && options.formData"
                />
                <SpeakerForm
                  :formData="options.formData"
                  v-if="
                    (formType === 'gs_speaker_update_mdm_apply' ||
                      formType === 'gs_speaker_mdm_apply') &&
                    options.formData
                  "
                />
                <CustomerOperateApply
                  :formData="options.formData"
                  v-if="
                    formType === 'customer_operate_apply' && options.formData
                  "
                />
                <InsAccessApply
                  :formData="options.formData"
                  v-if="formType === 'gs_access_apply' && options.formData"
                />
              </nut-tab-pane>
              <nut-tab-pane title="审批记录" style="min-height: 400px">
                <div class="">
                  <TimeLine
                    :flowTaskHistoryVo="flowTaskHistoryVo"
                    :todoTaskActorVo="todoTaskActorVo"
                  />
                </div>
              </nut-tab-pane>
            </nut-tabs>
          </div>
        </div>
      </div>
      <div
        class="fixed-content px-[15px] flex gap-[10px] items-center justify-center"
      >
        <!-- 下部分固定内容 -->
        <!--        <div class="w-[10%] text-center text-[#F32F29]" @click="handleTransfer()" v-show="isRefuse">-->
        <!--          <img :src="zhuanfa_icon" class="w-[20px] h-[20px]" />-->
        <!--          <div class="text-[10px] text-[#4B5666]">-->
        <!--            转发-->
        <!--          </div>-->
        <!--        </div>-->
        <div
          class="w-[50%] text-center h-[44px] leading-[44px] bg-[#FEEAEA] text-[#F32F29]"
          @click="handleRefuse()"
          v-show="isRefuse"
        >
          拒绝
        </div>
        <div
          class="w-[50%] text-center h-[44px] leading-[44px] bg-[#E8F0FF] text-[#2551F2]"
          @click="handleAgree()"
          v-show="isAgree"
        >
          同意
        </div>
        <div
          class="w-[100%] text-center h-[44px] leading-[44px] bg-[#E8F0FF] text-[#2551F2]"
          @click="handleRevoke()"
          v-show="isRevoke"
        >
          撤回
        </div>
      </div>

      <!-- <van-dialog v-model:show="showOption.show" title="审批意见" show-cancel-button @cancel="cancel()"
        @confirm="confirm()">
        <van-field v-model="opinion" rows="4" autosize label="审批意见" type="textarea" placeholder="请输入审批意见" />
      </van-dialog> -->
      <nut-popup
        v-model:visible="showOption.show"
        round
        :style="{ width: '311px' }"
      >
        <view style="padding: 16px">
          <view class="num-title-popup">审批意见</view>
          <nut-textarea
            v-model="opinion"
            limit-show
            max-length="200"
            placeholder="请输入审批意见"
          />
        </view>
        <view class="popup-footer-button">
          <view class="footer-button" @click="showOption.show = false">
            取消
          </view>
          <nut-divider
            direction="vertical"
            :style="{
              height: '48px',
              margin: 0,
              top: 0,
              borderLeft: '1px solid #E5E6EB',
            }"
          />
          <view class="footer-button" style="color: #2551f2" @click="confirm">
            确定
          </view>
        </view>
      </nut-popup>

      <TransferTask ref="transferTask" @confirm="transfer" />
    </div>
  </div>
</template>
<script setup>
import { ref, defineProps } from "vue";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import TimeLine from "./components/timeLine.vue";
import {
  taskInstanceDetailApi,
  taskInstanceFormApi,
  taskInstanceActionApi,
  agreeTaskApi,
  refuseTaskApi,
  taskNodeInfoApi,
  getFileInfo,
  revokeTaskApi,
  transferTaskApi,
} from "../../api/bpm.js";
// import FormCreate from "@form-create/vant";
import { closeToast, showLoadingToast, showToast } from "vant";
import ProcessStatusTag from "./components/processStatusTag.vue";
import InstitutionApply from "@/packages/h5/bpm/approve_form/pcb_institution_apply.vue";
import CustomerApply from "@/packages/h5/bpm/approve_form/pcb_customer_apply.vue";
import DrugstoreApply from "@/packages/h5/bpm/approve_form/pcb_drugstore_apply.vue";
import JurInstitutionApply from "@/packages/h5/bpm/approve_form/pcb_jur_institution_apply.vue";
import JurCustomerApply from "@/packages/h5/bpm/approve_form/pcb_jur_customer_apply.vue";
import ExternalAffiliationChannelsForm from "@/packages/h5/bpm/approve_form/external_affiliation_channels";
import InsDrugList from "@/packages/h5/bpm/approve_form/external_affiliation_channels_ins_drug";
import TumValidDay from "@/packages/h5/bpm/approve_form/tum_valid_day";
import SpeakerForm from "@/packages/h5/bpm/approve_form/speaker.vue";
import { showConfirmDialog } from "vant";
import zhuanfa_icon from "./../../assets/images/zhuanfa_icon.png";
import TransferTask from "./components/transferTask.vue";
import SpecUnbinding from "@/packages/h5/bpm/approve_form/spec_unbinding.vue";
import CustomerOperateApply from "@/packages/h5/bpm/approve_form/customer_operate_apply.vue";
import InsAccessApply from "@/packages/h5/bpm/approve_form/ins_access_apply.vue";

const router = useRouter();
const query = router.params;

/**
//  * formType表单类型, 用于判断展示哪个表单
 * formType改为通过data?.flowProcess?.processKey获取流程唯一标识
 * 一般情况下使用提交时的表单改为只读，以<原表单页面vue文件名>ApproveForm.vue命名的文件,import进来
 * OPTIMIZE: 使用formType和v-if选择渲染哪个表单的做法仅为临时方案。后期开发使用code判断应渲染展示的代码片段时需要重新设计。
 */
const formType = ref("");
// formType.value = query.formType

const showOption = ref({
  type: "agree",
  show: false,
});
const state = ref({});
const active = ref(0);
const fApi = ref({});
const formData = ref({});
const data = ref({});
const flowTaskHistoryVo = ref([]);
const todoTaskActorVo = ref({});
// 审批意见
const opinion = ref(null);
const currentNodeInfo = ref({});
const isAgree = ref(false);
const isRefuse = ref(false);
const isRevoke = ref(false);
const transferTask = ref();
const processName = ref("");
const flowProcess = ref({});

const options = ref({
  onSubmit: (formData) => {
    alert(JSON.stringify(formData));
  },
  submitBtn: { show: false },
  resetBtn: { show: false },
  formData: null,
});
const rule = ref([
  {
    type: "input",
    field: "goods_name",
    title: "商品名称",
    value: "form-create",
  },
  {
    type: "checkbox",
    field: "label",
    title: "标签",
    value: [0, 1, 2, 3],
    options: [
      { label: "好用", value: 0 },
      { label: "快速", value: 1 },
      { label: "高效", value: 2 },
      { label: "全能", value: 3 },
    ],
  },
]);

const getDetail = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await taskInstanceDetailApi({
      instanceId: query.instanceId,
    });
    data.value = res?.data || {};
    await getFormDetail();
    await getAction();
  } catch (e) {
  } finally {
    closeToast();
  }
};

// 院外关联渠道
const external_affiliation_channels = [
  "gs_special_external_affiliation_channels_apply",
  "gs_routine_external_affiliation_channels_apply",
];
const getFormDetail = async () => {
  try {
    const res = await taskInstanceFormApi({
      processId: data.value.processId,
      instanceId: query.instanceId,
    });

    const formData =
      (res.data?.formData && JSON.parse(res.data.formData)) || {};
    processName.value = res?.data?.flowProcess?.processName;
    flowProcess.value = res?.data || {};
    // 获取审批表单类型
    formType.value = res?.data?.flowProcess?.processKey;
    console.log("formType.value", formType.value);
    if (
      external_affiliation_channels.includes(res?.data?.flowProcess?.processKey)
    ) {
      formType.value = "external_affiliation_channels";
    }
    console.log("....", formType.value);
    // await refactorFileList(formData);
    options.value.formData = formData;
  } catch (e) {
  } finally {
  }
};

const refactorFileList = async (data) => {
  const attUrlList = data.attUrlList || [];
  const _ = await Promise.all(
    attUrlList.map(async (d) => {
      const res = await getFileInfo(d.ossId);
      return res?.data?.rows && res.data.rows[0] && res.data.rows[0].url;
    })
  );
  data.attUrlList = _;
};

const getAction = async () => {
  const res = await taskInstanceActionApi({
    instanceId: query.instanceId,
  });

  try {
    flowTaskHistoryVo.value = res?.data?.flowTaskHistoryVo || [];
    todoTaskActorVo.value = res?.data?.todoTaskActorVo;

    if (
      res?.data?.todoTaskActorVo?.taskKey ===
      res?.data?.flowTaskHistoryVo?.[res?.data?.flowTaskHistoryVo?.length - 1]
        ?.taskKey
    ) {
      res.data.flowTaskHistoryVo.splice(
        res.data.flowTaskHistoryVo.length - 1,
        1
      );
    }

    isAgree.value = res.data?.isAgree;
    isRevoke.value = res.data?.isRevoke;
    isRefuse.value = res.data?.isRefuse;
  } catch (e) {
    console.log(e);
  }
};

// 获取节点详情
const getNodeInfo = async () => {
  const res = await taskNodeInfoApi({
    processId: query.processId,
    nodeKey: query.currentNode,
  });
  currentNodeInfo.value = res.data || {};
};

// 审批流操作 同意/拒绝
const approveHandle = async (type) => {
  try {
    // if (type === 'refuse' && (opinion.value === '' || opinion.value === null || opinion.value === undefined)) {
    //   showToast({
    //     message: '请输入审批意见',
    //   })
    //   return false
    // }
    Taro.showLoading({
      mask: true,
      title: "加载中",
    });
    const _query = {
      taskId: data.value.taskId,
      instanceId: query.instanceId,
      opinion: opinion.value,
    };
    if (type === "agree") {
      await agreeTaskApi(_query);
    } else if (type === "refuse") {
      await refuseTaskApi(_query);
    } else if (type === "revoke") {
      await revokeTaskApi(_query);
    }

    showOption.show = false;
    Taro.hideLoading();
    if (query.source) {
      Taro.redirectTo({
        url: "/pages/index/index",
      });
    } else {
      Taro.navigateBack({
        delta: 1,
      });
    }
  } catch (e) {
    closeToast();
  } finally {
  }
};

const confirm = () => {
  approveHandle(showOption.value.type);
};

const cancel = () => {
  showOption.value.show = false;
  showOption.value.type = "";
};

const handleRefuse = () => {
  showOption.value.show = true;
  showOption.value.type = "refuse";
};

const handleAgree = () => {
  showOption.value.show = true;
  showOption.value.type = "agree";
};

const handleRevoke = () => {
  showOption.value.show = true;
  showOption.value.type = "revoke";
};

const beforeClose = (action) =>
  new Promise((resolve) => {
    // action !== 'confirm'  拦截取消操作
    if (action === "confirm") {
      approveHandle("revoke");
      resolve(true);
    } else {
      resolve(true);
    }
  });

const handleTransfer = () => {
  transferTask.value?.open();
};

const transfer = async (e) => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    await transferTaskApi({
      instanceId: query.instanceId,
      userId: e.userId,
      empCode: e.userName,
    });
    closeToast();
    showToast("操作成功！");
    await Taro.navigateBack({
      delta: 1,
    });
  } catch (e) {
    console.log(e, "eee");
    showToast(e.msg);
  }
};

const getExtConfig = (str) => {
  if (!str) return {};
  try {
    return JSON.parse(str).keyWords;
  } catch (e) {
    return {};
  }
};

const getFormData = (str) => {
  if (!str) return {};
  try {
    return JSON.parse(str);
  } catch (e) {
    return {};
  }
};

useDidShow(() => {
  getDetail();
  // getNodeInfo();
});
</script>
<style lang="less">
.container {
  position: relative;
  height: 100vh;
  /* 设置容器高度为视口高度 */
  overflow: hidden;
  /* 隐藏溢出的内容 */
}

.scrollable-content {
  position: absolute;
  top: 0;
  bottom: 66px;
  /* 下部分固定内容的高度 */
  left: 0;
  right: 0;
  overflow-y: auto;
  /* 允许垂直滚动 */
}

.content-wrapper {
  // 背景渐变色
  background: linear-gradient(to bottom, #d2e7ff 0%, #edeff5 30%);
}

.fixed-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 66px;
  /* 下部分固定内容的高度 */
  background-color: #fff;
}

.flowTaskHistory {
  padding-top: 16px;
  background-color: #fff;
}

.approve-detail {
  .top {
    background: #fff;
    border-radius: 8px;
    padding: 16px;

    .header {
      display: flex;
      justify-content: space-between;

      .name {
        font-weight: 600;
        font-size: 20px;
        color: #1d212b;
      }

      .status {
        font-size: 12px;
      }
    }

    .user-name {
      color: #1d212b;
      font-size: 14px;
    }

    .time {
      color: #869199;
      font-size: 14px;
    }

    .nameAvatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #92a8f8;
      color: #fff;
      line-height: 24px;
      text-align: center;
      min-width: 24px;
      margin-right: 4px;
      font-size: 12px;
    }
  }

  .num-title-popup {
    font-size: 16px;
    color: #121d29;
    text-align: center;
    margin-bottom: 10px;
  }

  .popup-footer-button {
    height: 48px;
    line-height: 48px;
    display: flex;
    align-items: center;
    border-top: 1px solid #e5e6eb;

    .footer-button {
      width: 50%;
      text-align: center;
      font-size: 16px;
    }
  }
}

.van-tabs__line {
  background: #2551f2;
}
.nut-textarea {
  background: #f4f5f7 !important;
}
</style>
