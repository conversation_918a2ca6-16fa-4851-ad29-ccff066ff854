<template>
  <view>
    <view class="visit-imagePreview">
      <view style="padding: 16px 0; min-width: 63px; color: #869199">拍照</view>
      <view class="photo-list">
        <view
          class="photo"
          v-for="(photo, index) in state.detailImageList"
          :key="photo.ossId"
        >
          <img
            :src="photo.url"
            alt=""
            style="width: 80px; height: 80px"
            @click="lookImage(index)"
          />
        </view>
       
      </view>
    </view>
    <nut-image-preview
      :show="state.showPreview"
      :images="state.imgData"
      :init-no="initNo"
      @close="state.showPreview = false"
      style="width: 100%; height: 100%"
    />
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  computed,
  watch,
  onMounted,
} from "vue";
import { imageUrl } from "../../../api/institutionalVisitsApi";
const initNo = ref(0);
const state = reactive({
  detailImageList: [],
  showPreview: false,
  imgData:[]
});
const lookImage = (i) => {
  console.log("查看", i);
  initNo.value = i;
  state.showPreview = true;
};

const getImgList = (list) => {
  if( !list) return 
  state.detailImageList = []
  state.imgData = []
 list.forEach(async (item) => {
    const res = await imageUrl(item);
    state.detailImageList.push(res.data.rows[0]);
    state.imgData.push({src:res.data.rows[0].url});
    
  });
 
  console.log( "11111", state.detailImageList,state.imgData,)
};

defineExpose({
  getImgList
});

</script>
<style lang="scss" scoped>
.visit-imagePreview {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 8px;
  .photo-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    .photo {
      width: 80px;
      height: 80px;
      position: relative;
      overflow: hidden;
      .del {
        position: absolute;
        font-size: 12px;
        right: -12px;
        top: -10px;
        width: 24px;
        height: 24px;
        background: black;
        border-radius: 50%;
        text-align: center;
        line-height: 24px;
        color: #fff;
        padding-top: 7px;
        padding-right: 7px;
      }
      img {
        width: 100%;
      }
    }
  }
}
</style>
