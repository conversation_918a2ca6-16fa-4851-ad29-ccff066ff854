<template>
  <view class="target-detail">
    <view class="detail-top">
      <view class="top-name">
        <view style="color: #1d212b; font-size: 20px; font-weight: 500">{{
          state.obj.crmInsName
        }}</view>
        <view style="color: #4e595e; font-size: 14px"
          >{{ decodeURIComponent(query.nickName)
          }}<tetx v-if="query.deptName"
            >({{ decodeURIComponent(query.deptName) }})</tetx
          ></view
        >
      </view>
      <view class="top-item">
        <view
          class="top-text"
          style="display: flex; align-items: center; justify-content: center"
        >
          <img :src="caretLeft" class="top-icons" @click="prevMonth" />
          <view
            >{{ state.month }}月推广有效性<text style="font-size: 11px"
              >(元)</text
            ></view
          >
          <img :src="caretRight" class="top-icons" @click="nextMonth" />
        </view>
        <view class="top-num" style="color: #ffb637">
          {{
            formatNumber((state.proList.length && state.proList[0].sum) || "")
          }}
        </view>
      </view>
    </view>
    <view class="detail-table">
      <view class="item-report">
        <view
          class="item-table-title"
          style="background: #f3f4f5; border-radius: 8px 8px 0 0"
        >
          <view class="label">产品推广有效性类别</view>
          <view class="value" style="color: #869199">推广有效性额(元)</view>
        </view>
        <view class="item-table">
          <view
            class="item-table-item"
            v-for="(item, index) in state.proList.length &&
            state.proList[0].details"
            :key="index"
            :style="{
              'border-bottom':
                index == state.proList[0].details.length - 1
                  ? ''
                  : '1px solid #e5e6eb',
            }"
          >
            <view class="label">{{ item.productName }}</view>
            <view class="value">{{ formatNumber(item.sum || "") }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow, useDidHide } from "@tarojs/taro";
import { reactive, onMounted } from "vue";
import caretLeft from "@/images/caret-left.png";
import caretRight from "@/images/caret-right.png";
import { formatNumber } from "@/utils/pharmacy";
import { targetDetail } from "@/api/target";

const router = useRouter();
const query = router.params;
const state = reactive({
  month: null,
  info: {},
  params: {
    year: "",
    postCode: "",
    insCrmCode: "",
  },
  proList: [],
  obj: {},
});
const prevMonth = () => {
  if (state.month === 1) {
    state.month = 12; // 如果是1月，切换到12月
  } else {
    state.month--; // 否则，减去1个月
  }
  state.proList = state.obj.monthList.filter(
    (item) => item.month == state.month - 1
  );
};
const nextMonth = () => {
  if (state.month === 12) {
    state.month = 1; // 如果是12月，切换到1月
  } else {
    state.month++; // 否则，增加1个月
  }
  state.proList = state.obj.monthList.filter(
    (item) => item.month == state.month - 1
  );

  console.log(state.proList);
};

const getTargetDetail = async () => {
  const res = await targetDetail(state.params);
  state.obj = res.data;
  state.proList = res.data.monthList.filter(
    (item) => item.month === state.month - 1
  );
  console.log("state.proList", state.proList);
};

const openDetail = () => {};
onMounted(() => {
  Taro.setNavigationBarTitle({ title: "推广有效性" });
  state.info = {
    ...JSON.parse(decodeURIComponent(query.detail)),
  };
  state.month = Number(query.month);
  state.params = {
    year: state.info.year,
    postCode: state.info.postCode,
    insCrmCode: state.info.insCrmCode,
  };
  getTargetDetail();
});
</script>
<style lang="scss">
.target-detail {
  .detail-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    .top-item {
      min-width: 102px;
      height: 64px;
      background: #fcfcfc;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #e5e6eb;
      text-align: center;
      .top-text {
        margin-top: 8px;
        color: #4e595e;
        font-size: 14px;

        .top-icons {
          width: 16px;
          height: 16px;
          display: block;
          vertical-align: middle;
        }
      }
      .top-num {
        color: #1d212b;
        font-size: 22px;
        font-weight: bold;
      }
    }
  }
  .detail-table {
    margin-top: 8px;
    background: #fff;
    padding: 16px;
    border-radius: 16px;
    height: 70vh;
    .item-report {
      .item-table {
        border: 1px solid #e5e6eb;
        border-radius: 0 0 8px 8px;
        .item-table-item {
          display: flex;
          align-items: center;
          padding: 8px 0;
          // border-bottom: 1px solid #e5e6eb;

          .label {
            padding-left: 16px;
            width: 70%;
            color: #121d29;
            font-size: 12px;
            text-align: left;
          }
          .value {
            width: 30%;
            color: #2551f2;
            font-size: 14px;
            text-align: center;
          }
        }
      }
      .item-table-title {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border: 1px solid #e5e6eb;
        border-bottom: none;

        .label {
          padding-left: 16px;
          width: 70%;
          color: #869199;
          font-size: 14px;
          text-align: left;
        }
        .value {
          width: 30%;
          color: #869199;
          font-size: 14px;
          text-align: center;
        }
      }
    }
  }
}
</style>
