<template>
  <view class="search-speaker">
    <view class="section">
      <CellClick @selectClick="selectInsName" :value="state.insName" :required="true" label="医疗机构名称" placeholder="请选择"
        :phRight="true" />
      <nut-input v-model="state.params.name" placeholder="请输入" clearable :border="false" :formatter="formatter"
        input-align="right">
        <template #left>
          <view class="label">讲者姓名</view>
        </template>
      </nut-input>
      <view class="footer-button">
        <nut-row type="flex" justify="center">
          <nut-col :span="24">
            <nut-button color="#2551F2" @click="clickSearchButton" shape="square" block
              :disabled="!state.params.name">搜索</nut-button>
          </nut-col>
        </nut-row>
      </view>
    </view>

    <view class="search-text" v-if="num === 0">{{ state.text1 }}</view>
    <view class="result-text" style="font-size: 16px">
      <scroll-view style="height: 500px" :scroll-y="true" :scroll-top="state.scrollTop" @scrolltolower="onScrollToLower">
        <view class="result" v-if="num > 0 && !state.loading">
          <view style="color: #869199; font-size: 14px;" @click=tips>
            <IconFont name="tips" size="12px" style="vertical-align: middle;"></IconFont>查询帮助
          </view>
          <view style="color: #2551f2;margin-left: 50px;">查询结果如下</view>
        </view>
        <view v-if="num > 0 && !state.loading && !state.speakerList.length">
          <view class="no-result">讲者库中暂无相关人员信息</view>
          <view class="no-result">请点击下方的新增讲者按钮继续</view>
        </view>
        <view v-for="item in state.speakerList" :key="item.dsCode" class="instItem">
          <view style="margin-bottom: 10px; text-align: center;padding: 12px;">
            <view class="custName">{{ item.name }} 
              <nut-tag
                plain
                color="#C6CAD1"
                text-color="#4E595E"
                style="margin-top: -4px; vertical-align: middle"
                v-if="item.type === 'add'"
              >
                {{ item.type }}
              </nut-tag>
              <nut-tag
              v-else
                plain
                color="#94BFFF"
                text-color="#2551F2"
                style="margin-top: -4px; vertical-align: middle"
              >
                {{ item.type }}
              </nut-tag>
             
            </view>
            <view class="insName">{{ item.insName }}</view>
          </view>
          <view style="padding-right: 12px;">
            <view class="righeItem" @click="clickAddButton('add')" v-if="item.oprType === 'add'">新增为医生讲者</view>
            <view v-else>
              <view class="righeItem" @click="speakerDetail(item)">查看</view>
              <view class="righeItem" @click="clickAddButton('edit',item)">变更</view>
            </view>
          </view>
        </view>
        <view v-if="state.speakerList.length > 0 && state.speakerList.length == state.total"
          style="color: #869199; font-size: 16px; text-align: center">已全部加载完毕</view>
      </scroll-view>
    </view>

    <FooterButton text="新增讲者" @click-button="clickAddButton('add')" :buttonIcon="arrowRight"
      v-if="num !== 0 || state.insName" :disabled="num === 0 || !state.insName">
      <template #icon>
        <Uploader color="#fff" />
      </template>
    </FooterButton>
  </view>
  <SelectIns ref="selectInsRef" @ins-confirm="insConfirm" />
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { onMounted, reactive, watch, ref } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import CellClick from "../../../pages/components/cellClick/index.vue";
import { Uploader } from "@nutui/icons-vue-taro";
import arrowRight from "../../../images/arrow-right.png";
import arrow from "../../../images/arrow.png";
import { formatter } from "../../../utils/customer.js";
import { speakeAddSearchApi } from "../../../api/speaker.js";
import SelectIns from './selectIns'
import { IconFont } from '@nutui/icons-vue-taro'
const initHrInfo = Taro.getStorageSync("initHrInfo");
const router = useRouter();
const scrollMyFlag = ref(false);
const num = ref(0);
const query = router.params;
const selectInsRef = ref(null)
const state = reactive({
  footerButtontext: "搜索",
  speakerList: [],
  scrollTop: 0,
  total: 0,
  loading: false,
  insName: '',
  params: {
    name: '',
    institutionCode: ''
  },
  text1: '请先选择机构，输入讲者姓名查询讲者是否已存在若无查询结果，点击搜索后下方出现的新增讲者按钮继续,若查询到客户，可选择客户新增为讲者，若查询到讲者可查询讲者信息并变更'
});

const getspeakerList = () => {
  state.loading = true;
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  speakeAddSearchApi(state.params)
    .then((res) => {
      if (res.code == 200) {
        state.speakerList = res.data || [];
        Taro.hideLoading();
        state.loading = false;
      } else {
        Taro.hideLoading();
      }
    })
    .catch(() => {
      Taro.hideLoading();
      state.loading = false;
    });
};
const clickSearchButton = () => {
  num.value++;
  if (state.loading) return;
  state.speakerList = [];
  getspeakerList();
};

const clickAddButton = (type,item) => {
  if (type == 'add') {
    Taro.navigateTo({
      url: `/pages/speaker/components/addSpeaker?type=${type}&name=${state.params.name}&insName=${state.insName}&institutionCode=${state.params.institutionCode}`,
    });
  } else {
    
    Taro.navigateTo({
      url: `/pages/speaker/components/addSpeaker?type=${type}&info=${JSON.stringify(item)}`,
    });
  }

};

const speakerDetail = (item) => {
  if (item.isClaim === "0") return;
  Taro.navigateTo({
    url: `pages/speaker/components/speakerDetail?code=${item.code}`,
  });
};

const selectInsName = () => {
  selectInsRef.value.open()
}

const insConfirm = (ins) => {
  state.insName = ins.insName
  state.params.institutionCode = ins.code
}

const tips = () => {

  Taro.showModal({
    title: '查询帮助',
    content: state.text1,
    confirmText: "确定",
    showCancel: false,
  });

}
watch(
  () => state.params.name,
  (newValue) => {
    if (!newValue) {
      num.value = 0;
      state.speakerList = [];
    }
  }
);
onMounted(() => {
  Taro.setNavigationBarTitle({ title: "讲者新增/变更" });
});
</script>
<style lang="scss">
.search-speaker {
  padding-bottom: 80px;

  .section {
    background: #fff;
    font-size: 16px;
    padding: 0 16px;

    .arrowIcon {
      width: 16px;
      height: 16px;
      margin-top: 2px;
    }
  }

  .search-text {
    color: #869199;
    font-size: 14px;
    padding: 8px 32px;
    text-align: center;
  }

  .result-text {

    text-align: center;
  }

  .result {
    display: flex;
    padding: 16px 24px;
    align-items: center;
  }

  .no-result {
    color: #869199;
    font-size: 12px;
    text-align: center;
  }

  .instItem {
    background-color: #ffffff;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .custName {
      font-weight: 500;
      font-size: 16px;
      color: #1d212b;
      text-align: left;
    }

    .insName {
      text-align: left;
      font-size: 14px;
      color: #869199;
    }

    .righeItem {
width: 102px;
      height: 32px;
      background: #e8f0ff;
      border-radius: 100px 100px 100px 100px;
      border: 1px solid #e8f0ff;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #2551f2;
      line-height: 33px;
      text-align: center;
      margin: 4px 0;
    }

  }

  .label {
    &::before {
      content: "*";
      color: red;
      margin-top: 6px;
      margin-right: 5px;
      vertical-align: middle;
    }
  }

  .nut-radio-group {
    padding: 0 16px;
  }

  .footer-button {
    background-color: #ffffff;
    padding: 8px 0;
    width: 100%;

    .nut-button {
      border-radius: 4px;
    }

    .border-none {
      border: none;
    }

    .nut-button__wrap {
      img {
        width: 10px;
        height: 10px;
      }
    }
  }

  .input-text::placeholder {
    color: #c9cdd0;
  }
}</style>
