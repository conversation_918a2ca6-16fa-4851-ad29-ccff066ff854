<template>
  <view class="ins-addIns">
    <view>
      <nut-form :model-value="state.params" ref="ruleForm">
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">基本信息</view>
        <view class="section">
          <nut-form-item label="姓名" prop="name" required :label-width="100"
            :rules="[{ required: true, message: '请填写姓名' }]">
            <nut-input v-model="state.params.name" clearable input-align="right" :border="false" placeholder="请输入"
              :max-length="100">
            </nut-input>
          </nut-form-item>
          <nut-form-item label="医疗机构" prop="insName" required :label-width="100"
            :rules="[{ required: true, message: '请选择医疗机构' }]">
            <nut-input v-model="state.params.insName" clearable input-align="right" :border="false" placeholder="请选择"
              style="opacity: 0; height: 0; width: 0; overflow: hidden" @click="selectInsName"
              v-if="!state.params.insName">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
            <view v-else @click="selectInsName" style="font-size: 16px; text-align: right">{{ state.params.insName || `>`
            }}</view>
          </nut-form-item>
          <nut-form-item label="讲者类型" prop="type" :label-width="100" required
            :rules="[{ required: true, message: '请选择机构级别' }]">
            <nut-input v-model="state.params.type" clearable input-align="right" :border="false" placeholder="请选择"
              readonly @click="selectClick('speakerType')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>

          <nut-form-item label="退休返聘" prop="isRehired" :label-width="100" required
            :rules="[{ required: true, message: '请选择退休返聘' }]">
            <nut-input v-model="state.params.isRehired" clearable input-align="right" :border="false" placeholder="请选择"
              readonly @click="selectClick('retireStatus')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>

          <nut-form-item label="行政级别" prop="job" :label-width="100" required
            :rules="[{ required: true, message: '请选择行政级别' }]">
            <nut-input v-model="state.params.job" clearable input-align="right" :border="false" placeholder="请选择" readonly
              @click="selectClick('administrationLvl')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item label="省市区" prop="district" required :label-width="100"
            :rules="[{ required: true, message: '请选择省市区' }]">
            <nut-input v-model="state.params.district" clearable input-align="right" :border="false" placeholder="请输入"
              readonly @click="selectClick('district')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>


          <nut-form-item label="学术评级" prop="academicRating" :label-width="100">
            <nut-input v-model="state.params.academicRating" clearable input-align="right" :border="false"
              placeholder="请选择" readonly @click="selectClick('academyLevel')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>


          <nut-form-item label="品牌评级" prop="brandRating" :label-width="100">
            <nut-input v-model="state.params.brandRating" clearable input-align="right" :border="false" placeholder="请选择"
              readonly @click="selectClick('brandLevel')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>


          <nut-form-item label="准入评级" prop="accessRating" :label-width="100">
            <nut-input v-model="state.params.accessRating" clearable input-align="right" :border="false" placeholder="请选择"
              readonly @click="selectClick('accessLevel')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>


          <nut-form-item label="抗衰评级" prop="antiAgingRating" :label-width="100">
            <nut-input v-model="state.params.antiAgingRating" clearable input-align="right" :border="false"
              placeholder="请选择" readonly @click="selectClick('antiAgingRating')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>


          <nut-form-item label="治疗领域" prop="therapyArea" :label-width="100" required
            :rules="[{ required: true, message: '请选择机构级别' }]">
            <nut-input v-model="state.params.therapyArea" clearable input-align="right" :border="false" placeholder="请选择"
              readonly @click="selectClick('therapyArea')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>



          <nut-form-item label="性别" prop="sexName" :label-width="100" required
            :rules="[{ required: true, message: '请选择机构级别' }]">
            <nut-input v-model="state.params.sexName" clearable input-align="right" :border="false" placeholder="请选择" readonly
              @click="selectClick('sex')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>



          <nut-form-item label="电话" prop="phone" :label-width="100" required
            :rules="[{ required: true, message: '请输入电话', validator: phoneValidator }]">
            <nut-input v-model="state.params.phone" clearable input-align="right" :border="false" placeholder="请输入"
             >
            </nut-input>
          </nut-form-item>

          <nut-form-item label="邮件" prop="email" :label-width="100"  :rules="[{ required: true, message: '请输入电话', validator: emilValidator }]">
            <nut-input v-model="state.params.email" clearable input-align="right" :border="false" placeholder="请输入"
              :max-length="100">
            </nut-input>
          </nut-form-item>
        </view>
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">补充-身份证</view>
        <view class="section">
          <view>客户身份证验真要求照片清晰，请上传身份证正面照片</view>
          <view style="margin-left: 25%;">
            <view class="photos">
              <img :src="idcard" alt="" style="width: 140px;height: 90px;margin-top: 16px;" v-if="!state.cardImageSrc" />
              <img :src="state.cardImageSrc" alt="Base64 Image" style="width: 140px;height: 90px;margin-top: 16px;" v-else />

            </view>
            <nut-button color="#2551f2" size="mini" shape="square" style="width: 162px;" @click="ocrChooseImage('card')">上传身份证
            </nut-button>
          </view>
          <nut-form-item label="证件类型" prop="idCardName" :label-width="100" required
            :rules="[{ required: true, message: '请选择证件类型' }]">
            <nut-input v-model="state.params.idCardName" clearable input-align="right" :border="false" placeholder="请选择"
              readonly @click="selectClick('idCardType')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <nut-form-item label="身份证号码" prop="idCardNum" :label-width="100" required
            :rules="[{ required: true, message: '请输入身份证号码',validator: cardValidator }]">
            <nut-input v-model="state.params.idCardNum" clearable input-align="right" :border="false" placeholder="请输入"
             >
            </nut-input>
          </nut-form-item>

          <nut-form-item label="地址" prop="idCardAddress" :label-width="100" required
            :rules="[{ required: true, message: '请输入地址' }]">
            <nut-input v-model="state.params.idCardAddress" clearable input-align="right" :border="false"
              placeholder="请输入" >
            </nut-input>
          </nut-form-item>

        </view>
        <view style="
            color: #869199;
            font-size: 14px;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
          ">补充-银行卡</view>
        <view class="section">
          <view>客户身份证验真要求照片清晰，请上传银行卡正面照片</view>
          <view style="margin-left: 25%;">
            <view class="photos">
              <img :src="bank" alt="" style="width: 140px;height: 90px;margin-top: 16px;" v-if="!state.bankImageSrc">
              <img :src="state.bankImageSrc" alt="Base64 Image" style="width: 140px;height: 90px;margin-top: 16px;" v-else />
            </view>
            <nut-button color="#2551f2" size="mini" shape="square" style="width: 162px;"  @click="ocrChooseImage('bank')">上传银行卡
            </nut-button>
          </view>
          <nut-form-item label="开户银行" prop="bankName" :label-width="100" required
            :rules="[{ required: true, message: '请输入开户银行' }]">
            <nut-input v-model="state.params.bankName" clearable input-align="right" :border="false" placeholder="请输入"
          >
            </nut-input>
          </nut-form-item>
          <nut-form-item label="银行卡号 " prop="bankNum" :label-width="100" required
            :rules="[{ required: true, message: '请输入身份证号码',validator: bankNumValidator }]">
            <nut-input v-model="state.params.bankNum" clearable input-align="right" :border="false" placeholder="请输入"
              :max-length="50">
            </nut-input>
          </nut-form-item>

          <nut-form-item label="开户省市区/县" prop="bankDistrict" required :label-width="100"
            :rules="[{ required: true, message: '请选择省市区' }]">
            <nut-input v-model="state.params.bankDistrict" clearable input-align="right" :border="false" placeholder="请输入"
              readonly @click="selectClick('bankDistrict')">
              <template #right>
                <img :src="arrow" class="arrowIcon" />
              </template>
            </nut-input>
          </nut-form-item>
          <view class="photo">
            <nut-form-item label="备注" prop="remark" star-position="left">

              <nut-textarea v-model="state.params.remark" limit-show max-length="200" placeholder="请输入" />
            </nut-form-item>
            <nut-form-item label="附件" prop="fileList" star-position="left">
              <view class="upload">
                <view class="item-img" v-for="(item, index) in state.params.fileList" :key="index">
                  <img :src="item.url || item.attUrl" style="width: 80px; height: 80px" @click="handlePreview(index)" />
                  <img :src="fileClose" class="file-close" @click="handleDelFile(item)"/>
                </view>
                <view @click="takePhoto()">
                  <img :src="CameraImg" class="camera-img" />
                </view>
              </view>
            </nut-form-item>
            <nut-image-preview v-if="state.params.fileList" :autoplay="0" :show="showPreview" :images="state.params.fileList.map((d) => ({ src: d.url || d.attUrl }))
              " @close="hideImgPreview" :init-no="imgPreviewIndex" />
          </view>
        </view>
      </nut-form>
      <FooterButton text="提交申请" :isLoading="isLoading" @click-button="clickRight" />
    </view>
    <PopupRadio ref="popupRadioRef" @radio-confirm="radioConfirm" />
    <SelectIns ref="selectInsRef" @ins-confirm="insConfirm" />
    <DistrictPopup ref="districtPopupRef" @district-confirm="districtConfirm" />
  </view>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
import CellClick from "../../../pages/components/cellClick/index.vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import PopupRadio from "../../../pages/components/popupRadio/popupRadio";

import { ocrCardApi,ocrBankApi,addSpeakerApi, ocrCardV2Api } from "../../../api/speaker.js";
import arrow from "../../../images/arrow.png";
import location_ins from "../../../images/location_ins.png";
import SelectIns from "./selectIns.vue";
import DistrictPopup from "../../../pages/components/districtPopup/index";
import { appCode, tenantId } from "../../../utils/content";
import idcard from "../../../images/idcard.png";
import bank from "../../../images/bank.png";
import CameraImg from "../../../images/uploader.png";
import fileClose from "../../../images/file-close.png";
import { apiAuth } from "../../../utils/feishuAuth";
import { baseApi,getBase64 } from "../../../utils/content";
import { imageUrl } from "../../../api/institutionalVisitsApi";
import { processListApi } from './../../../api/bpm';
const initHrInfo = Taro.getStorageSync("000010-initHrInfo");
const orgList = initHrInfo.orgList.filter((item) => item.postType === "1")[0];
const isLoading = ref(false);
const selectInsRef = ref(null);
const router = useRouter();
const query = router.params;
const popupRadioRef = ref(null);
const gdmapRef = ref(null);
const ruleForm = ref(null);
const districtPopupRef = ref(null);
const TokenKey = Taro.getStorageSync("000010-access_token");
const imgPreviewIndex = ref(0);
const showPreview = ref(0);
const phoneValidator = (val) => {
  if (/^1\d{10}$/.test(val)) {
    return Promise.resolve();
  } else {
    return Promise.reject("手机号格式不正确");
  }
};


const emilValidator = (val) => {
  if (/^[^@]+@[^@]+$/.test(val) || !val) {
    return Promise.resolve();
  } else {
    return Promise.reject("邮件格式不正确");
  }
};
const cardValidator = (val) => {
  if (/^\d{17}(\d|X|x)$/.test(val) || !val) {
    return Promise.resolve();
  } else {
    return Promise.reject("身份证格式不正确");
  }
};
const  bankNumValidator = (val) => {
  if (/^\d+$/.test(val)) {
    return Promise.resolve();
  } else {
    return Promise.reject("必须输入数字");
  }
};

const processId = ref('');
const state = reactive({
  rightText: "确定", //根据进来的情况判断

  info: {
    applicant: initHrInfo.empName,
    applicantCode: initHrInfo.empCode,
    postCode: orgList.code,
    postName: orgList.name,
    deptCode: orgList.deptCode,
    deptName: orgList.value,
    ancestors: orgList.ancestors,
    postIdList: initHrInfo.postIdList,
  },
  params: {
    name: '',
    insName: '',
    insCode: '',
    type: '',
    isRehired: '',
    provinceCode: '',
    cityCode: '',
    districtCode: '',
    district: '',
    academicRating: '',
    brandRating: '',
    accessRating: '',
    antiAgingRating: '',
    therapyArea: '',
    sex: '',
    sexName: '',
    phone: '',
    email: '',
    idCardType: '',
    idCardName:'',
    idCardNum: '',
    idCardAddress: '',
    bankName:'',
    bankCityCode: '',
    bankNum: '',
    bankProvinceCode: '',
    bankDistrictCode: '',
    bankDistrict: '',
    remark: '',
    fileList: []
  },
  masterValue: query.type === "add" ? "是" : "否",
  type: "",
  list: [
    { dictLabel: "是", dictValue: "是" },
    { dictLabel: "否", dictValue: "否" },
  ],
  sexValue: "",
  url: process.env.TARO_APP_API + `/hr/file/upload-picture`, // 讲者的需要固定写死，上传到000010租户下
  headers: {
    clientid: process.env.TARO_APP_CLIENT_ID,
    Authorization: `Bearer ${TokenKey}`,
  },
  cardImageSrc:'',
  bankImageSrc:'',
  sexList: [
      { dictLabel: "男", dictValue: "0" },
      { dictLabel: "女", dictValue: "1" },
    ],
});

const feishuNotice = ref({
  appUrl: 'https://accounts.feishu.cn/open-apis/authen/v1/authorize?app_id=cli_a648641833f9100c&redirect_uri=https://athena-gen-mp-dev.dgtmeta.com/pages/gen/index/&scope=&state=speaker_approve_',
  pcUrl: 'https://athena-hr-dev.dgtmeta.com/approve_detail?instanceId='
})

const clickRight = async () => {
  Taro.showLoading({
    title: "加载中",
    icon: "loading",
  });
  ruleForm.value.validate().then(async ({ valid, errors }) => {
    if (valid) {
      isLoading.value = true;
      console.log("ddddd",state.params)
      const params = {
        applyContent: {
          ...state.params,
        },
        enableWorkflow: true,
        applyType: query.type ==='add' ? '10' : '11',
        appCode: appCode(),
        tenantId: '000010',
        ...state.info,
        processId: processId.value,
        ...feishuNotice.value
      };

      const res = await addSpeakerApi(params);
      isLoading.value = false;
      if (res.code === 200) {
        Taro.showToast({
          title: "成功",
          icon: "none",
          duration: 2000,
        });
        Taro.hideLoading();
        Taro.reLaunch({
          url: `/pages/speaker/index`,
        });
      }
    } else {
      Taro.hideLoading();
      console.log("error submit!!", errors);
    }
  });
};
const selectClick = (type) => {
  state.type = type;
  let options = [];

  if (type === "academyLevel") {
    options = Taro.getStorageSync("academyLevel");
    popupRadioRef.value.open(options, state.params.academicRating);
  }
  if (type === "accessLevel") {
    options = Taro.getStorageSync("accessLevel");
    popupRadioRef.value.open(options, state.params.accessRating);
  }

  if (type === "administrationLvl") {
    options = Taro.getStorageSync("administrationLvl");
    popupRadioRef.value.open(options, state.params.job);
  }

  if (type === "brandLevel") {
    options = Taro.getStorageSync("brandLevel");
    popupRadioRef.value.open(options, state.params.brandRating);
  }
  if (type === "retireStatus") {
    options = Taro.getStorageSync("retireStatus");
    popupRadioRef.value.open(options, state.params.isRehired);
  }

  if (type === "speakerType") {
    options = Taro.getStorageSync("speakerType");
    popupRadioRef.value.open(options, state.params.type);
  }
  if (type === "antiAgingRating") {
    options = Taro.getStorageSync("antiAgingRating");
    popupRadioRef.value.open(options, state.params.antiAgingRating);
  }
  if (type === "therapyArea") {
    options = Taro.getStorageSync("therapyArea");
    popupRadioRef.value.open(options, state.params.therapyArea);
  }

  if (type === "idCardType") {
    options = Taro.getStorageSync("idCardType");
    popupRadioRef.value.open(options, state.params.idCardType);
  }
  if (type === "sex") {
    popupRadioRef.value.open(state.sexList, state.params.sex);
  }
  if (type === "district") {
    options = Taro.getStorageSync("district");
    districtPopupRef.value.open([
    state.params.provinceCode,
      state.params.cityCode,
      state.params.districtCode,
    ], "ins");
  }

  if (type === "bankDistrict") {
    options = Taro.getStorageSync("district");
    districtPopupRef.value.open([ state.params.bankProvinceCode,
      state.params.bankCityCode,
      state.params.bankDistrictCode], "ins");
  }

};

const selectInsName = () => {
  selectInsRef.value.open()
};

const insConfirm = (ins) => {
  state.params.insName = ins.insName
  state.params.insCode = ins.code
}

const radioConfirm = (val) => {
  if (state.type === "academyLevel") {
    state.params.academicRating = val;

  }
  if (state.type === "accessLevel") {
    state.params.accessRating = val;
  }

  if (state.type === "administrationLvl") {
    state.params.job = val;

  }

  if (state.type === "brandLevel") {
    state.params.brandRating = val;

  }
  if (state.type === "retireStatus") {
    state.params.isRehired = val;
  }

  if (state.type === "speakerType") {
    state.params.type = val;

  }
  if (state.type === "antiAgingRating") {
    state.params.antiAgingRating = val;
  }
  if (state.type === "therapyArea") {
    state.params.therapyArea = val;
  }

  if (state.type === "idCardType") {
    const options = Taro.getStorageSync("idCardType");
    state.params.idCardName = options.filter(item => item.dictValue === val)[0].dictLabel
    state.params.idCardType = val;
  }
  if (state.type === "sex") {
    state.params.sexName = state.sexList.filter(item => item.dictValue === val)[0].dictLabel
    state.params.sex = val;
  }

};

const districtConfirm = (value, codeList) => {
  if(state.type ==='bankDistrict') {
    state.params.bankDistrict = value;
  state.params.bankProvinceCode = codeList[0] || "";
  state.params.bankCityCode = codeList[1] || "";
  state.params.bankDistrictCode = codeList[2] || "";
  } else {
    state.params.district = value;
  state.params.provinceCode = codeList[0] || "";
  state.params.cityCode = codeList[1] || "";
  state.params.districtCode = codeList[2] || "";
  }

};

const chooseImage = () =>
  new Promise((res, rej) => {
    try {
      tt.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (data) => {
          console.log(data, "--data");
          const fileSystemManager = tt.getFileSystemManager();
          fileSystemManager.readFile({
            filePath: data.tempFilePaths?.[0],
            encoding: "base64",
            success(res_v1) {
              const imageType = "image/png";
              const base64ImageURL = `data:${imageType};base64,${res_v1?.data}`;
              res(base64ImageURL);
            },
            fail(res) {
              console.log(`readFile fail: ${JSON.stringify(res)}`);
            },
          });
        },
        fail: (err) => rej(err),
      });
    } catch (err) {
      console.log("!ttt", err);
      Taro.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (data) => {
          console.log(data, "---dddd");
          res(data?.tempFilePaths?.[0]);
        },
        fail: (err) => rej(err),
      });
    }
  });
const takePhoto = async () => {
  try {
    const fileUrl = await chooseImage();
    Taro.showLoading({
      mask: true,
      title: "上传中",
    });

    Taro.uploadFile({
      url: state.url,
      header: {
        clientid: process.env.TARO_APP_CLIENT_ID,
        Authorization: `Bearer ${TokenKey}`,
      },
      filePath: fileUrl,
      name: "file",
      fail: () => {
        Taro.hideLoading();
        console.log("上传失败");
      },
      success: (result) => {
        Taro.hideLoading();
        try {
          const data = JSON.parse(result.data);
          console.log("data",data)
          state.params.fileList.push(Object.assign(data.data));
        } catch (e) {
          console.log(e);
        }
      },
    });
  } catch (err) {}
};
const handlePreview = (index) => {
  imgPreviewIndex.value = index;
  showImgPreview();
};
const handleDelFile = (e) => {
  const index = state.params.fileList.findIndex((d) => d.ossId === e.ossId);
  if (index > -1) {
    state.params.fileList.splice(index, 1);
  }
};

const hideImgPreview = () => {
  showPreview.value = false;
};

const showImgPreview = () => {
  showPreview.value = true;
};
const ocrChooseImage = (type) =>
  new Promise((res, rej) => {
    try {
      tt.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (data) => {
          console.log(data, "--data");
          const fileSystemManager = tt.getFileSystemManager();
          fileSystemManager.readFile({
            filePath: data.tempFilePaths?.[0],
            encoding: "base64",
            success(res_v1) {
              const imageType = "image/png";
              const base64ImageURL = `data:${imageType};base64,${res_v1?.data}`;
              res(base64ImageURL);
            },
            fail(res) {
              console.log(`readFile fail: ${JSON.stringify(res)}`);
            },
          });
        },
        fail: (err) => rej(err),
      });
    } catch (err) {
      console.log("!ttt", err);
      Taro.chooseImage({
        count: 1, // 默认为9，设置为1表示只能选择一张图片
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (data) => {
          convertLocalImageToBase64(data.tempFilePaths?.[0],type)
        },
        fail: (err) => rej(err),
      });
    }
  });


const convertLocalImageToBase64 = async (filePath,type) => {
  try {
    const response = await fetch(filePath);
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const blob = await response.blob();
    const base64String = await getBase64(blob);
    if(type==='card') {
      state.cardImageSrc = base64String;
    } else {
      state.bankImageSrc = base64String;
    }


   const base64Index = base64String.indexOf(';base64,') + ';base64,'.length;
  console.log('ssss',base64String.substring(base64Index))
  getOcrApi(base64String.substring(base64Index), type)
  } catch (error) {
    console.error('Error converting local image to base64:', error);
  }
};
const getOcrApi = async (url,type) => {
  try {
    // const res = type === 'card' ? await ocrCardApi({image_base64:url}) :await ocrBankApi({image_base64:url})
    Taro.showLoading({
      title: "智能识别中",
      icon: "loading",
    })
    const res = type === 'card' ? await ocrCardV2Api({
      base64: url,
      template: '1',
      imgType: 'jpg'
    }) :await ocrCardV2Api({
      base64: url,
      template: '3',
      imgType: 'jpg'
    });

    if(type === 'card') {
      if(res.success) {
        Taro.hideLoading();
        const idCardInfo = res.result;
        if(idCardInfo) {
          state.params.idCardNum = idCardInfo.IDNumber;
          state.params.idCardAddress = idCardInfo.Address;
        }

      }else {
        res.message && Taro.showToast({
          icon: 'none',
          title: res.message
        })
      }
    }else {
      if(res.success) {
        Taro.hideLoading();
        const bankCardInfo = res.result;
        if(bankCardInfo) {
          state.params.bankName = bankCardInfo.card_name;
          state.params.bankNum = bankCardInfo.card_number;
        }

      }else {
        res.message && Taro.showToast({
          icon: 'none',
          title: res.message
        })
      }
    }
  } catch (e) {
    Taro.hideLoading();
  }

}

const viewList = (list) => {
 list.forEach(async (item) => {
    const res = await imageUrl(item);
    state.params.fileList.push(res.data.rows[0]);
  });
};

const getProcess = async (type) => {
  try {
    const processRes = await processListApi({
      processKey: type === 'add' ? 'gs_speaker_mdm_apply' : 'gs_speaker_update_mdm_apply',
      processState: 1
    })
    console.log(processRes, 'processRes')
    processId.value = processRes?.rows?.[0]?.id

  } catch (e) {

  }
}
onMounted(() => {
  apiAuth();
  getProcess(query.type);
  if (query.type === "add") {
    Taro.setNavigationBarTitle({ title: "新增讲者申请" });
    state.params.insName = decodeURIComponent(query.insName);
    state.params.name = decodeURIComponent(query.name);
    state.params.institutionCode =query.institutionCode;
  } else {
    Taro.setNavigationBarTitle({ title: "变更讲者申请" });
    state.params = {
      ...JSON.parse(decodeURIComponent(query.info)),
    }
    console.log("state.params",state.params)
    state.params.institutionCode = state.params.insCode;

    state.params.sexName= state.sexList.filter(item => item.dictValue === state.params.sex)?.[0]?.dictLabel  || '';
    if(state.params.idCardType) {
      state.params.idCardName = Taro.getStorageSync("idCardType").filter(item => item.dictValue === state.params.idCardType)?.[0]?.dictLabel
    }
    // if(!state.params.district){
    //   // 获取省市区名称
    //   const {province, city,district} = state.params;
    //   // 根据实际值拼接显示
    //   state.params.district = [province, city, district]
    //     .filter(Boolean)  // 过滤掉空值
    //     .join('/');      // 用 / 连接
    // }
    // if(!state.params.bankDistrict){
    // // 获取省市区名称
    // const {bankProvince, bankCity,bankDistrict} = state.params;
    // // 根据实际值拼接显示
    // state.params.bankDistrict = [bankProvince, bankCity, bankDistrict]
    //   .filter(Boolean)  // 过滤掉空值
    //   .join('/');      // 用 / 连接
    // }
    if(state.params.fileList) {
      viewList(state.params.fileList)
    }
  }
});

</script>
<style lang="scss">
.ins-addIns {
  padding-bottom: 90px;

  .section {
    font-size: 14px;
    padding: 0 16px;
    background: #fff;

    .label {
      color: #1d212b;

      &::before {
        content: "*";
        color: red;
        vertical-align: middle;
        margin-right: 5px;
      }
    }

    .photos {
      width: 164px;
      height: 122px;
      background-color: #F3F4F5;
      text-align: center;
    }

    .photo {
      .nut-cell {
        display: block;
      }

      .nut-form-item__label.nut-cell__title {
        width: 100%;
      }

      .upload {
        margin-top: 8px;
        display: flex;

        .item-img {
          position: relative;
          margin-right: 8px;

          .file-close {
            width: 20px;
            height: 20px;
            position: absolute;
            top: 0;
            right: 0;
          }
        }

        .camera-img {
          width: 80px;
          height: 80px;
        }
      }
    }
  }

  .nut-cell__title {
    color: #1d212b;
  }

  .nut-input-left-box {
    margin-right: 10px;
  }

  .arrowIcon {
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }

  .location_ins {
    width: 24px;
    height: 24px;
  }

  .nut-cell-group__wrap {
    margin-top: 0;
    background: none;
  }

  .nut-cell__title {
    font-size: 16px;
  }

  // .nut-input__inner {
  //   white-space: pre-wrap !important;
  // }
  // .nut-input {
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-form-item__body__slots {
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-cell__value {
  //   display: block;
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
  // .nut-cell__value .nut-form-item__body {
  //   display: block;
  //   width: 59px;
  //   white-space: pre-wrap;
  //   word-break: break-all;
  // }
}
</style>
