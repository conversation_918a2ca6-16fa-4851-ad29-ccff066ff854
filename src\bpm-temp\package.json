{"name": "vue3-vant-mobile", "type": "module", "version": "3.1.1", "packageManager": "pnpm@9.12.1", "description": "Vue + Vite H5 Starter Template", "license": "MIT", "scripts": {"dev": "cross-env MOCK_SERVER_PORT=8086 vite", "build": "vite build", "build:dev": "vite build --mode=development", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "vitest", "release": "bumpp --commit --push --tag", "prepare": "husky"}, "dependencies": {"@form-create/vant": "^3", "@unhead/vue": "^1.11.7", "@vant/touch-emulator": "^1.4.0", "@vant/use": "^1.6.0", "@vueuse/core": "^11.1.0", "axios": "^1.7.7", "echarts": "^5.5.1", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.2.4", "pinia-plugin-persistedstate": "^4.1.1", "resize-detector": "^0.3.0", "vant": "^4.9.8", "vconsole": "^3.15.1", "vue": "^3.5.12", "vue-i18n": "^10.0.4", "vue-router": "^4.4.5"}, "devDependencies": {"@antfu/eslint-config": "3.7.3", "@iconify-json/carbon": "^1.2.3", "@intlify/unplugin-vue-i18n": "^5.2.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.7.5", "@types/nprogress": "^0.2.3", "@unocss/eslint-plugin": "0.63.4", "@unocss/preset-rem-to-px": "0.63.4", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.20", "bumpp": "^9.7.1", "consola": "^3.2.3", "cross-env": "^7.0.3", "eslint": "^9.12.0", "eslint-plugin-format": "^0.1.2", "husky": "^9.1.6", "less": "^4.2.0", "mockjs": "^1.1.0", "postcss-mobile-forever": "^4.1.6", "rollup": "^4.24.0", "terser": "^5.34.1", "typescript": "^5.6.3", "unocss": "0.63.4", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "unplugin-vue-router": "^0.10.8", "vite": "^5.4.8", "vite-plugin-mock-dev-server": "^1.8.0", "vite-plugin-pwa": "^0.20.5", "vite-plugin-sitemap": "^0.7.1", "vite-plugin-vconsole": "^2.1.1", "vite-plugin-vue-devtools": "^7.4.6", "vitest": "^2.1.2", "vue-tsc": "^2.1.6"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["postcss", "esbuild"], "allowedVersions": {"rollup": "^4.x"}, "allowAny": ["eslint"]}, "allowedDeprecatedVersions": {"glob": "7.2.3", "inflight": "1.0.6", "sourcemap-codec": "1.4.8"}}}