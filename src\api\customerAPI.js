// 肿瘤机构和客户API
import httpRequest from "@/servers/http";

//标准科室
export const deptNameList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=dept_name`);
};
//科室标签
export const departmentLabelList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=department_label`);
};

export const professionList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=profession_tech_title`);
};

export const administrationList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=administration_lvl`);
};

export const cusLevelList = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=cus_idea_level`);
};
export const cusPotenLevel = () => {
  return httpRequest.get(`/plt/dict/data/list?dictType=cus_poten_level`);
};

export const district = (params) => {
  return httpRequest.get(`/mdm/hcd/district`, params);
};
