export const finderParams = (data) => {
  return ({
    "use_app_cloud_id": true,
    "periods": [{
      "granularity": "day",
      "type": "past_range",
      "spans": [{"type": "timestamp", "timestamp": data.startTime}, {"type": "timestamp", "timestamp": data.endTime}],
      "timezone": "Asia/Shanghai",
      "week_start": 1
    }, {
      "granularity": "all",
      "type": "past_range",
      "spans": [{"type": "timestamp", "timestamp": data.startTime}, {"type": "timestamp", "timestamp": data.endTime}],
      "timezone": "Asia/Shanghai",
      "week_start": 1,
      "align_unit": "day",
      "skip_period": true
    }],
    "version": 3,
    "content": {
      "profile_groups_v2": [],
      "profile_filters": [{
        "show_name": `${data.empName}; ${data.empCode}`,
        "show_label": "1",
        "expression": {
          "logic": "and",
          "expressions": [{
            "logic": "or",
            "conditions": [{
              "property_type": "common_param",
              "property_name": "GS_empName",
              "property_compose_type": "origin",
              "property_operation": "=",
              "property_values": [data.empName]
            }]
          }, {
            "logic": "or",
            "conditions": [{
              "property_type": "common_param",
              "property_name": "GS_empNo",
              "property_compose_type": "origin",
              "property_operation": "=",
              "property_values": [data.empCode]
            }]
          }]
        }
      }],
      "orders": [],
      "query_type": "event",
      "queries": [[{
        "indicator_show_name": "总次数",
        "measure_info": {},
        "event_indicator": "events",
        "show_name": "任意主动事件",
        "show_label": "A",
        "event_name": "any_active_event",
        "event_type": "origin",
        "filters": [],
        "groups_v2": [{
          "property_compose_type": "origin",
          "property_name": "GS_empName",
          "property_type": "common_param"
        }, {
          "property_compose_type": "origin",
          "property_name": "GS_latitude",
          "property_type": "common_param",
          "number_group_type": 0
        }, {
          "property_compose_type": "origin",
          "property_name": "GS_empNo",
          "property_type": "common_param"
        }, {
          "property_compose_type": "origin",
          "property_name": "GS_longitude",
          "property_type": "common_param",
          "number_group_type": 0
        }, {
          "property_compose_type": "origin",
          "property_name": "$event_time",
          "property_type": "common_param",
          "datetime_group_type": 0,
          "datetime_group_granularity": "minute"
        }],
        "extra": {}
      }]],
      "page": {"limit": 200, "offset": 0},
      "option": {
        "refresh_cache": true,
        "fusion": false,
        "insight": {},
        "analysis_subject": {},
        "skip_period_restrict": false
      }
    },
    "option": {
      "blend": {"status": true, "base": 0, "base_period": true},
      "transpose": false,
      "finder": {"sort_rule": "avg_desc"}
    },
    "app_ids": [data.finderAppId],
    "show_option": {
      "__version": "3.2.0",
      "__uba_service_id": "event",
      "period": {
        "granularity": "day",
        "weekStart": 1,
        "beginTime": {"timestamp": data.startTime, "amount": 7, "unit": "day", "hour": 0, "tab": 1},
        "endTime": {"timestamp": data.startTime, "amount": 2, "unit": "day", "hour": 23, "tab": 1},
        "shortcut": "custom"
      },
      "tableTotalType": "average",
      "extra": {"is_pie": true, "scene": "event_query"},
      "scene": "common",
      "queryEvent": [{
        "uuid": "586e0f87-6c0f-4bae-a8b5-ab72826cf762",
        "label": "A",
        "event": {
          "event_type": "origin",
          "event_name": "any_active_event",
          "show_name": "任意主动事件",
          "event_id": 1895,
          "preset": true,
          "support_indicators": ["events", "event_users", "events_per_user", "uv_per_au", "pv_per_au", "sum", "avg", "per_user", "pct", "sum_per_au", "min", "max", "distinct", "distinct_user_attr"],
          "metric": {}
        },
        "indicator": {"value": {"indicator": {"indicator_type": "events"}, "label": "总次数"}}
      }],
      "eventCompute": [],
      "inlineCohort": [{
        "uuid": "922a8616-d713-4756-9975-e34721186ea2", "autoName": `${data.empName}; ${data.empCode}`, "customName": "", "conditions": {
          "logic": "and",
          "conditions": [{
            "uuid": "f5c3666a-6dc6-419b-bd48-a06612925464",
            "logic": "or",
            "conditions": [{
              "uuid": "71c93865-f868-4034-8afb-3c764b9d1a82",
              "inlineCohortType": "event_property",
              "event_property": {
                "uuid": "27d78c24-4036-4af7-8420-393b42a2aa3f",
                "filter": {
                  "property_compose_type": "origin",
                  "property_type": "common_param",
                  "name": "GS_empName",
                  "show_name": "",
                  "preset": false,
                  "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_not_contain_null", "contain", "not_contain", "not_contain_contain_null", "custom_contain", "match", "not_match"],
                  "value_type": "string",
                  "has_property_dict": false
                },
                "operation": {"label": "=", "alias": "", "tip": "=", "value": "="},
                "operationValues": [{"value": data.empName, "label": null}]
              }
            }]
          }, {
            "uuid": "f30956a1-da99-4925-91ae-013eddf9b9ad",
            "logic": "or",
            "conditions": [{
              "uuid": "4ba04881-fc37-45b7-a319-5a9d656ba6b4",
              "inlineCohortType": "event_property",
              "event_property": {
                "uuid": "ef392d70-e50e-4217-a073-f08e0be17e45",
                "filter": {
                  "property_compose_type": "origin",
                  "property_type": "common_param",
                  "name": "GS_empNo",
                  "show_name": "",
                  "preset": false,
                  "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_not_contain_null", "contain", "not_contain", "not_contain_contain_null", "custom_contain", "match", "not_match"],
                  "value_type": "string",
                  "has_property_dict": false
                },
                "operation": {"label": "=", "alias": "", "tip": "=", "value": "="},
                "operationValues": [{"value": data.empCode, "label": null}]
              }
            }]
          }]
        }
      }],
      "attrsGroup": [{
        "uuid": "d4751146-0f53-4d0b-aaa3-4ed2e9b497e3",
        "event_label": ["A"],
        "property": {
          "name": "GS_empName",
          "description": "",
          "preset": false,
          "property_type": "common_param",
          "property_compose_type": "origin",
          "show_name": "",
          "status": 1,
          "value_type": "string",
          "values": [],
          "has_property_dict": false,
          "create_time": 1725359498,
          "modify_time": 1726105269,
          "operation_type": "all_value",
          "sort_index": -1714,
          "used_times": 1714,
          "owners": [],
          "common_param": {"event_name": "any_event"},
          "property_type_for_fe": "common_param",
          "is_support_filter": true,
          "is_support_group_by": true,
          "is_support_input_text": true,
          "is_support_value_search": false,
          "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_not_contain_null", "contain", "not_contain", "not_contain_contain_null", "custom_contain", "match", "not_match"],
          "support_indicators": ["distinct", "distinct_user_attr"],
          "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
          "desensitization_type": null,
          "relative_events": []
        }
      }, {
        "uuid": "c0e1a109-ded9-486a-8dfa-e6a690215ac1",
        "event_label": ["A"],
        "property": {
          "name": "GS_latitude",
          "description": "",
          "preset": false,
          "property_type": "common_param",
          "property_compose_type": "origin",
          "show_name": "",
          "status": 1,
          "value_type": "float",
          "values": [],
          "has_property_dict": false,
          "create_time": 1725357778,
          "modify_time": 1725513192,
          "operation_type": "all_value",
          "sort_index": -1410,
          "used_times": 1410,
          "owners": [],
          "common_param": {"event_name": "any_event"},
          "property_type_for_fe": "common_param",
          "is_support_filter": true,
          "is_support_group_by": true,
          "is_support_input_text": true,
          "is_support_value_search": false,
          "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_contain_null", ">", ">=", "<", "<=", "between"],
          "support_indicators": ["sum", "avg", "per_user", "pct", "sum_per_au", "min", "max", "distinct", "distinct_user_attr"],
          "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
          "desensitization_type": null,
          "relative_events": []
        },
        "groupSetting": {"number_group_type": 0}
      }, {
        "uuid": "718fed30-74f2-4548-89ce-b62ac9ea1a82",
        "event_label": ["A"],
        "property": {
          "name": "GS_empNo",
          "description": "",
          "preset": false,
          "property_type": "common_param",
          "property_compose_type": "origin",
          "show_name": "",
          "status": 1,
          "value_type": "string",
          "values": [],
          "has_property_dict": false,
          "create_time": 1725356694,
          "modify_time": 1726109064,
          "operation_type": "all_value",
          "sort_index": -1510,
          "used_times": 1510,
          "owners": [],
          "common_param": {"event_name": "any_event"},
          "property_type_for_fe": "common_param",
          "is_support_filter": true,
          "is_support_group_by": true,
          "is_support_input_text": true,
          "is_support_value_search": false,
          "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_not_contain_null", "contain", "not_contain", "not_contain_contain_null", "custom_contain", "match", "not_match"],
          "support_indicators": ["distinct", "distinct_user_attr"],
          "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
          "desensitization_type": null,
          "relative_events": []
        }
      }, {
        "uuid": "f2447ff3-066b-44d3-99cb-b66c9110d251",
        "event_label": ["A"],
        "property": {
          "name": "GS_longitude",
          "description": "",
          "preset": false,
          "property_type": "common_param",
          "property_compose_type": "origin",
          "show_name": "",
          "status": 1,
          "value_type": "float",
          "values": [],
          "has_property_dict": false,
          "create_time": 1725357778,
          "modify_time": 1725514184,
          "operation_type": "all_value",
          "sort_index": -1410,
          "used_times": 1410,
          "owners": [],
          "common_param": {"event_name": "any_event"},
          "property_type_for_fe": "common_param",
          "is_support_filter": true,
          "is_support_group_by": true,
          "is_support_input_text": true,
          "is_support_value_search": false,
          "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_contain_null", ">", ">=", "<", "<=", "between"],
          "support_indicators": ["sum", "avg", "per_user", "pct", "sum_per_au", "min", "max", "distinct", "distinct_user_attr"],
          "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
          "desensitization_type": null,
          "relative_events": []
        },
        "groupSetting": {"number_group_type": 0}
      }, {
        "uuid": "d17aff19-12ea-431c-8767-1d04d097029a",
        "event_label": ["A"],
        "property": {
          "name": "$event_time",
          "description": null,
          "preset": true,
          "property_type": "common_param",
          "property_compose_type": "origin",
          "show_name": "事件发生时间",
          "status": 1,
          "value_type": "datetime",
          "values": [],
          "has_property_dict": false,
          "create_time": 1725354728,
          "modify_time": 1725354728,
          "operation_type": "all_value",
          "sort_index": -1396,
          "used_times": 1396,
          "owners": [],
          "common_param": {"event_name": "any_event"},
          "property_type_for_fe": "common_param",
          "is_support_filter": true,
          "is_support_group_by": true,
          "is_support_input_text": true,
          "is_support_value_search": false,
          "support_operations": ["is_null", "is_not_null", "time"],
          "support_indicators": ["distinct", "distinct_user_attr"],
          "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
          "desensitization_type": null,
          "relative_events": []
        },
        "granularitySetting": {"datetime_group_type": 0, "datetime_group_granularity": "minute"},
        "datetime_group_type": 0,
        "datetime_group_granularity": "minute"
      }],
      "chartConfiguration": {"resultSortOrder": "avg_desc"},
      "chartType": "line"
    }
  })
}

export const positionEventParams = (data) => ({
  "use_app_cloud_id": true,
  "periods": [{
    "granularity": "day",
    "type": "past_range",
    "spans": [{"type": "timestamp", "timestamp": data.startTime}, {"type": "timestamp", "timestamp": data.endTime}],
    "timezone": "Asia/Shanghai",
    "week_start": 1
  }, {
    "granularity": "all",
    "type": "past_range",
    "spans": [{"type": "timestamp", "timestamp": data.startTime}, {"type": "timestamp", "timestamp": data.endTime}],
    "timezone": "Asia/Shanghai",
    "week_start": 1,
    "align_unit": "day",
    "skip_period": true
  }],
  "version": 3,
  "content": {
    "profile_groups_v2": [],
    "profile_filters": [{
      "show_name": `${data.empCode}; ${data.empName}`,
      "show_label": "1",
      "expression": {
        "logic": "and",
        "expressions": [{
          "logic": "or",
          "conditions": [{
            "property_type": "common_param",
            "property_name": "GS_empNo",
            "property_compose_type": "origin",
            "property_operation": "=",
            "property_values": [data.empCode]
          }]
        }, {
          "logic": "or",
          "conditions": [{
            "property_type": "common_param",
            "property_name": "GS_empName",
            "property_compose_type": "origin",
            "property_operation": "=",
            "property_values": [data.empName]
          }]
        }]
      }
    }],
    "orders": [],
    "query_type": "event",
    "queries": [[{
      "indicator_show_name": "总次数",
      "measure_info": {},
      "event_indicator": "events",
      "show_name": "",
      "show_label": "A",
      "event_name": "position_event",
      "event_type": "origin",
      "filters": [],
      "groups_v2": [{
        "property_compose_type": "origin",
        "property_name": "GS_empName",
        "property_type": "common_param"
      }, {
        "property_compose_type": "origin",
        "property_name": "GS_latitude",
        "property_type": "common_param",
        "number_group_type": 0
      }, {
        "property_compose_type": "origin",
        "property_name": "GS_empNo",
        "property_type": "common_param"
      }, {
        "property_compose_type": "origin",
        "property_name": "GS_longitude",
        "property_type": "common_param",
        "number_group_type": 0
      }, {
        "property_compose_type": "origin",
        "property_name": "$event_time",
        "property_type": "common_param",
        "datetime_group_type": 0,
        "datetime_group_granularity": "minute"
      }],
      "extra": {}
    }]],
    "page": {"limit": 200, "offset": 0},
    "option": {
      "refresh_cache": true,
      "fusion": false,
      "insight": {},
      "analysis_subject": {},
      "skip_period_restrict": false
    }
  },
  "option": {
    "blend": {"status": true, "base": 0, "base_period": true},
    "transpose": false,
    "finder": {"sort_rule": "avg_desc"}
  },
  "app_ids": [data.finderAppId],
  "show_option": {
    "__version": "3.2.0",
    "__uba_service_id": "event",
    "period": {
      "granularity": "day",
      "weekStart": 1,
      "beginTime": {"timestamp": data.startTime, "amount": 7, "unit": "day", "hour": 0, "tab": 1},
      "endTime": {"timestamp": data.startTime, "amount": 2, "unit": "day", "hour": 23, "tab": 1},
      "shortcut": "custom"
    },
    "tableTotalType": "average",
    "extra": {"is_pie": true, "scene": "event_query"},
    "scene": "common",
    "queryEvent": [{
      "uuid": "586e0f87-6c0f-4bae-a8b5-ab72826cf762",
      "label": "A",
      "event": {
        "event_type": "origin",
        "event_name": "position_event",
        "show_name": "",
        "event_id": 1910,
        "preset": false,
        "support_indicators": ["events", "event_users", "events_per_user", "uv_per_au", "pv_per_au", "sum", "avg", "per_user", "pct", "sum_per_au", "min", "max", "distinct", "distinct_user_attr"],
        "metric": {}
      },
      "indicator": {"value": {"indicator": {"indicator_type": "events"}, "label": "总次数"}}
    }],
    "eventCompute": [],
    "inlineCohort": [{
      "uuid": "922a8616-d713-4756-9975-e34721186ea2",
      "autoName": `${data.empCode}; ${data.empName}`,
      "customName": "",
      "conditions": {
        "logic": "and",
        "conditions": [{
          "uuid": "381d34df-fe39-4f47-8f6e-92dd5a790d36",
          "logic": "or",
          "conditions": [{
            "uuid": "8d2cf221-ba41-4ae5-9ef5-1e985804d28d",
            "inlineCohortType": "event_property",
            "event_property": {
              "uuid": "128a7457-0e24-4d24-8a42-779740f34e63",
              "filter": {
                "property_compose_type": "origin",
                "property_type": "common_param",
                "name": "GS_empNo",
                "show_name": "",
                "preset": false,
                "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_not_contain_null", "contain", "not_contain", "not_contain_contain_null", "custom_contain", "match", "not_match"],
                "value_type": "string",
                "has_property_dict": false
              },
              "operation": {"label": "=", "alias": "", "tip": "=", "value": "="},
              "operationValues": [{"value": data.empCode, "label": null}]
            }
          }]
        }, {
          "uuid": "d0d5cb14-66a7-4108-958e-6c5f40b5715a",
          "logic": "or",
          "conditions": [{
            "uuid": "6d579adb-cb07-4287-824d-bb8dd79c9c84",
            "inlineCohortType": "event_property",
            "event_property": {
              "uuid": "59885d93-f4d4-460d-9819-9d0d9dad348f",
              "filter": {
                "property_compose_type": "origin",
                "property_type": "common_param",
                "name": "GS_empName",
                "show_name": "",
                "preset": false,
                "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_not_contain_null", "contain", "not_contain", "not_contain_contain_null", "custom_contain", "match", "not_match"],
                "value_type": "string",
                "has_property_dict": false
              },
              "operation": {"label": "=", "alias": "", "tip": "=", "value": "="},
              "operationValues": [{"value": data.empName, "label": null}]
            }
          }]
        }]
      }
    }],
    "attrsGroup": [{
      "uuid": "d4751146-0f53-4d0b-aaa3-4ed2e9b497e3",
      "event_label": ["A"],
      "property": {
        "name": "GS_empName",
        "description": "",
        "preset": false,
        "property_type": "common_param",
        "property_compose_type": "origin",
        "show_name": "",
        "status": 1,
        "value_type": "string",
        "values": [],
        "has_property_dict": false,
        "create_time": 1725359498,
        "modify_time": 1726105269,
        "operation_type": "all_value",
        "sort_index": -1714,
        "used_times": 1714,
        "owners": [],
        "common_param": {"event_name": "any_event"},
        "property_type_for_fe": "common_param",
        "is_support_filter": true,
        "is_support_group_by": true,
        "is_support_input_text": true,
        "is_support_value_search": false,
        "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_not_contain_null", "contain", "not_contain", "not_contain_contain_null", "custom_contain", "match", "not_match"],
        "support_indicators": ["distinct", "distinct_user_attr"],
        "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
        "desensitization_type": null,
        "relative_events": []
      }
    }, {
      "uuid": "c0e1a109-ded9-486a-8dfa-e6a690215ac1",
      "event_label": ["A"],
      "property": {
        "name": "GS_latitude",
        "description": "",
        "preset": false,
        "property_type": "common_param",
        "property_compose_type": "origin",
        "show_name": "",
        "status": 1,
        "value_type": "float",
        "values": [],
        "has_property_dict": false,
        "create_time": 1725357778,
        "modify_time": 1725513192,
        "operation_type": "all_value",
        "sort_index": -1410,
        "used_times": 1410,
        "owners": [],
        "common_param": {"event_name": "any_event"},
        "property_type_for_fe": "common_param",
        "is_support_filter": true,
        "is_support_group_by": true,
        "is_support_input_text": true,
        "is_support_value_search": false,
        "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_contain_null", ">", ">=", "<", "<=", "between"],
        "support_indicators": ["sum", "avg", "per_user", "pct", "sum_per_au", "min", "max", "distinct", "distinct_user_attr"],
        "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
        "desensitization_type": null,
        "relative_events": []
      },
      "groupSetting": {"number_group_type": 0}
    }, {
      "uuid": "718fed30-74f2-4548-89ce-b62ac9ea1a82",
      "event_label": ["A"],
      "property": {
        "name": "GS_empNo",
        "description": "",
        "preset": false,
        "property_type": "common_param",
        "property_compose_type": "origin",
        "show_name": "",
        "status": 1,
        "value_type": "string",
        "values": [],
        "has_property_dict": false,
        "create_time": 1725356694,
        "modify_time": 1726109064,
        "operation_type": "all_value",
        "sort_index": -1510,
        "used_times": 1510,
        "owners": [],
        "common_param": {"event_name": "any_event"},
        "property_type_for_fe": "common_param",
        "is_support_filter": true,
        "is_support_group_by": true,
        "is_support_input_text": true,
        "is_support_value_search": false,
        "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_not_contain_null", "contain", "not_contain", "not_contain_contain_null", "custom_contain", "match", "not_match"],
        "support_indicators": ["distinct", "distinct_user_attr"],
        "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
        "desensitization_type": null,
        "relative_events": []
      }
    }, {
      "uuid": "f2447ff3-066b-44d3-99cb-b66c9110d251",
      "event_label": ["A"],
      "property": {
        "name": "GS_longitude",
        "description": "",
        "preset": false,
        "property_type": "common_param",
        "property_compose_type": "origin",
        "show_name": "",
        "status": 1,
        "value_type": "float",
        "values": [],
        "has_property_dict": false,
        "create_time": 1725357778,
        "modify_time": 1725514184,
        "operation_type": "all_value",
        "sort_index": -1410,
        "used_times": 1410,
        "owners": [],
        "common_param": {"event_name": "any_event"},
        "property_type_for_fe": "common_param",
        "is_support_filter": true,
        "is_support_group_by": true,
        "is_support_input_text": true,
        "is_support_value_search": false,
        "support_operations": ["is_null", "is_not_null", "=", "!=", "not_equal_contain_null", ">", ">=", "<", "<=", "between"],
        "support_indicators": ["sum", "avg", "per_user", "pct", "sum_per_au", "min", "max", "distinct", "distinct_user_attr"],
        "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
        "desensitization_type": null,
        "relative_events": []
      },
      "groupSetting": {"number_group_type": 0}
    }, {
      "uuid": "d17aff19-12ea-431c-8767-1d04d097029a",
      "event_label": ["A"],
      "property": {
        "name": "$event_time",
        "description": null,
        "preset": true,
        "property_type": "common_param",
        "property_compose_type": "origin",
        "show_name": "事件发生时间",
        "status": 1,
        "value_type": "datetime",
        "values": [],
        "has_property_dict": false,
        "create_time": 1725354728,
        "modify_time": 1725354728,
        "operation_type": "all_value",
        "sort_index": -1396,
        "used_times": 1396,
        "owners": [],
        "common_param": {"event_name": "any_event"},
        "property_type_for_fe": "common_param",
        "is_support_filter": true,
        "is_support_group_by": true,
        "is_support_input_text": true,
        "is_support_value_search": false,
        "support_operations": ["is_null", "is_not_null", "time"],
        "support_indicators": ["distinct", "distinct_user_attr"],
        "support_event_types": ["origin", "virtual", "bav", "indicator", "metric"],
        "desensitization_type": null,
        "relative_events": []
      },
      "granularitySetting": {"datetime_group_type": 0, "datetime_group_granularity": "minute"},
      "datetime_group_type": 0,
      "datetime_group_granularity": "minute"
    }],
    "chartConfiguration": {"resultSortOrder": "avg_desc"},
    "chartType": "line"
  }
})
