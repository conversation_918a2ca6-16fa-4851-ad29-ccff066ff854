import Taro from "@tarojs/taro";
import getBaseUrl from "./baseUrl";
import interceptors from "./interceptors";
import { baseApi } from "@/utils/content";

interceptors.forEach((interceptorItem) => Taro.addInterceptor(interceptorItem));

const refactorApi = (api) => {
  return api.replace(/^\/msr\//, baseApi);
};

class httpRequest {
  baseOptions(params, method = "GET") {
    let { url, data } = params;
    const BASE_URL = process.env.TARO_APP_API;
    let contentType = "application/json";
    contentType = params.contentType || contentType;
    const regex = /^https:\/\/.*/;
    const option = {
      url: regex.test(url) ? url : BASE_URL + refactorApi(url),
      data: data,
      method: method,
      timeout: 60000,
      header: {
        "content-type": contentType,
        Authorization: `Bearer ${Taro.getStorageSync("access_token")}`,
        clientid: process.env.TARO_APP_CLIENT_ID,
      },
    };
    return Taro.request(option);
  }

  get(url, data = "") {
    let option = { url, data };
    return this.baseOptions(option);
  }

  post(url, data, contentType) {
    let params = { url, data, contentType };
    return this.baseOptions(params, "POST");
  }

  put(url, data = "") {
    let option = { url, data };
    return this.baseOptions(option, "PUT");
  }

  delete(url, data = "") {
    let option = { url, data };
    return this.baseOptions(option, "DELETE");
  }
}

export default new httpRequest();
