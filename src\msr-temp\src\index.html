<!DOCTYPE html>
<html>

<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
	<meta content="width=device-width,initial-scale=1,user-scalable=no" name="viewport" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="apple-touch-fullscreen" content="yes" />
	<meta name="format-detection" content="telephone=no,address=no" />
	<meta name="apple-mobile-web-app-status-bar-style" content="white" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <script src="https://cdn.staticfile.org/jquery/1.10.2/jquery.min.js"></script>
    <!-- 引入 JSSDK -->
    <!-- JS 文件版本在升级功能时地址会变化，如有需要（比如使用新增的 API），请重新引用「网页应用开发指南」中的JSSDK链接，确保你当前使用的JSSDK版本是最新的。-->
    <script
            type="text/javascript"
            src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.16.js"
    ></script>
	<title>标品CRM</title>
	<script>
      <%= htmlWebpackPlugin.options.script %>
	</script>
	<script>
			window._AMapSecurityConfig = {
			securityJsCode: "9b4c3dbff424030192879358eab1ece1",
      };
	</script>
	<!-- <script type="text/javascript">
      window._AMapSecurityConfig = {
        serviceHost: `${process.env.TARO_APP_API}/_AMapService`,
      };
    </script> -->
	<script type="text/javascript"
		src="https://webapi.amap.com/maps?v=1.4.15&key=bb5f9b6e971fa4ea858755323732d0fe"></script>
</head>

<body>
	<div id="app"></div>
</body>

</html>
