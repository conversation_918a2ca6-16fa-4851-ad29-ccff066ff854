<template>
  <view class="searchBar">
    <nut-searchbar :placeholder="props.placeholder" v-model="searchTxt">
      <template #leftin>
        <Search2 size="16px" />
      </template>
      <template #rightin>
        <view style="display: flex; flex-direction: row; align-items: center">
          <view class="searchFont" @click="search">搜索</view>
        </view>
      </template>
    </nut-searchbar>
    <view class="search-right" @click="insFilters" v-if="props.filters">
      <img :src="screen_2" alt="" v-if="!props.active" />
      <img :src="screen_3" alt="" v-else />
    </view>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import { Search2, Left, Photograph, Message, My } from "@nutui/icons-vue-taro";
import screen_2 from "@/images/screen_2.png";
import screen_3 from "@/images/screen_3.png";
const searchTxt = ref("");
const emit = defineEmits(["seacrh-top", "insFilters"]);
const props = defineProps({
  placeholder: {
    type: String,
    default: "",
  },
  active: {
    typeof: Boolean,
    default: false,
  },
  filters: {
    typeof: Boolean,
    default: true,
  },
});
const state = reactive({
  timer: null,
});

const debounce = (fn, wait) => {
  if (state.timer !== null) {
    clearTimeout(state.timer);
  }
  state.timer = setTimeout(fn, wait);
};
const search = () => {
  emit("seacrh-top", searchTxt.value);
};
const insFilters = () => {
  emit("filters");
};
</script>
<style lang="scss">
.searchBar {
  height: 48px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding: 0 16px;
  .split {
    width: 0px;
    height: 19px;
    border: 1px solid #e5e6eb;
  }

  .searchFont {
    height: 21px;
    font-weight: 400;
    font-size: 15px;
    color: #2551f2;
    line-height: 21px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-right: 8px;
    margin-left: 4px;
  }

  .nut-searchbar__search-input {
    height: 36px;
    border-radius: 20px 20px 20px 20px;
    border: 1px solid #1d212b;
    color: #869199;
    background: none;
  }

  .search-right {
    font-size: 14px;
    min-width: 40px;
    margin-left: 8px;

    img {
      width: 36px;
      height: 36px;
      vertical-align: middle;
    }
  }
  .nut-searchbar {
    // width: 87%;
    padding: 0;
  }
  .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    padding-right: 0;
  }
}
</style>
