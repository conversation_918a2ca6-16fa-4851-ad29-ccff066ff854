import httpRequest from "@/servers/http";

export const insLevel = (params) => {
  return httpRequest.get("/plt/dict/data/list?dictType=ins_level", params);
};
export const insGrade = (params) => {
  return httpRequest.get("/plt/dict/data/list?dictType=ins_grade", params);
};
export const insProfessionType = (params) => {
  return httpRequest.get(
    "/plt/dict/data/list?dictType=ins_profession_type",
    params
  );
};
export const economicType = (params) => {
  return httpRequest.get("/plt/dict/data/list?dictType=economic_type", params);
};

export const institutionApplyListApi = (params) => {
  return httpRequest.post("/msr/institution/apply-ins/list", params);
};

// 下机构详情
export const institutionDetail = (id) => {
  return httpRequest.get(`/msr/institution/apply-ins/${id}`);
};

// 新增机构-查询列表
export const crmApplyList = (pageNum, pageSize, params) => {
  return httpRequest.post(
    `/msr/institution/crm-is-apply-list?params=${pageNum}&pageSize=${pageSize}`,
    params
  );
};
