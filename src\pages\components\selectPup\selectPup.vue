<template>
  <view> 
    <nut-form>
<nut-form-item :label="props.label" :required="props.required">
<nut-input v-model="props.name" readonly  input-align="right" class="nut-input-text" :placeholder="props.placeholder"  @click="onClick">
<template #right><img :src="arrow" class="arrowIcon" v-if="props.arrowIcon"/></template>
</nut-input>
</nut-form-item>
</nut-form>
  </view>
</template>
<script setup>
import {onMounted, reactive,defineEmits,defineProps } from 'vue'
import arrow from '@/images/arrow.png'
const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  arrowIcon: {
    type: Boolean,
    default: true
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
})
const emit =defineEmits(['click-input']) 
const state = reactive({})

const onClick = () => {
emit('click-input')
}
</script>
<style lang="scss">
    .arrowIcon {
width: 16px;
height: 16px;
display: inline-block;
}
.nut-picker__right {
  color: #2551F2;
}
</style>
