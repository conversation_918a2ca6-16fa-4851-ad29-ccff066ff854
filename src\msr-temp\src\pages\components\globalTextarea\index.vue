<template>
  <view class="globa-textarea">
    <nut-textarea v-model="value[0].content" limit-show :max-length="100" />
  </view>
</template>
<script setup>
import { reactive } from 'vue';
const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
});
const state = reactive({});
</script>
<style lang="scss">
.globa-textarea {
  .nut-textarea {
    height: 65px;
    padding: 0;
    height: 88px;
  }

  textarea {
    left: -5px;
    z-index: 0;
    height: 88px;
  }
  .nut-textarea__textarea {
    padding: 16px;
  }
  textarea[disabled] {
    color: #1d1e1e !important;
  }
}
</style>
