<template>
  <!-- 产品信息选择 -->
  <view class="selectProducts">
    <TopSearch
      fixed="fixed"
      @top-search="search"
      placeholder="请输入产品名称"
    />
    <view class="contentMargin27">
      <view
        class="pro-content"
        v-for="item in state.productList"
        :key="item.productCode"
      >
        <view
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: #fff;
          "
          @click="clickVisit(item.productCode)"
        >
          <view class="productName">{{ item.productName }}</view>
          <img
            :src="!item.infoIconFlag ? arrowDown : arrowUp"
            alt=""
            v-if="item?.productInfoList.length != 0"
            style="width: 13px; height: 13px; padding-left: 10px"
          />
        </view>
        <view
          v-if="item.infoIconFlag && item?.productInfoList.length != 0"
          class="pro-item"
        >
          <nut-checkbox-group
            ref="group"
            v-model="item.checkboxed"
            @change="change2"
          >
            <nut-checkbox
              v-for="j in item.productInfoList"
              :key="j.infoId"
              :label="j.infoId"
              text-position="left"
            >
              <template #icon>
                <img :src="icon" alt="" class="radioIcon" />
              </template>
              <template #checkedIcon>
                <img :src="checkedIcon" alt="" class="radioIcon" />
              </template>
              {{ j.info }}
            </nut-checkbox>
          </nut-checkbox-group>

          <view> </view>
        </view>
      </view>
    </view>

    <SelectProductsPop ref="selectProductsPopRef" />

    <view class="footerButton">
      <view class="flex justify-between items-center px-[20px]">
        <view @tap="openChecked()">已选</view>
        <view>
          <nut-button type="info">确定</nut-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import Taro, { useRouter } from "@tarojs/taro";
import {
  defineEmits,
  defineProps,
  nextTick,
  onMounted,
  reactive,
  ref,
} from "vue";
import { institutionProducts } from "../../../api/institutionalVisitsApi.js";
import arrowDown from "../../../images/arrow-down.png";
import arrowUp from "../../../images/up.png";
import icon from "../../../images/radio-icon1.png";
import checkedIcon from "../../../images/radio-icon2.png";
import TopSearch from "../../../pages/components/topSearch/topSearch.vue";
import SelectProductsPop from './selectProductsPop';

const router = useRouter();
const query = router.params;
const initHrInfo = Taro.getStorageSync("initHrInfo");

const props = defineProps({
  institutionObject: {
    type: Object,
    default: () => {
      return {};
    },
  },
  customerObject: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const emit = defineEmits(["button-product"]);
const state = reactive({
  leftTetx: "产品信息选择",
  text: "确认",
  checkedResult: [],
  params: {
    insCode: "",
    jurCodeList: initHrInfo.jurCodeList,
    selfJurCodeList: initHrInfo.selfJurCodeList,
    subJurCodeList: initHrInfo.subJurCodeList,
    productName: "",
  },
  productList: [],
});
const group = ref(null);
const selectProductsPopRef  = ref();

const clickVisit = (code) => {
  state.productList.forEach((item) => {
    if (item.productCode === code) {
      item.infoIconFlag = !item.infoIconFlag;
    }
  });
};
const search = (name) => getInstitutionProducts(name);
const getSelectedProductInfos = (instproductList) => {
  instproductList.forEach((product) => {
    const checkedInfoIds = new Set(product.checkboxed);
    const productInfoList = product.productInfoList;

    const filteredProductInfoList = productInfoList.filter((info) =>
      checkedInfoIds.has(info.infoId)
    );
    product.productInfoList = filteredProductInfoList;
  });
  return instproductList;
};

const clickButton = () => {
  const list = state.productList.filter(
    (item) => item.checkboxed.length
  );
  if (!list.length) {
    Taro.showToast({
      title: "请至少选择一个产品添加",
      icon: "none",
    });
    return;
  } else {
    state.productList = state.productList.filter(
      (item) => item.checkboxed.length
    );
    const selectePro = getSelectedProductInfos(state.productList);
    Taro.setStorage({ key: "selectePro", data: selectePro });
    Taro.navigateBack({
      delta: 1,
    });
  }
};

const getInstitutionProducts = async (name = "") => {
  state.params.productName = name;
  Taro.showLoading({
    mask: true,
    title: "搜索中",
  });
  const res = await institutionProducts(state.params).catch((err) =>
    Taro.hideLoading()
  );
  Taro.hideLoading();

  state.productList = res.data.map((item, index) => ({
    ...item,
    infoIconFlag: index == 0,
    checkboxed: [],
    productInfoList: item.productInfoList.map((j) => ({
      ...j,
      cusProdRespCode: "",
    })),
  }));
  console.log("wssss", state.productList);
  const list = Taro.getStorageSync("setPro");
  if (list.length) {
    state.productList.forEach((z) => {
      list.forEach((item) => {
        z.productInfoList.forEach((a) => {
          item.productInfoList.forEach((b) => {
            if (a.infoId === b.infoId) {
              a.cusProdRespCode = b.cusProdRespCode;
            }
          });
        });
        if (z.productCode === item.productCode) {
          z.checkboxed = item.productInfoList.map((i) => i.infoId);
        }
      });
    });

    console.log("处理过的", state.productList);
    // Taro.removeStorageSync("setPro");
  }
};

const openChecked = () => {
  selectProductsPopRef.value?.open();
}

onMounted(() => {
  Taro.setNavigationBarTitle({ title: "选择产品" });
  state.params.insCode = query.insCode;
  getInstitutionProducts();
});
</script>

<style lang="scss">
.search-box {
}
.contentMargin27 {
  margin-top: 16.5px;
}
.productName {
  //styleName: 16;
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 400;
  // line-height: 22.4px;
  // text-align: left;
}
.selectProducts {
  padding-bottom: 75px;
  .pro-content {
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.04);
    margin-bottom: 1px;
    .pro-item {
      padding: 7px 24px;
      background: #f5f5f5;
    }
    .nut-checkbox--reverse .nut-checkbox__label {
      color: #869199;
      font-size: 12px;
    }
  }
  .radioIcon {
    width: 20px;
    height: 20px;
    display: block;
  }
}

.footerButton {
  position: fixed;
  bottom: 0;
  background-color: #ffffff;
  width: 100%;
  padding-top: 8px;
  padding-bottom: 34px;
  .nut-button {
    border-radius: 4px;
  }
  .border-none {
    border: none;
  }
  .nut-button__wrap {
    img {
      width: 10px;
      height: 10px;
    }
  }
}
</style>
