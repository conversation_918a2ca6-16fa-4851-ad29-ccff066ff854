<template>
  <view
      style="
        position: fixed;
        height: 56px;
        text-align: right;
        bottom: 80px;
        right: 16px;
      "
      @click="handleClick"
    >
      <view class="oval">
       <view style="margin-right: 8px;"><IconFont name="uploader" color="#fff" size="12px"  style="display: block;"></IconFont></view>

        <view style="color: #fff; font-size: 15px;">{{props.title}}</view>
      </view>
    </view>
</template>
<script setup>
import { IconFont } from "@nutui/icons-vue-taro";

const props = defineProps({
  title:{
   type: String,
   default:''
  }

})
const emit = defineEmits("click-oval")
const handleClick = () => {
  emit("click-oval")
}
</script>
<style>
  .oval {
    display: flex;
   align-items: center;
   justify-content:center ;
    width: 82px;
    height: 41px;
    background: linear-gradient( 144deg, #597DFF 0%, #2551F2 100%);
    box-shadow: 0px 2px 4px 0px rgba(37,81,242,0.45);
    border-radius: 100px 100px 100px 100px;
  }
</style>