<template>
  <view class="insFilters">
    <CellClick
      @selectClick="selectClick('time')"
      :value="state.params.startDate + '-' + state.params.endDate"
      label="选择时间"
      placeholder="请选择"
      :phRight="true"
    />
    <!-- <CellClick
      @selectClick="selectClick('type')"
      :value="state.params.type"
      label="申请类型"
      placeholder="请选择"
      :phRight="true"
    /> -->
  </view>
  <Calendar ref="calendarRef" @chage-time="chageTime" />
  <PopupRadio ref="popupRadioRef" @radio-confirm="radioConfirm" />
  <FooterButtonTwo
    rightText="确定"
    @click-left="clickButtonLeft"
    @click-right="clickButtonRight"
    leftText="重置"
    :plainFlag="false"
    leftColor=""
    leftTextColor="#4E595E"
  />
</template>
<script setup>
import { onMounted, reactive, ref } from "vue";
import Taro, { useRouter } from "@tarojs/taro";
import CellClick from "../../../pages/components/cellClick/index.vue";
import FooterButtonTwo from "../../../pages/components/footerButtonTwo/footerButtonTwo";
import Calendar from "../../../pages/components/nutCalendar/index.vue";
import PopupRadio from "../../../pages/components/popupRadio/popupRadio";
const districtPopupRef = ref(null);
const router = useRouter();
const query = router.params;
const calendarRef = ref(null);
const popupRadioRef = ref(null);
const state = reactive({
  params: {
    startDate: null,
    endDate: null,
  },
});

const selectClick = (type) => {
  if (type === "time") {
    calendarRef.value.open(state.params.startDate, state.params.endDate);
  }
  if (type === "type") {
    const options = Taro.getStorageSync("approveType");
    popupRadioRef.value.open(options, state.params.type);
  }
};
const chageTime = (start, end) => {
  state.params.startDate = start;
  state.params.endDate = end;
};
const radioConfirm = () => {};

const clickButtonLeft = () => {
  state.params = {
    startDate: "",
    endDate: "",
  };
};
const clickButtonRight = () => {
  Taro.setStorage({ key: "approveFilters", data: state.params });
  Taro.navigateBack({
    delta: 1,
  });
};

onMounted(() => {
  Taro.setNavigationBarTitle({ title: "筛选" });
  const infoString = query.info;
  let infoObject;
  try {
    const decodedInfo = decodeURIComponent(infoString);
    infoObject = JSON.parse(decodedInfo);
    state.params = {
      startDate: decodeURIComponent(infoObject.startDate),
      endDate: decodeURIComponent(infoObject.endDate),
    };
  } catch (error) {}
});
</script>
<style lang="scss">
.insFilters {
  padding: 0 16px;
  background: #fff;
}
</style>
