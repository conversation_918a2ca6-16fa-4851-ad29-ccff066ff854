import Taro from "@tarojs/taro";
import {
  deptNameList,
  professionList,
  administrationList,
  cusLevelList,
  district,
  cusPotenLevel,
  departmentLabelList,
} from "../api/customerAPI.js";

export async function getDepartmentLabelList() {
  const { rows } = await departmentLabelList();
  Taro.setStorage({
    key: "departmentLabelList",
    data: rows,
  });
}

export async function getDeptNameList() {
  const { rows } = await deptNameList();
  Taro.setStorage({
    key: "deptNameList",
    data: rows,
  });
}
export async function getProfessionList() {
  const { rows } = await professionList();
  Taro.setStorage({
    key: "professionList",
    data: rows,
  });
}
export async function geAdministrationList() {
  const { rows } = await administrationList();
  Taro.setStorage({
    key: "administrationList",
    data: rows,
  });
}

export async function getCusLevelList() {
  const { rows } = await cusLevelList();
  Taro.setStorage({
    key: "cusLevelList",
    data: rows,
  });
}
export async function getCusPotenLevel() {
  const { rows } = await cusPotenLevel();
  Taro.setStorage({
    key: "cusPotenLevelList",
    data: rows,
  });
}

export async function getDistrict() {
  const { data } = await district();

  data.forEach((item) => {
    item.children.unshift({ code: "", value: "全部" });
    if (item.children && item.children.length) {
      item.children.forEach((i) => {
        if (i.children && item.children.length) {
          i.children.unshift({ code: "", value: "全部" });
        }
      });
    }
  });

  Taro.setStorage({
    key: "districtList",
    data: data,
  });
}

export const formatter = (value) => {
  return value.replace(/^\s+|\s+$/, "");
};
