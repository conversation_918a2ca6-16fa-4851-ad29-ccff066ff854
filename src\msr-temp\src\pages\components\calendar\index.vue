<template>
  <view class="calendar">
    <vue-hash-calendar
      ref="calendar"
      :show-not-current-month-day="false"
      :mark-date="props.dateList"
      picker-type="date"
      @change="onChange"
      @touchstart="touchstart"
      @slidechange="slidechange"
    >
      <template v-slot:day="scope">
        <view v-if="scope?.extendAttr?.isToday">今</view>
        <view v-else-if="!scope?.extendAttr?.isCurrentMonthDay"></view>
        <view v-else>{{ scope?.date?.day }}</view>
      </template>
      <template #action>
        <!-- <img  @tap="lastMonth" :src="arrowLeft" alt="" class="arrowRight"> -->
        <view class="title">{{ state.getTitie }}月</view>
        <!-- <img @tap="nextMonth"  :src="arrowRight" alt="" class="arrowleft"/> -->
      </template>
      <!-- <template #arrow="scope">
      <view><img src="../../../images/calendar-line.png" alt="" class="calendar-line" /></view>
      </template> -->
    </vue-hash-calendar>
  </view>
</template>
<script setup>
import VueHashCalendar from "vue3-hash-calendar";
import "vue3-hash-calendar/es/index.css";
import { defineEmits, defineProps, reactive, ref } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import arrowRight from "../../../images/arrow-right.png";
import arrowLeft from "../../../images/arrow-left.png";
const props = defineProps({
  dateList: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(["click-date", "change-date"]);
const calendar = ref(null);
const state = reactive({
  getTitie: null,
  dateList: [
    { color: "#b3b3b3", date: [] },
    { color: "#fff", type: "dot", date: [] },
  ],
});
const onChange = (date) => {
  state.getTitie = dayjs(date).format("YYYY M");
  emit("change-date", date);
};

const lastMonth = (e) => {
  calendar.value.lastMonth();
};
const nextMonth = () => {
  calendar.value.nextMonth();
};
const touchstart = (e) => {
  //   e.defaultPrevented()
  //  e.end = true
  //  e.defaultPrevented = true
  //   console.log("touchstart",e)
};

const slidechange = (direction) => {
  console.log(direction, "434444");
};
</script>

<style lang="scss">
.calendar {
  width: 100vw;
  .title {
    font-size: 14px;
  }
  .calendar-line {
    width: 18px;
    height: 2px;
    flex-shrink: 0;
    background-color: #999;
  }
}
.arrowRight {
  width: 18px;
  height: 18px;
}
.arrowleft {
  width: 22px;
  height: 22px;
}
.hash-calendar.calendar_inline {
  z-index: 0;
  background: #fff;
}
.hash-calendar .calendar_title {
  background: #fff;
  border-bottom: none;
  height: 44px;
  justify-content: space-around;
}
.hash-calendar .calendar_day_checked {
  background: #2551f2;
}
.hash-calendar .calendar_dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  bottom: 5px;
  left: 26px;
}
.hash-calendar .ctrl-img {
  background-color: #ffffff;
}
//  .calendar_content {
//   height: 200px;
// }
.hash-calendar .calendar_day {
  border-radius: 3px;
}
.calendar_group_li {
  // transform:translate3d(0px, 0px, 0px) !important
}
.hash-calendar .calendar_body {
  width: 100vw;
}
.hash-calendar .calendar_content {
  height: 32vh;
}
</style>
