<template>
  <view class="insDeatil">
    <view class="section">
      <view class="cus-information">
        <view class="information-name">
          <view style="display: flex; align-items: center">
            <view
              style="
                margin-right: 8px;
                color: #1d212b;
                font-size: 20px;
                font-weight: 500;
              "
              >{{ state.info.insName }}
              <nut-tag
                plain
                color="#94BFFF"
                text-color="#2551F2"
                style="vertical-align: text-bottom"
              >
                {{ state.info.insMdmCode }}
              </nut-tag></view
            >
          </view>
        </view>
        <view
          style="margin-top: 8px"
          v-if="state.info.insGrade || state.info.insLevel"
        >
          <nut-tag
            plain
            :color="getInsLevelColor(state.info.insGrade)"
            :text-color="getInsLevelTextColor(state.info.insGrade)"
            style="margin-right: 8px; vertical-align: text-top"
          >
            {{ state.info.insGrade }}{{ state.info.insLevel }}
          </nut-tag>
        </view>
        <view
          style="color: #869199; font-size: 14px; margin-top: 5px"
          v-if="state.info.wbId"
        >
          网报编码：{{ state.info.wbId }}
        </view>
        <view
          style="color: #869199; font-size: 14px; margin-top: 5px"
          v-if="state.info.mgr && state.info.mgr.length > 0"
        >
          代表：{{ state.info.mgr.map((item) => item.value).join("、") }}
        </view>
      </view>
    </view>
    <InsTabDetail ref="insDetailRef" />
    <FooterButton
      text="编辑产品"
      :plain="true"
      textColor="#2551f2"
      @click-button="clickButton"
      v-if="sysJuInsObj.settingStatus === '1'"
    >
      <template #icon>
        <Edit color="#2551f2" size="14px" />
      </template>
    </FooterButton>
    <view class="insTabDetail" v-if="appCode === 'athena_ade' || appCode === 'athena_ais'">
      <view style="font-size: 16px; color: #1d212b">机构潜力</view>
      <view class="cusDetail-item">
        <view class="cusDetail-item-title">
          <view class="value">
            <view
              style="margin-bottom: 8px; color: #1d212b; font-size: 14px"
            >
              <span style="color: #869199">是否TOP500：</span
              >{{ state?.potentialData?.["是否TOP500"] }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import arrowRight from "../../../images/arrow-right.png";
import arrowRight2 from "../../../images/arrow-right_2.png";
import vector from "../../../images/woman.png";
import man from "../../../images/man.png";
import cus_ins from "../../../images/cus_ins.png";
import { Edit } from "@nutui/icons-vue-taro";
import InsTabDetail from "../components/insTabDetail.vue";
import { institutionDetail } from "../../../api/area.js";
import {
  getInsLevelColor,
  getInsLevelTextColor,
  getPostCode,
} from "../../../utils/area.js";
import { baseApi } from "../../../utils/content";
import { appCode } from "@/utils/content";
import { checkDept, getLabel } from "../../institutionalVisits048/api.js";

const ISGEN = ["/gen/"].includes(baseApi);
const sysJuInsObj = Taro.getStorageSync("sysJuInsObj");
const deptCode = Taro.getStorageSync("initHrInfo")?.orgList?.[0]?.deptCode;

const router = useRouter();
const query = router.params;
const insDetailRef = ref(null);
const state = reactive({
  activekeys: "1",
  id: query.id,
  info: {},
  postUseName: "",
  list: [],
  showFooterPopupVisible: false,
  potentialVisible: false,
  potentialData: {},
});

const getDetail = async () => {
  const params = {
    jurCode: query.jurCode,
    insCode: query.insCode,
  };
  if (query.claiming === "1") {
    params.isClaim = "0";
  } else {
    params.isClaim = "1";
  }
  const res = await institutionDetail(params);
  state.info = res.data;
  insDetailRef.value.setInfo(res.data);
};

const getPotential = async () => {
  try {
    const deptRes = await checkDept(deptCode);
    if (deptRes.data === true) {
      state.potentialVisible = true;
      const labelRes = await getLabel({ type: "ins", code: query.insCode });
      state.potentialData = labelRes.data || {};
    } else {
      state.potentialVisible = false;
    }
  } catch (e) {
    state.potentialVisible = false;
  }
};

const changeTab = (val) => {
  console.log(val);
};
const clickButton = () => {
  Taro.setStorage({ key: "detailInfo", data: state.info });
  const initHrInfo = Taro.getStorageSync("initHrInfo");

  const org = initHrInfo.orgList?.filter((item) => item.postType === "1")[0];

  Taro.navigateTo({
    url: `/pages/area/components/editProduct?type=ins`,
  });
};

useDidShow(async () => {
  Taro.setNavigationBarTitle({ title: "机构详情" });
  await getDetail();
  await getPotential();
});
</script>

<style lang="scss">
.insDeatil {
  padding-bottom: 93px;

  .section {
    font-size: 14px;
    color: #869199;
    background: #fff;

    padding: 16px;

    .cus-information {
      padding: 16px;
      background: #f4f5f7;
      background-image: url("../../../images/card-bg.png");
      background-size: 100% 100%;

      .information-name {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .sexIcon {
          display: inline-block;
          margin-right: 5px;
          width: 18px;
          height: 18px;
          vertical-align: text-bottom;
        }
      }

      .hosType {
        height: 22px;
        border-radius: 2px 2px 2px 2px;
        border: 1px solid #94bfff;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #2551f2;
        line-height: 22px;
        padding: 0 4px;
        margin-right: 4px;
      }

      .insIcon {
        display: inline-block;
        margin-right: 6px;
        width: 18px;
        height: 18px;
        vertical-align: text-bottom;
      }
    }
  }

  .tabs-box {
    margin-bottom: 20px;

    .tabs-box-quest-list-item {
      background-color: #fff;
      margin-bottom: 12px;
      padding-left: 16px;
      padding-top: 12px;
      border-radius: 4px;
    }
  }

  .nut-tab-pane {
    padding: 0;
    background: none;
    padding: 12px;
  }

  .title {
    width: max-content;
    height: 24px;
    //background: #E8F0FF;
    border-radius: 2px 2px 2px 2px;
    //padding: 4px 6px 3px;
    display: flex;
    justify-content: center;
    align-self: center;

    .font {
      height: 17px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 12px;
      color: #2551f2;
      line-height: 17px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-left: 6px;
      margin-right: 6px;
      display: flex;
      justify-content: center;
      align-self: center;
    }
  }
}
</style>
