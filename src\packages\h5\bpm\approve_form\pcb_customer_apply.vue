<template>
  <view class="bg-white px-[16px]">
    <view>
      <view class="text-[#1D212B] text-[14px] py-[10px]">基本信息</view>
      <view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >部门</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.ancestors
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >岗位</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.postName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >客户名称</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.custName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >机构名称</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >机构主数据编码</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insMdmCode
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >是否主执业点</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.masterFlag
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >标准科室</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insDeptName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >职称</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.professionTechTitle
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >行政级别</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.administrationLevel
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >性别</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.sex
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >备注</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.remark
          }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { onMounted, reactive, ref } from "vue";
const props = defineProps(["formData"]);
</script>
<style lang="scss"></style>
