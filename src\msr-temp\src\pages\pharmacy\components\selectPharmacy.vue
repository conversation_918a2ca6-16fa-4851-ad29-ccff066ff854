<template>
  <view class="selectPharmacy">
    <GlobalPopup ref="popupRef">
      <TopSearch
        @top-search="search"
        placeholder="请输入药店名称"
        ref="topSearchRef"
      />
      <scroll-view
        style="height: 400px"
        :scroll-y="true"
        :scroll-top="0"
        @scrolltolower="scrollToLower"
      >
        <view v-if="!state.flag && !state.insList.length" class="result-loading"
          >搜索暂无数据</view
        >
        <nut-radio-group v-model="state.radioChecked" text-position="left">
          <GlobalNutRadio
            v-for="item in state.insList"
            :value="item.id"
            :key="item.id"
          >
            <template #name>
              <view>
                {{ item.name }}
              </view>
            </template>
          </GlobalNutRadio>
        </nut-radio-group>
      </scroll-view>

      <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { reactive, ref } from "vue";
import { SuperiorDrugstoreList } from "../../../api/pharmacy.js";
import TopSearch from "../../../pages/components/topSearch/topSearch.vue";
import GlobalPopup from "../../../pages/components/globalPopup/globalPopup.vue";
import GlobalNutRadio from "../../../pages/components/globalNutRadio/globalNutRadio.vue";
const emit = defineEmits(["confirm"]);
const popupRef = ref(null);
const state = reactive({
  insList: [],
  radioChecked: "",
  params: {
    dsName: "",
    pageSize: 10,
    pageNum: 1,
  },
  flag: false,
  total: 0,
});

const search = (name) => {
  state.params.dsName = name;
  state.params.pageNum = 1;
  state.insList = [];
  getInstitutionList();
};

const getInstitutionList = async () => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  state.flag = true;
  const res = await SuperiorDrugstoreList(state.params);
  if (!state.insList) {
    state.insList = res.rows;
  } else {
    state.insList = [...state.insList, ...res.rows];
  }
  state.total = res.total;
  state.flag = false;
  Taro.hideLoading();
};

const scrollToLower = () => {
  if (!state.flag && state.insList.length < state.total) {
    state.params.pageNum++;
    getInstitutionList();
  }
};
const confirm = () => {
  const ins = state.insList.filter((item) => item.id === state.radioChecked)[0];
  emit("confirm", ins);
  popupRef.value.close();
};

const open = () => {
  state.insList = [];
  state.params.dsName = "";
  popupRef.value.open();

  getInstitutionList();
};
defineExpose({
  open,
});
</script>
<style lang="scss">
.selectPharmacy {
  // background: #f4f5f7;
  .result-loading {
    color: #869199;
    font-size: 12px;
    text-align: center;
    margin-top: 16px;
  }
  .searchBar .nut-searchbar {
    width: 100%;
  }
  .nut-radio-group {
    padding: 0 16px;
  }
  .nut-radio {
    background: #fff;
  }

  .footer-comfirm {
    height: 40px;

    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    padding-right: 0;
  }
}
</style>
